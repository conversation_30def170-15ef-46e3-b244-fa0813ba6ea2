<template>
    <view class="zh-wrapper">
        <scroll-view :scroll-into-view="menuScrollIntoView" class="menus" scroll-with-animation scroll-y>
            <view class="wrapper">
                <view v-for="(item, index) in goods" :id="`menu-${item.id}`" :key="index"
                      class="menu"  :class="{ 'current': item.id == curCateId }" @tap="handleMenuTap(item.id)">
                    <text>{{ item.name }}</text>

                </view>
            </view>
        </scroll-view>
        <scroll-view :scroll-top="cateScrollTop" class="goods" scroll-with-animation scroll-y
                     @scroll="handleGoodsScroll">
            <view class="wrapper">
                <view class="list">
                    <view v-for="(item, index) in goods" :id="`cate-${item.id}`" :key="index" class="category">
                        <slot :data="item" name="goods">
<!--                            <view class="title">-->
<!--                                <text>{{ item.name }}</text>-->
<!--                            </view>-->
                            <view class="items">
                                <view v-for="(good, key) in item.childs" :key="key" >
                                    <view class="good">
                                        <view class="right" @click="toList(good)">
                                            <text class="name">{{ good.name }}</text>
                                            <view class="desc">更多</view>
                                        </view>
                                    </view>
                                    <goodItem :categoryId="good.id"></goodItem>
                                </view>
                            </view>
                        </slot>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script lang="ts" setup>
import goodItem from './GoodListItem.vue'
import {getCurrentInstance, nextTick, ref, watch} from "vue";
import type {ICateItem} from './goods.model';

const props = defineProps<{
    scrollList: ICateItem[]
}>()
const instance = getCurrentInstance();
const menuScrollIntoView = ref("");
const curCateId = ref(1);
const cateScrollTop = ref(0);
// 增加默认选中状态
curCateId.value = props.scrollList[0].id;
// 计算高度状态
const sizeCalcState = ref(false);
const goods = ref<ICateItem[]>([]);
watch(
    () => props.scrollList,
    newVal => {
        goods.value = newVal;
        nextTick(() => {
            if (newVal && newVal.length > 0) {
                calcSize();
            }
        })
    },
    {
        immediate: true,
        deep: true
    }
)

// 点击左侧菜单
function handleMenuTap(id: number) {
    if (!sizeCalcState.value) {
        calcSize()
    }
    curCateId.value = id
    nextTick(() => {
        cateScrollTop.value = goods.value.find(item => item.id == id)?.top as number;
    })
}

//
function handleGoodsScroll(e: any) {
    if (!sizeCalcState.value) {
        calcSize()
    }
    const {scrollTop} = e.detail
    const chickItem = goods.value.filter(item => item.id == curCateId.value)[0]
    if (chickItem?.top< scrollTop) {
        // 此处scrollTop + 1为了处理scrolltop的偏差值
        let tabs = goods.value.filter(item => item.top <= (scrollTop + 1))
        if (tabs.length > 0) {
            curCateId.value = tabs[tabs.length - 1].id
        }
    }
}

function calcSize() {
    let h = 10
    goods.value.forEach(item => {
        let view = uni.createSelectorQuery().in(instance).select(`#cate-${item.id}`)
        view.fields({
            size: true
        }, (data: any) => {
            item.top = h
            h += data.height
            item.bottom = h
        }).exec()
    })
    sizeCalcState.value = true
}
const toList = (item: any) => {
console.log(item)
    uni.navigateTo({
        url: '/pages/shop/sort?category='+item.id
    })
}
</script>

<style lang="scss">
$text-color-assist: #919293; // 辅助色
$text-color-base: #5A5B5C; // 基础色

page {
    height: 100%;
}
.zh-wrapper {
   flex:1;
    overflow: hidden;
    width: 100%;
    display: flex;

    .menus {
        width: 200rpx;
        height: 100%;
        overflow: hidden;

        .wrapper {
            width: 100%;
            height: 100%;

            .menu {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                padding: 30rpx 20rpx;
                position: relative;
                border-left: 6rpx solid transparent;
                color: #4F4F4F;
                text-align: center;
                font-feature-settings: 'case' on;
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Subheadline, 30rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Subheadline, 40rpx);
                letter-spacing: var(--Letter-Spacing-Subheadline, -0.24px);

                &:nth-last-child(1) {
                    margin-bottom: 130rpx;
                }
                &.current {
                    border-left: 6rpx solid $uni-color-primary;
                    color: #282A2E;
                    text-align: center;
                    font-feature-settings: 'case' on;
                    /* Medium/Subheadline */
                    font-family: "PingFang SC";
                    font-size: 30rpx;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 40rpx; /* 133.333% */
                    letter-spacing: -0.24px;
                }
                .dot {
                    position: absolute;
                    width: 34rpx;
                    height: 34rpx;
                    line-height: 34rpx;
                    font-size: 22rpx;
                    background-color: $uni-color-primary;
                    color: #ffffff;
                    top: 16rpx;
                    right: 10rpx;
                    border-radius: 100%;
                    text-align: center;
                }
            }
        }
    }

    .goods {
        flex: 1;
        height: 100%;
        overflow: hidden;
        background-color: #ffffff;

        .wrapper {
            width: 100%;
            height: 100%;
            padding: 20rpx;

            .list {
                width: 100%;
                font-size: $uni-font-size-base;
                padding-bottom: 130rpx;

                :deep(.category) {
                    width: 100%;

                    .title {
                        padding: 30rpx 0;
                        display: flex;
                        align-items: center;
                        color: $text-color-base;
                        font-weight: bold;

                        .icon {
                            width: 38rpx;
                            height: 38rpx;
                            margin-left: 10rpx;
                        }
                    }
                }

                :deep(.items) {
                    width: 100%;
                    //background-color:#f5f5f5;
                    .good {
                        width: 100%;
                        flex-shrink: 0;
                        display: flex;
                        align-items: center;
                        margin-bottom: 30rpx;
                        margin-right: 20rpx;

                        .right {
                            flex: 1;
                            height: 30rpx;
                            overflow: hidden;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding-right: 14rpx;

                            .name {
                                color: #191919;
                                font-family: "SF Pro Text";
                                font-size: 28rpx;
                                font-style: normal;
                                font-weight: 500;
                                line-height: 28rpx; /* 100% */
                            }

                            .desc {
                                flex: 1;
                                text-align: right;
                                color: #969696;
                                font-family: "PingFang SC";
                                font-size: 24rpx;
                                font-style: normal;
                                font-weight: 400;
                                line-height: 24rpx; /* 100% */
                            }

                            .price_and_action {
                                width: 100%;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .price {
                                    font-size: $uni-font-size-base;
                                    font-weight: 600;
                                }

                                .btn-group {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    position: relative;

                                    .btn {
                                        padding: 0 20rpx;
                                        box-sizing: border-box;
                                        font-size: $uni-font-size-sm;
                                        height: 44rpx;
                                        line-height: 44rpx;

                                        &.property_btn {
                                            border-radius: 24rpx;
                                        }

                                        &.add_btn,
                                        &.reduce_btn {
                                            padding: 0;
                                            width: 44rpx;
                                            border-radius: 44rpx;
                                        }
                                    }

                                    .dot {
                                        position: absolute;
                                        background-color: #ffffff;
                                        border: 1px solid $uni-color-primary;
                                        color: $uni-color-primary;
                                        font-size: $uni-font-size-sm;
                                        width: 36rpx;
                                        height: 36rpx;
                                        line-height: 36rpx;
                                        text-align: center;
                                        border-radius: 100%;
                                        right: -12rpx;
                                        top: -10rpx;
                                    }

                                    .number {
                                        width: 44rpx;
                                        height: 44rpx;
                                        line-height: 44rpx;
                                        text-align: center;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>