<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" >开启碰撞告警
                <u-switch v-model="mainInfo.flag"></u-switch>
            </view>
        </view>

        <view class="listBox" v-if="mainInfo.flag">
            <view class="listItem borderTopRadius ">开启App通知
                <u-switch v-model="mainInfo.info.app"></u-switch>
            </view>
            <view class="listItem " >开启短信通知
                <u-switch v-model="mainInfo.info.msg"></u-switch>
            </view>
            <view class="listItem   borderBottomRadius">开启语音电话通知
                <u-switch v-model="mainInfo.info.tel"></u-switch>
            </view>
        </view>
    </view>
    <view class="mainDesc">通知号码为App注册手机号，可在我的-账号管理-手机号进行变更</view>
</template>

<script lang="ts" setup>
import {onMounted, ref,watch} from 'vue'


import {useCarStatusStore} from '@/stores'
const carStatusStore = useCarStatusStore()
const mainInfo:any = ref({})
let mainList:any = ref([])
import {cloneDeep} from "@/utils/clone";
const props = defineProps({
    id:{
        type:Number||String,
        default:0
    }
})
onMounted(()=>{
    let list = cloneDeep(carStatusStore.carStatus)
    for(let i in list){
        if(list[i].id==props.id){
            mainInfo.value =list[i]
        }
    }
    mainList.value =  list
})
watch(()=>mainInfo.value,()=>{
    mainList.value[props.id-1] = mainInfo.value
    carStatusStore.setCarStatus(mainList.value)
},{
    deep:true
})
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}
.mainDesc{
    padding: 40rpx;
    color: var(--Text-tips, #909399);
    font-feature-settings: 'case' on;
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption2, 22rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
    letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;
    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .nickname {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

    }
}


</style>
