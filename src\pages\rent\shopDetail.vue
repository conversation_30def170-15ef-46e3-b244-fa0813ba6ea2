<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius">
                <image :src="mainInfo.imgs[0]" class="banner"></image>
                <view class="name">{{ mainInfo.name }}</view>
                <view class="title">{{ mainInfo.company }}</view>
                <view class="border"></view>
                <view class="infoBox border">
                    <view class="infoContent">
                        <view class="infoDesc">营业时间</view>
                        <view class="infoText">00：00-24：00</view>
                    </view>
                    <L-arrow></L-arrow>
                </view>
                <view class="infoBox border">
                    <view class="infoContent">
                        <view class="infoDesc">{{ mainInfo.address }}</view>
                        <view class="infoText blue">距您约{{ distanceStr }}</view>
                    </view>
                    <L-arrow></L-arrow>
                </view>
                <view class="infoBox">
                    <view class="infoContent">
                        <view class="infoDesc">联系电话</view>
                        <view class="infoText">{{ mainInfo.tel }}</view>
                    </view>
                    <L-arrow></L-arrow>
                </view>
            </view>
        </view>
        <view class="listBox" style="padding-bottom: 100rpx">
            <view class="mainTitle borderTopRadius ">租车套餐</view>
            <view class="listItem  borderBottomRadius">
                <u-radio-group v-model="comboValue" @change="radioGroupChange">
                    <view v-for="item in list" class="priceBox ">
                        <view class="main">
                            <u-radio :name="item.menu.id" style="width:40rpx"></u-radio>
                            <view class="mainInfo">
                                <view class="title"> {{ item.menu.name }}</view>
                                <view class="desc">{{ item.menu.description }}</view>
                                <view class="priceMain">
                                    <view class="price">
                                        <view class="priceText">租金</view>
                                        <view class="priceNum">
                                            <view class="unit">￥</view>
                                            {{ item.menu.price / 100 }}
                                        </view>
                                    </view>
                                    <view class="price">
                                        <view class="priceText">天数</view>
                                        <view class="priceNum"> {{ item.menu.days }}
                                            <view class="unit">天</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>

                    </view>
                </u-radio-group>
            </view>
        </view>
    </view>
    <view class="btnBox">
        <u-button type="primary" @click="buyRent">去购买</u-button>
    </view>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getNearStoreComboAPI, getNearStoreDetailAPI} from "@/services/rent";

const buyRent = () => {
    uni.navigateTo({
        url: '/pages/rent/createOrder?id=' + comboValue.value + "&store=" + props.id
    })
}
const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})
const mainInfo: any = ref<any>({})
const list = ref([])
onMounted(async () => {
    let res = await getNearStoreDetailAPI(props.id)
    console.log(res)
    mainInfo.value = res.result
    uni.getLocation({
        type: 'gcj02',
        success: (res) => {
            console.log(res, mainInfo)
            getDistance(mainInfo.value.location?.coordinates[1], mainInfo.value.location?.coordinates[0], res.latitude, res.longitude)
        }
    });
    const resList = await getNearStoreComboAPI(props.id)
    console.log(resList)
    let arr = resList.result.rows
    comboValue.value = arr[0].menu.id
    list.value = arr
})
const distanceStr: any = ref(0)
const getDistance = (lat1: any, lon1: any, lat2: any, lon2: any) => {
    console.log(lat1, lon1, lat2, lon2)
    const R = 6371; // 地球半径，单位为公里
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    // 距离，单位为公里
    const distance = R * c * 1000;
    if (distance < 1000) {
        distanceStr.value = distance.toFixed(0) + 'm'
    } else {
        distanceStr.value = (distance / 1000).toFixed(2) + 'km'
    }
}
const deg2rad = (deg: any) => {
    return deg * (Math.PI / 180);
}
const comboValue = ref(0)
const radioGroupChange = (e: any) => {
    console.log(e)
    comboValue.value = e
}
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.btnBox {
    position: fixed;
    left: 32rpx;
    width: 686rpx;
    border-radius: 16rpx;
    bottom: 0;
}

.mainTitle {
    background: #ffffff;
    color: var(--Text-main, #282A2E);
    font-feature-settings: 'case' on;
    font-family: "PingFang SC";
    font-size: 34rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 44rpx; /* 129.412% */
    letter-spacing: -0.408px;
    padding: 24rpx 22rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 16rpx;
        background: #FFFFFF;
        color: var(--Text-main, #282A2E);
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .banner {
            width: 654rpx;
            height: 366rpx;
            flex-shrink: 0;
            border-radius: 12rpx;
            box-shadow: 0px 1.907px 3.813px 0px rgba(0, 0, 0, 0.04);
        }

        .name {
            color: var(--Text-main, #282A2E);
            font-feature-settings: 'case' on;
            font-family: "PingFang SC";
            font-size: 34rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 44rpx; /* 129.412% */
            letter-spacing: -0.408px;
            margin-top: 24rpx;
        }

        .title {
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 100% */
            margin-top: 10rpx;
            margin-bottom: 26rpx;
        }

        .infoBox {
            display: flex;
            align-items: center;
            margin-top: 16rpx;
            padding: 24rpx;

            .infoContent {
                flex: 1;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-right: 8rpx;

                .infoDesc {
                    flex: 1;
                    color: var(--Text-main, #282A2E);
                    font-family: var(--Font-Family, "PingFang SC");
                    font-size: var(--Font-Size-Callout, 32rpx);
                    font-style: normal;
                    font-weight: 400;
                    line-height: var(--Line-Height-Callout, 42rpx); /* 131.25% */
                    letter-spacing: var(--Letter-Spacing-Callout, -0.32px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .infoText {
                    width: 186rpx;
                    color: var(--Text-content, #53565C);
                    font-family: var(--Font-Family, "PingFang SC");
                    font-size: var(--Font-Size-Footnote, 24rpx);
                    font-style: normal;
                    font-weight: 400;
                    line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
                    letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
                    flex-shrink: 0;
                    text-align: right;
                }

                .blue {
                    color: #3C9CFF;
                }
            }
        }

        .priceBox {
            width: 100%;
            padding: 12rpx 24rpx;

            .main {
                display: flex;
                align-items: flex-start;

                .mainInfo {
                    flex: 1;

                    .title {
                        color: var(--Text-main, #282A2E);
                        font-feature-settings: 'case' on;
                        font-family: "PingFang SC";
                        font-size: 30rpx;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 30rpx;
                    }

                    .desc {
                        color: var(--Text-main, #999);
                        font-family: "PingFang SC";
                        font-size: 24rpx;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 24rpx; /* 100% */
                        margin-bottom: 16rpx;
                    }

                    .priceMain {
                        display: flex;

                        .price {
                            flex: 1;
                            display: flex;
                            align-items: flex-end;
                        }

                        .priceText {
                            color: #C3C3C3;
                            font-family: "PingFang SC";
                            font-size: 24rpx;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 24rpx; /* 100% */
                        }

                        .priceNum {
                            display: flex;
                            align-items: flex-end;
                            color: #F00;
                            font-family: "PingFang SC";
                            font-size: 40rpx;
                            font-style: normal;
                            font-weight: 500;
                            line-height: 35rpx; /* 100% */
                            .unit {
                                color: #F00;
                                font-family: "PingFang SC";
                                font-size: 24rpx;
                                font-style: normal;
                                font-weight: 500;
                                line-height: 24rpx; /* 100% */
                            }
                        }
                    }
                }

            }


        }
    }

    .border {
        border-bottom: 1px solid #ededed;
    }
}
</style>
