<template>
	<view class="listBox">
		<view class="listItem" @tap="toSelModel(item)" v-for="item in brandList">
            <view class="brandImg">
                <image class="brand" :src="item.img" mode="aspectFit"></image>
            </view>
            <view class="brandText">{{ item.name }}</view>
            <L-arrow></L-arrow>
        </view>
	</view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {useCarStore} from '@/stores'
import {onMounted} from "vue";
import {getBrandAPI} from '@/services/device'
const carStore = useCarStore()
const brandList = ref([])
onMounted(async ()=>{
   let res =  await getBrandAPI({page:1,size:100})
    if(res.msg=='success'){
        brandList.value = res.result.rows
    }
    console.log(res)
})
const toSelModel = (item:any) => {
    let obj =  Object.assign(carStore.carInfo||{}, {brand:item})
    // 合并对象
    carStore.setCarInfo(obj)
    uni.redirectTo({
        url: '/pages/addDevice/selModel?id='+item.id
    })
}
</script>

<style lang="scss" scoped>
.listBox{
padding: 32rpx;
    .listItem{
        padding: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin-bottom: 32rpx;
        .brandImg{
            width: 112rpx;
            height: 112rpx;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12rpx;
            background: var(--Background-layout, #F5F5F5);
            .brand{
                width: 40rpx;
                height: 40rpx;
            }
        }
        .brandText{
            flex: 1;
            margin: 0 28rpx;
            color: #000;

            /* Medium/Headline */
            font-family: "PingFang SC";
            font-size: 34rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 44rpx; /* 129.412% */
            letter-spacing: -0.408px;
        }
    }
}
</style>
