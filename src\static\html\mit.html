<!doctype html>
<html>
<head>
<meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'>

<link href='https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext' rel='stylesheet' type='text/css' /><style type='text/css'>html {overflow-x: initial !important;}:root { --bg-color: #ffffff; --text-color: #333333; --select-text-bg-color: #B5D6FC; --select-text-font-color: auto; --monospace: "Lucida Console",Consolas,"Courier",monospace; --title-bar-height: 20px; }
.mac-os-11 { --title-bar-height: 28px; }
html { font-size: 14px; background-color: var(--bg-color); color: var(--text-color); font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; }
h1, h2, h3, h4, h5 { white-space: pre-wrap; }
body { margin: 0px; padding: 0px; height: auto; inset: 0px; font-size: 1rem; line-height: 1.428571; overflow-x: hidden; background: inherit; }
iframe { margin: auto; }
a.url { word-break: break-all; }
a:active, a:hover { outline: 0px; }
.in-text-selection, ::selection { text-shadow: none; background: var(--select-text-bg-color); color: var(--select-text-font-color); }
#write { margin: 0px auto; height: auto; width: inherit; word-break: normal; overflow-wrap: break-word; position: relative; white-space: normal; overflow-x: visible; padding-top: 36px; }
#write.first-line-indent p { text-indent: 2em; }
#write.first-line-indent li p, #write.first-line-indent p * { text-indent: 0px; }
#write.first-line-indent li { margin-left: 2em; }
.for-image #write { padding-left: 8px; padding-right: 8px; }
body.typora-export { padding-left: 30px; padding-right: 30px; }
.typora-export .footnote-line, .typora-export li, .typora-export p { white-space: pre-wrap; }
.typora-export .task-list-item input { pointer-events: none; }
@media screen and (max-width: 500px) {
  body.typora-export { padding-left: 0px; padding-right: 0px; }
  #write { padding-left: 20px; padding-right: 20px; }
}
#write li > figure:last-child { margin-bottom: 0.5rem; }
#write ol, #write ul { position: relative; }
img { max-width: 100%; vertical-align: middle; image-orientation: from-image; }
button, input, select, textarea { color: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; font-size-adjust: inherit; font-kerning: inherit; font-variant-alternates: inherit; font-variant-ligatures: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-position: inherit; font-variant-emoji: inherit; font-feature-settings: inherit; font-optical-sizing: inherit; font-variation-settings: inherit; }
input[type="checkbox"], input[type="radio"] { line-height: normal; padding: 0px; }
*, ::after, ::before { box-sizing: border-box; }
#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre { width: inherit; }
#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p { position: relative; }
p { line-height: inherit; }
h1, h2, h3, h4, h5, h6 { break-after: avoid-page; break-inside: avoid; orphans: 4; }
p { orphans: 4; }
h1 { font-size: 2rem; }
h2 { font-size: 1.8rem; }
h3 { font-size: 1.6rem; }
h4 { font-size: 1.4rem; }
h5 { font-size: 1.2rem; }
h6 { font-size: 1rem; }
.md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p { margin-top: 1rem; margin-bottom: 1rem; }
.hidden { display: none; }
.md-blockmeta { color: rgb(204, 204, 204); font-weight: 700; font-style: italic; }
a { cursor: pointer; }
sup.md-footnote { padding: 2px 4px; background-color: rgba(238, 238, 238, 0.7); color: rgb(85, 85, 85); border-radius: 4px; cursor: pointer; }
sup.md-footnote a, sup.md-footnote a:hover { color: inherit; text-transform: inherit; text-decoration: inherit; }
#write input[type="checkbox"] { cursor: pointer; width: inherit; height: inherit; }
figure { overflow-x: auto; margin: 1.2em 0px; max-width: calc(100% + 16px); padding: 0px; }
figure > table { margin: 0px; }
thead, tr { break-inside: avoid; break-after: auto; }
thead { display: table-header-group; }
table { border-collapse: collapse; border-spacing: 0px; width: 100%; overflow: auto; break-inside: auto; text-align: left; }
table.md-table td { min-width: 32px; }
.CodeMirror-gutters { border-right-width: 0px; border-right-style: none; border-right-color: currentcolor; background-color: inherit; }
.CodeMirror-linenumber { -webkit-user-select: none; }
.CodeMirror { text-align: left; }
.CodeMirror-placeholder { opacity: 0.3; }
.CodeMirror pre { padding: 0px 4px; }
.CodeMirror-lines { padding: 0px; }
div.hr:focus { cursor: none; }
#write pre { white-space: pre-wrap; }
#write.fences-no-line-wrapping pre { white-space: pre; }
#write pre.ty-contain-cm { white-space: normal; }
.CodeMirror-gutters { margin-right: 4px; }
.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background: inherit; position: relative !important; }
.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }
#write .md-fences.mock-cm { white-space: pre-wrap; }
.md-fences.md-fences-with-lineno { padding-left: 0px; }
#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }
.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }
.CodeMirror-line, twitterwidget { break-inside: avoid; }
svg { break-inside: avoid; }
.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }
.footnotes + .footnotes { margin-top: 0px; }
.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; background: 0px 0px; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; }
li div { padding-top: 0px; }
blockquote { margin: 1rem 0px; }
li .mathjax-block, li p { margin: 0.5rem 0px; }
li blockquote { margin: 1rem 0px; }
li { margin: 0px; position: relative; }
blockquote > :last-child { margin-bottom: 0px; }
blockquote > :first-child, li > :first-child { margin-top: 0px; }
.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }
#write .footnote-line { white-space: pre-wrap; }
@media print {
  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }
  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }
  .typora-export * { print-color-adjust: exact; }
  .typora-export #write { break-after: avoid; }
  .typora-export #write::after { height: 0px; }
  .is-mac table { break-inside: avoid; }
  #write > p:nth-child(1) { margin-top: 0px; }
  .typora-export-show-outline .typora-export-sidebar { display: none; }
  figure { overflow-x: visible; }
}
.footnote-line { margin-top: 0.714em; font-size: 0.7em; }
a img, img a { cursor: pointer; }
pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background: rgb(204, 204, 204); display: block; overflow-x: hidden; }
p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }
#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }
p > .md-image:only-child { display: inline-block; width: 100%; }
#write .MathJax_Display { margin: 0.8em 0px 0px; }
.md-math-block { width: 100%; }
.md-math-block:not(:empty)::after { display: none; }
.MathJax_ref { fill: currentcolor; }
[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }
.md-task-list-item { position: relative; list-style-type: none; }
.task-list-item.md-task-list-item { padding-left: 0px; }
.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: medium; }
.math { font-size: 1rem; }
.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }
.md-toc-content { position: relative; margin-left: 0px; }
.md-toc-content::after, .md-toc::after { display: none; }
.md-toc-item { display: block; color: rgb(65, 131, 196); }
.md-toc-item a { text-decoration: none; }
.md-toc-inner:hover { text-decoration: underline; }
.md-toc-inner { display: inline-block; cursor: pointer; }
.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }
.md-toc-h2 .md-toc-inner { margin-left: 2em; }
.md-toc-h3 .md-toc-inner { margin-left: 4em; }
.md-toc-h4 .md-toc-inner { margin-left: 6em; }
.md-toc-h5 .md-toc-inner { margin-left: 8em; }
.md-toc-h6 .md-toc-inner { margin-left: 10em; }
@media screen and (max-width: 48em) {
  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }
  .md-toc-h4 .md-toc-inner { margin-left: 5em; }
  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }
  .md-toc-h6 .md-toc-inner { margin-left: 8em; }
}
a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }
.footnote-line a:not(.reversefootnote) { color: inherit; }
.reversefootnote { font-family: ui-monospace, sans-serif; }
.md-attr { display: none; }
.md-fn-count::after { content: "."; }
code, pre, samp, tt { font-family: var(--monospace); }
kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }
.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }
code { text-align: left; vertical-align: initial; }
a.md-print-anchor { white-space: pre !important; border-width: medium !important; border-style: none !important; border-color: currentcolor !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; background: 0px 0px !important; text-decoration: initial !important; text-shadow: initial !important; }
.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }
.md-diagram-panel > svg { max-width: 100%; }
[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }
[lang="mermaid"] .node text { font-size: 1rem; }
table tr th { border-bottom-width: 0px; border-bottom-style: none; border-bottom-color: currentcolor; }
video { max-width: 100%; display: block; margin: 0px auto; }
iframe { max-width: 100%; width: 100%; border: medium; }
.highlight td, .highlight tr { border: 0px; }
mark { background: rgb(255, 255, 0); color: rgb(0, 0, 0); }
.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }
.md-expand mark .md-meta { opacity: 0.3 !important; }
mark .md-meta { color: rgb(0, 0, 0); }
@media print {
  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }
}
.md-diagram-panel .messageText { stroke: none !important; }
.md-diagram-panel .start-state { fill: var(--node-fill); }
.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }
.md-fences.md-fences-math { font-size: 1em; }
.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }
.md-fences-advanced:not(.md-focus) { background: inherit; }
.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }
.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }
.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }
.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }
@media screen and (max-width: 1024px) {
  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }
}
@media screen and (max-width: 800px) {
  .typora-export-sidebar { display: none; }
}
.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }
.outline-content ul { margin-top: 0px; margin-bottom: 0px; }
.outline-content strong { font-weight: 400; }
.outline-expander { width: 1rem; height: 1.428571rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }
.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }
.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }
.outline-expander:hover::before { content: ""; }
.outline-h1 > .outline-item { padding-left: 0px; }
.outline-h2 > .outline-item { padding-left: 1em; }
.outline-h3 > .outline-item { padding-left: 2em; }
.outline-h4 > .outline-item { padding-left: 3em; }
.outline-h5 > .outline-item { padding-left: 4em; }
.outline-h6 > .outline-item { padding-left: 5em; }
.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }
.outline-label:hover { text-decoration: underline; }
.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }
.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left-width: 28px; border-left-style: solid; border-left-color: transparent; border-right-width: 28px; border-right-style: solid; border-right-color: transparent; }
.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }
.outline-item-open > .outline-item > .outline-expander::before { content: ""; }
.outline-children { display: none; }
.info-panel-tab-wrapper { display: none; }
.outline-item-open > .outline-children { display: block; }
.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }
.typora-export .outline-item:hover { margin-right: -8px; border-right-width: 8px; border-right-style: solid; border-right-color: transparent; }
.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }
.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }
.typora-export-collapse-outline .outline-children { display: none; }
.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }
.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }
.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }
.md-inline-math-container mjx-container { zoom: 0.95; }
mjx-container { break-inside: avoid; }
.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }
.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }
.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }
.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }
.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }
.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left-width: 0.25em; border-left-style: solid; border-left-color: rgb(0, 0, 0); }
.md-alert-text-note { color: rgb(9, 105, 218); }
.md-alert-text-important { color: rgb(130, 80, 223); }
.md-alert-text-warning { color: rgb(154, 103, 0); }
.md-alert-text-tip { color: rgb(31, 136, 61); }
.md-alert-text-caution { color: rgb(207, 34, 46); }
.md-alert-text { font-size: 0.9rem; font-weight: 700; }
.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }
.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }


:root {
    --side-bar-bg-color: #fafafa;
    --control-text-color: #777;
}

@include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);

/* open-sans-regular - latin-ext_latin */
  /* open-sans-italic - latin-ext_latin */
    /* open-sans-700 - latin-ext_latin */
    /* open-sans-700italic - latin-ext_latin */
  html {
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
}

body {
    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;
    color: rgb(51, 51, 51);
    line-height: 1.6;
}

#write {
    max-width: 860px;
  	margin: 0 auto;
  	padding: 30px;
    padding-bottom: 100px;
}

@media only screen and (min-width: 1400px) {
	#write {
		max-width: 1024px;
	}
}

@media only screen and (min-width: 1800px) {
	#write {
		max-width: 1200px;
	}
}

#write > ul:first-child,
#write > ol:first-child{
    margin-top: 30px;
}

a {
    color: #4183C4;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-weight: bold;
    line-height: 1.4;
    cursor: text;
}
h1:hover a.anchor,
h2:hover a.anchor,
h3:hover a.anchor,
h4:hover a.anchor,
h5:hover a.anchor,
h6:hover a.anchor {
    text-decoration: none;
}
h1 tt,
h1 code {
    font-size: inherit;
}
h2 tt,
h2 code {
    font-size: inherit;
}
h3 tt,
h3 code {
    font-size: inherit;
}
h4 tt,
h4 code {
    font-size: inherit;
}
h5 tt,
h5 code {
    font-size: inherit;
}
h6 tt,
h6 code {
    font-size: inherit;
}
h1 {
    font-size: 2.25em;
    line-height: 1.2;
    border-bottom: 1px solid #eee;
}
h2 {
    font-size: 1.75em;
    line-height: 1.225;
    border-bottom: 1px solid #eee;
}

/*@media print {
    .typora-export h1,
    .typora-export h2 {
        border-bottom: none;
        padding-bottom: initial;
    }

    .typora-export h1::after,
    .typora-export h2::after {
        content: "";
        display: block;
        height: 100px;
        margin-top: -96px;
        border-top: 1px solid #eee;
    }
}*/

h3 {
    font-size: 1.5em;
    line-height: 1.43;
}
h4 {
    font-size: 1.25em;
}
h5 {
    font-size: 1em;
}
h6 {
   font-size: 1em;
    color: #777;
}
p,
blockquote,
ul,
ol,
dl,
table{
    margin: 0.8em 0;
}
li>ol,
li>ul {
    margin: 0 0;
}
hr {
    height: 2px;
    padding: 0;
    margin: 16px 0;
    background-color: #e7e7e7;
    border: 0 none;
    overflow: hidden;
    box-sizing: content-box;
}

li p.first {
    display: inline-block;
}
ul,
ol {
    padding-left: 30px;
}
ul:first-child,
ol:first-child {
    margin-top: 0;
}
ul:last-child,
ol:last-child {
    margin-bottom: 0;
}
blockquote {
    border-left: 4px solid #dfe2e5;
    padding: 0 15px;
    color: #777777;
}
blockquote blockquote {
    padding-right: 0;
}
table {
    padding: 0;
    word-break: initial;
}
table tr {
    border: 1px solid #dfe2e5;
    margin: 0;
    padding: 0;
}
table tr:nth-child(2n),
thead {
    background-color: #f8f8f8;
}
table th {
    font-weight: bold;
    border: 1px solid #dfe2e5;
    border-bottom: 0;
    margin: 0;
    padding: 6px 13px;
}
table td {
    border: 1px solid #dfe2e5;
    margin: 0;
    padding: 6px 13px;
}
table th:first-child,
table td:first-child {
    margin-top: 0;
}
table th:last-child,
table td:last-child {
    margin-bottom: 0;
}

.CodeMirror-lines {
    padding-left: 4px;
}

.code-tooltip {
    box-shadow: 0 1px 1px 0 rgba(0,28,36,.3);
    border-top: 1px solid #eef2f2;
}

.md-fences,
code,
tt {
    border: 1px solid #e7eaed;
    background-color: #f8f8f8;
    border-radius: 3px;
    padding: 0;
    padding: 2px 4px 0px 4px;
    font-size: 0.9em;
}

code {
    background-color: #f3f4f4;
    padding: 0 2px 0 2px;
}

.md-fences {
    margin-bottom: 15px;
    margin-top: 15px;
    padding-top: 8px;
    padding-bottom: 6px;
}


.md-task-list-item > input {
  margin-left: -1.3em;
}

@media print {
    html {
        font-size: 13px;
    }
    pre {
        page-break-inside: avoid;
        word-wrap: break-word;
    }
}

.md-fences {
	background-color: #f8f8f8;
}
#write pre.md-meta-block {
	padding: 1rem;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f7f7f7;
    border: 0;
    border-radius: 3px;
    color: #777777;
    margin-top: 0 !important;
}

.mathjax-block>.code-tooltip {
	bottom: .375rem;
}

.md-mathjax-midline {
    background: #fafafa;
}

#write>h3.md-focus:before{
	left: -1.5625rem;
	top: .375rem;
}
#write>h4.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
#write>h5.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
#write>h6.md-focus:before{
	left: -1.5625rem;
	top: .285714286rem;
}
.md-image>.md-meta {
    /*border: 1px solid #ddd;*/
    border-radius: 3px;
    padding: 2px 0px 0px 4px;
    font-size: 0.9em;
    color: inherit;
}

.md-tag {
    color: #a7a7a7;
    opacity: 1;
}

.md-toc { 
    margin-top:20px;
    padding-bottom:20px;
}

.sidebar-tabs {
    border-bottom: none;
}

#typora-quick-open {
    border: 1px solid #ddd;
    background-color: #f8f8f8;
}

#typora-quick-open-item {
    background-color: #FAFAFA;
    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;
    border-style: solid;
    border-width: 1px;
}

/** focus mode */
.on-focus-mode blockquote {
    border-left-color: rgba(85, 85, 85, 0.12);
}

header, .context-menu, .megamenu-content, footer{
    font-family: "Segoe UI", "Arial", sans-serif;
}

.file-node-content:hover .file-node-icon,
.file-node-content:hover .file-node-open-state{
    visibility: visible;
}

.mac-seamless-mode #typora-sidebar {
    background-color: #fafafa;
    background-color: var(--side-bar-bg-color);
}

.mac-os #write{
    caret-color: AccentColor;
}

.md-lang {
    color: #b4654d;
}

/*.html-for-mac {
    --item-hover-bg-color: #E6F0FE;
}*/

#md-notification .btn {
    border: 0;
}

.dropdown-menu .divider {
    border-color: #e5e5e5;
    opacity: 0.4;
}

.ty-preferences .window-content {
    background-color: #fafafa;
}

.ty-preferences .nav-group-item.active {
    color: white;
    background: #999;
}

.menu-item-container a.menu-style-btn {
    background-color: #f5f8fa;
    background-image: linear-gradient( 180deg , hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0)); 
}


 @media print { @page {margin: 0 0 0 0;} body.typora-export {padding-left: 0; padding-right: 0;} #write {padding:0;}}
</style><title>EVL App 软件许可和服务协议</title>
</head>
<body class='typora-export'><div class='typora-export-content'>
<div id='write'  class=''><h1 id='ride-ass-app-软件许可和服务协议'><span>EVL App 软件许可和服务协议</span></h1><p><strong><span>特别提示</span></strong></p><p><span>为了使用EVL App（以下简称“本软件”）及其相关服务，您应当仔细阅读并遵守本《软件许可和服务协议》（以下简称“本协议”）。请您务必审慎阅读、充分理解各条款内容，特别是免除或限制责任的条款，以及开通或使用某项服务的单独协议，并选择接受或不接受。限制、免责条款可能以加粗形式提示您注意。</span></p><p><strong><span>重要声明</span></strong></p><ul><li><p><strong><span>接受协议</span></strong><span>：除非您已阅读并接受本协议所有条款，否则您无权使用本软件及相关服务。您使用本软件或相关服务即视为您已阅读并同意受本协议的约束。如果您不同意本协议，请在安装后自行卸载本软件。</span></p></li><li><p><strong><span>未成年人使用</span></strong><span>：如果您未满18周岁，请在法定监护人的陪同下阅读本协议及其他相关协议，并特别注意未成年人使用条款。</span></p></li></ul><hr /><h2 id='一协议的范围'><span>一、协议的范围</span></h2><h3 id='11-协议适用主体范围'><span>1.1 协议适用主体范围</span></h3><p><span>本协议是您（以下简称“您”或“用户”）与济南有人物联网技术有限公司及其关联公司（以下简称“本公司”或“我们”）之间关于您下载、安装、使用、复制本软件，以及使用相关服务所订立的协议。</span></p><h3 id='12-协议关系'><span>1.2 协议关系</span></h3><p><span>本协议内容同时包括我们可能不断发布的关于本服务的相关协议、业务规则等内容。您理解并同意，以上内容一经正式发布，即为本协议不可分割的组成部分，您同样应当遵守。我们会通过在相应页面显著位置提示、向您发送短信等方式通知您。在此情况下，若您继续使用我们的服务，即表示您同意受上述内容的约束。</span></p><hr /><h2 id='二关于本服务'><span>二、关于本服务</span></h2><h3 id='21-本服务的内容'><span>2.1 本服务的内容</span></h3><p><span>本服务包括以下内容，具体包括但不限于：</span></p><ul><li><p><span>车辆定位</span></p></li><li><p><span>轨迹查询</span></p></li><li><p><span>电量查询</span></p></li><li><p><span>预估里程查询</span></p></li><li><p><span>异动报警</span></p></li><li><p><span>智能诊断</span></p></li><li><p><span>远程锁车</span></p></li><li><p><span>失窃上报</span></p></li></ul><h3 id='22-本服务的形式'><span>2.2 本服务的形式</span></h3><ol start='' ><li><p><strong><span>客户端软件许可</span></strong><span>：您需要下载智能电动车客户端软件，我们授予您一项个人的、不可转让及非排他性的许可，仅可为访问或使用本服务的目的而使用本软件及服务。</span></p></li><li><p><strong><span>多平台支持</span></strong><span>：本服务客户端软件可能提供包括但不限于iOS、Android等多个应用版本，您应选择与所安装终端设备相匹配的软件版本。</span></p></li></ol><h3 id='23-本服务许可的范围'><span>2.3 本服务许可的范围</span></h3><ol start='' ><li><p><strong><span>许可授权</span></strong><span>：我们授予您一项个人的、不可转让及非排他性的许可，以使用本软件。您可为非商业目的在单一台终端设备上安装、使用、显示、运行本软件。</span></p></li><li><p><strong><span>复制权利</span></strong><span>：您可为使用本软件及服务的目的复制本软件的一个副本，仅用于备份。备份副本必须包含原软件中所有著作权信息。</span></p></li><li><p><strong><span>权利保留</span></strong><span>：本条及本协议其他条款未明示授权的所有权利仍由我们保留，您在行使这些权利时须另行取得我们的书面许可。若我们未行使前述任何权利，不构成对该权利的放弃。</span></p></li></ol><hr /><h2 id='三软件的获取'><span>三、软件的获取</span></h2><h3 id='31-获取途径'><span>3.1 获取途径</span></h3><p><span>您可以直接从我们的网站或得到我们授权的第三方获取本软件。</span></p><h3 id='32-未授权获取风险'><span>3.2 未授权获取风险</span></h3><p><span>如果您从未经授权的第三方获取本软件或与本软件名称相同的安装程序，我们无法保证该软件能够正常使用，并对因此给您造成的损失不予负责。</span></p><hr /><h2 id='四软件的安装与卸载'><span>四、软件的安装与卸载</span></h2><h3 id='41-安装版本选择'><span>4.1 安装版本选择</span></h3><p><span>我们可能为不同的终端设备开发不同的软件版本，您应根据实际情况选择合适的版本进行安装。</span></p><h3 id='42-安装步骤'><span>4.2 安装步骤</span></h3><p><span>下载安装程序后，您需要按照程序提示的步骤正确安装。</span></p><h3 id='43-卸载方式'><span>4.3 卸载方式</span></h3><p><span>如果您不再需要使用本软件或需要安装新版软件，可以自行卸载。</span></p><hr /><h2 id='五软件的更新'><span>五、软件的更新</span></h2><h3 id='51-更新目的'><span>5.1 更新目的</span></h3><p><span>为了改善用户体验、完善服务内容，我们将不断开发新服务，并不时提供软件更新（包括软件替换、修改、功能强化、版本升级等形式）。</span></p><h3 id='52-更新权利'><span>5.2 更新权利</span></h3><p><span>为了保证本软件及服务的安全性和功能一致性，我们有权不经特别通知而对软件进行更新，或对软件的部分功能效果进行改变或限制。</span></p><h3 id='53-旧版本处理'><span>5.3 旧版本处理</span></h3><p><span>本软件新版本发布后，旧版本可能无法使用。我们不保证旧版本软件的继续可用及相应的客户服务，请您随时下载最新版本。</span></p><hr /><h2 id='六用户个人信息保护'><span>六、用户个人信息保护</span></h2><h3 id='61-基本原则'><span>6.1 基本原则</span></h3><p><span>保护用户个人信息是我们的基本原则。除法律法规规定的情形外，未经用户许可，我们不会向第三方公开、透露用户个人信息。我们采用专业加密存储与传输方式，保障用户个人信息的安全。</span></p><h3 id='62-信息收集'><span>6.2 信息收集</span></h3><p><span>在注册账号或使用本服务过程中，您需提供必要的信息，例如：</span></p><ul><li><p><strong><span>账号注册</span></strong><span>：手机号码、邮箱等</span></p></li><li><p><strong><span>服务使用</span></strong><span>：车辆行踪轨迹信息</span></p></li></ul><p><span>若法律法规或政策有特殊规定，您需提供真实的身份信息。信息不完整可能导致服务受限或无法使用。</span></p><h3 id='63-信息管理'><span>6.3 信息管理</span></h3><p><span>您可随时浏览、修改自己提交的信息，但出于安全性和身份识别考虑，您可能无法修改注册时提供的初始注册信息及其他验证信息。</span></p><h3 id='64-安全措施'><span>6.4 安全措施</span></h3><p><span>我们将运用各种安全技术和程序建立完善的管理制度，保护您的个人信息，防止未经授权的访问、使用或披露。</span></p><h3 id='65-信息披露'><span>6.5 信息披露</span></h3><p><span>未经您的同意，我们不会向任何第三方公司、组织和个人披露您的个人信息，但法律法规另有规定的除外。</span></p><h3 id='66-未成年人保护'><span>6.6 未成年人保护</span></h3><p><span>我们非常重视未成年人个人信息的保护。若您是18周岁以下的未成年人，在使用我们的服务前，应取得您家长或法定监护人的书面同意。</span></p><h3 id='67-进一步信息'><span>6.7 进一步信息</span></h3><p><span>更多有关用户个人信息的规则，请查阅我们的《隐私政策》。</span></p><hr /><h2 id='七主要权利义务条款'><span>七、主要权利义务条款</span></h2><h3 id='71-账号使用规范'><span>7.1 账号使用规范</span></h3><h4 id='711-注册与绑定'><span>7.1.1 注册与绑定</span></h4><ul><li><p><span>您需注册一个智能电动车账号，使用手机号码和车辆上的SN码绑定注册。</span></p></li><li><p><span>为完善服务内容，我们可根据需求变更账号注册和绑定方式，且无须事先通知用户。</span></p></li></ul><h4 id='712-账号所有权'><span>7.1.2 账号所有权</span></h4><ul><li><p><span>智能电动车账号的所有权归本公司所有。</span></p></li><li><p><span>您仅获得账号的使用权，且该使用权仅属于初始注册人。</span></p></li><li><p><span>初始注册人不得赠与、借用、租用、转让或售卖账号，或许可非注册人使用。</span></p></li></ul><h4 id='713-账号安全'><span>7.1.3 账号安全</span></h4><ul><li><p><span>您有责任妥善保管账号信息及密码的安全。</span></p></li><li><p><span>您需对账号及密码下的行为承担法律责任。</span></p></li><li><p><span>不向他人透露账号及密码信息，若怀疑他人使用您的账号，应立即通知我们。</span></p></li></ul><h3 id='72-用户注意事项'><span>7.2 用户注意事项</span></h3><h4 id='721-资源使用'><span>7.2.1 资源使用</span></h4><ul><li><p><span>本软件使用过程中可能产生数据流量费用，用户需自行了解相关资费信息，并自行承担相关费用。</span></p></li></ul><h4 id='722-单独协议'><span>7.2.2 单独协议</span></h4><ul><li><p><span>使用某些特定服务时，可能需遵守单独协议，如《车辆智能服务协议》。使用前请阅读并同意相关协议。</span></p></li></ul><h4 id='723-数据存储安全'><span>7.2.3 数据存储安全</span></h4><ul><li><p><span>我们尽商业上的合理努力保障数据存储安全，但不提供完全保证，包括但不限于：</span></p><ul><li><p><span>不对数据删除或储存失败负责；</span></p></li><li><p><span>决定数据的最长储存期限及最大存储空间，建议用户自行备份；</span></p></li><li><p><span>停止使用服务后，我们可永久删除您的数据，且无义务返还任何数据。</span></p></li></ul></li></ul><h4 id='724-用户承担风险'><span>7.2.4 用户承担风险</span></h4><ul><li><p><span>用户需自行承担以下不可控风险：</span></p><ul><li><p><span>个人信息丢失、泄漏风险；</span></p></li><li><p><span>选择不匹配的软件版本导致的问题；</span></p></li><li><p><span>访问第三方网站带来的风险；</span></p></li><li><p><span>内容被他人转发、分享的风险；</span></p></li><li><p><span>网络不稳定导致的账号登录失败、资料同步不完整、页面加载缓慢等风险。</span></p></li></ul></li></ul><h3 id='73-第三方产品和服务'><span>7.3 第三方产品和服务</span></h3><h4 id='731-遵守第三方协议'><span>7.3.1 遵守第三方协议</span></h4><ul><li><p><span>使用第三方提供的产品或服务时，需遵守第三方的用户协议。我们与第三方各自承担责任。</span></p></li></ul><h4 id='732-第三方服务风险'><span>7.3.2 第三方服务风险</span></h4><ul><li><p><span>使用第三方系统或服务的结果由第三方提供，我们不保证其安全性、准确性、有效性及其他风险，相关争议及损害由用户自行承担。</span></p></li></ul><hr /><h2 id='八用户行为规范'><span>八、用户行为规范</span></h2><h3 id='81-信息内容规范'><span>8.1 信息内容规范</span></h3><h4 id='811-定义'><span>8.1.1 定义</span></h4><p><span>信息内容指用户在使用本软件及服务过程中制作、复制、发布、传播的任何内容，包括但不限于账号头像、名字、用户说明、文字、语音、图片等。</span></p><h4 id='812-禁止内容'><span>8.1.2 禁止内容</span></h4><p><span>用户不得利用账号或本软件及服务制作、复制、发布、传播以下干扰软件正常运营及侵犯他人合法权益的内容：</span></p><ol start='' ><li><p><strong><span>违反国家法律法规</span></strong><span>：</span></p><ul><li><p><span>违反宪法基本原则</span></p></li><li><p><span>危害国家安全、泄露国家秘密、颠覆政权、破坏统一</span></p></li><li><p><span>损害国家荣誉和利益</span></p></li><li><p><span>煽动民族仇恨、歧视，破坏团结</span></p></li><li><p><span>破坏宗教政策，宣扬邪教、封建迷信</span></p></li><li><p><span>散布谣言，扰乱社会秩序，破坏稳定</span></p></li><li><p><span>散布淫秽、色情、赌博、暴力、恐怖或教唆犯罪</span></p></li><li><p><span>侮辱或诽谤他人，侵害合法权益</span></p></li><li><p><span>煽动非法集会、结社、游行、示威、聚众扰乱</span></p></li><li><p><span>以非法民间组织名义活动</span></p></li><li><p><span>其他法律、行政法规禁止的内容</span></p></li></ul></li><li><p><strong><span>侵害他人合法权利</span></strong><span>：</span></p><ul><li><p><span>侵害名誉权、肖像权、知识产权、商业秘密等</span></p></li></ul></li><li><p><strong><span>涉及隐私</span></strong><span>：</span></p><ul><li><p><span>涉及他人隐私、个人信息或资料</span></p></li></ul></li><li><p><strong><span>骚扰与广告</span></strong><span>：</span></p><ul><li><p><span>发布骚扰、广告、过度营销或含有性暗示的信息</span></p></li></ul></li><li><p><strong><span>其他违法违规内容</span></strong><span>：</span></p><ul><li><p><span>违反法律法规、政策、公序良俗、社会公德，干扰软件运营，侵犯他人权益的信息内容</span></p></li></ul></li></ol><h3 id='82-软件使用规范'><span>8.2 软件使用规范</span></h3><p><span>除非法律允许或获得我们书面许可，您在使用本软件过程中不得从事以下行为：</span></p><ol start='' ><li><p><span>删除本软件及其副本上的著作权信息；</span></p></li><li><p><span>对本软件进行反向工程、反向汇编、反向编译，或尝试发现源代码；</span></p></li><li><p><span>使用、出租、出借、复制、修改、链接、转载、汇编、发表、出版、建立镜像站点等方式使用我们拥有知识产权的内容；</span></p></li><li><p><span>复制、修改、增加、删除、挂接运行或创作任何衍生作品，包括使用插件、外挂或未经授权的第三方工具/服务接入本软件和相关系统；</span></p></li><li><p><span>修改或伪造软件运行中的指令、数据，改变软件功能或运行效果，或传播用于上述用途的软件、方法；</span></p></li><li><p><span>通过未经授权的第三方软件、插件、外挂、系统登录或使用本软件及服务，或制作、发布上述工具；</span></p></li><li><p><span>干扰本软件及其组件、模块、数据的运行；</span></p></li><li><p><span>其他未经明确授权的行为。</span></p></li></ol><h3 id='83-服务运营规范'><span>8.3 服务运营规范</span></h3><p><span>除非法律允许或获得书面许可，您在使用本服务过程中不得从事以下行为：</span></p><ol start='' ><li><p><span>提交、发布虚假信息，冒充或利用他人名义使用、出租、复制、修改、链接、转载、汇编、发表、出版、建立镜像站点等行为；</span></p></li><li><p><span>诱导其他用户点击链接页面或分享信息；</span></p></li><li><p><span>虚构事实、隐瞒真相以误导、欺骗他人；</span></p></li><li><p><span>侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利；</span></p></li><li><p><span>利用账号或本软件及服务从事违法犯罪活动；</span></p></li><li><p><span>制作、发布与上述行为相关的方法、工具，或运营、传播此类方法、工具；</span></p></li><li><p><span>其他违反法律法规、侵犯他人权益、干扰正常运营或未经授权的行为。</span></p></li></ol><h3 id='84-对自己行为负责'><span>8.4 对自己行为负责</span></h3><p><span>您须对自己注册账号下的一切行为负责，包括所发表的任何内容及其后果。您应自行判断服务中的内容，并承担因使用内容而产生的所有风险，包括因依赖内容的正确性、完整性或实用性而产生的风险。我们不对因上述风险导致的任何损失或损害承担责任。</span></p><h3 id='85-违约处理'><span>8.5 违约处理</span></h3><h4 id='851-处罚措施'><span>8.5.1 处罚措施</span></h4><p><span>如果我们发现或收到他人举报用户违反本协议约定，我们有权不经通知随时删除、屏蔽相关内容，并视情节对违规账号处以警告、限制或禁止使用部分或全部功能、账号封禁直至注销的处罚，并公告处理结果。</span></p><h4 id='852-法律责任'><span>8.5.2 法律责任</span></h4><p><span>您同意，我们有权依合理判断对违反法律法规或本协议规定的行为进行处罚，采取适当的法律行动，并依据法律法规保存相关信息向有关部门报告，您应独自承担由此产生的一切法律责任。</span></p><h4 id='853-赔偿责任'><span>8.5.3 赔偿责任</span></h4><p><span>因您违反本协议或相关服务条款，导致或产生第三方主张的任何索赔、要求或损失，您应独立承担责任；若因此导致我们遭受损失，您亦应一并赔偿。</span></p><hr /><h2 id='九知识产权声明'><span>九、知识产权声明</span></h2><h3 id='91-知识产权归属'><span>9.1 知识产权归属</span></h3><p><span>本软件的一切著作权、商标权、专利权、商业秘密等知识产权，以及与本软件相关的所有信息内容（包括软件、程序、著作、文字、图片、音频、视频、图表、网页、界面设计、版面框架、数据或电子文档等）均受中华人民共和国法律法规和相应国际条约保护。本公司享有上述知识产权，但相关权利人依照法律规定应享有的权利除外。</span></p><h3 id='92-知识产权使用限制'><span>9.2 知识产权使用限制</span></h3><p><span>未经本公司或相关权利人书面同意，您不得为任何商业或非商业目的自行或许可第三方实施、利用、转让上述知识产权。</span></p><hr /><h2 id='十终端安全责任'><span>十、终端安全责任</span></h2><h3 id='101-风险声明'><span>10.1 风险声明</span></h3><p><span>您理解并同意，本软件可能受多种因素影响，包括但不限于用户行为、网络服务质量、社会环境等；也可能遭受安全问题侵扰，如他人非法利用用户资料进行骚扰，或终端设备感染病毒、木马程序等，影响本软件的正常使用。因此，您应加强信息安全及个人信息保护意识，注意密码保护，以免遭受损失。</span></p><hr /><h2 id='十一其他'><span>十一、其他</span></h2><h3 id='111-协议的修改'><span>11.1 协议的修改</span></h3><p><span>您使用本软件即视为您已阅读并同意受本协议的约束。我们有权在必要时修改本协议条款。您可在本软件的最新版本中查阅相关协议条款。协议条款变更后，如果您继续使用本软件，即视为您已接受修改后的协议；如果不接受修改后的协议，应停止使用本软件。</span></p><h3 id='112-协议签订地'><span>11.2 协议签订地</span></h3><p><span>本协议签订地为中华人民共和国山东省济南市。</span></p><h3 id='113-法律适用'><span>11.3 法律适用</span></h3><p><span>本协议的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。</span></p><h3 id='114-争议解决'><span>11.4 争议解决</span></h3><p><span>若您与本公司之间发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交本协议签订地有管辖权的人民法院管辖。</span></p><h3 id='115-标题解释'><span>11.5 标题解释</span></h3><p><span>本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</span></p><h3 id='116-条款效力'><span>11.6 条款效力</span></h3><p><span>本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。</span></p><hr /><p><strong><span>感谢您选择EVL App！</span></strong></p></div></div>
</body>
</html>