<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import OrderList from './components/OrderList.vue'

// 获取页面参数
const props = defineProps<{
    status: string
}>()
// tabs 数据
const orderTabs = ref([
    { id: -1, name: '全部', disabled: false },
    { id: 1, name: '待支付', disabled: false },
    { id: 4, name: '待发货', disabled: false },
    { id: 5, name: '待收货', disabled: false },
    { id: 7, name: '已完成', disabled: false },
    { id: 10, name: '售后', disabled: false },
    { id: 11, name: '已取消', disabled: false },
])
onMounted(
    () => {
        if(props.status==9999){
            activeIndex.value = 0
            activeTab.value = -1
        }else{
            let list = orderTabs.value
            for(let i in list){
                if(list[i].id==props.status){
                    changeTab(i)
                }
            }
        }
    }
)

// 高亮下标
const activeIndex: any = ref(0)
const changeTab = (index: any) => {
    console.error('当前选中索引：' + orderTabs.value[index].id, 11222)
    activeTab.value = orderTabs.value[index].id
    activeIndex.value = index
}
const activeTab = ref(-1)
</script>

<template>
    <view class="viewport">
        <v-tabs
            v-model="activeIndex"
            :tabs="orderTabs"
            z-index="99"
            activeColor="#3C9CFF"
            field="name"
            lineColor="#3C9CFF"
            @change="changeTab"
        ></v-tabs>
        <!-- 滑动项 -->
        <view v-for="item in orderTabs" :key="item.name">
            <!-- 订单列表 -->
            <view class="listBox">
                <OrderList v-if="activeTab == item.id" :order-state="item.id" />
            </view>
        </view>
    </view>
</template>

<style lang="scss">
page {
    height: 100%;
}
.listBox{
    height: calc(100% - 120rpx);
    overflow-y: scroll;
}

.viewport {
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

// tabs
.tabs {
    display: flex;
    justify-content: space-around;
    line-height: 60rpx;
    margin: 0 10rpx;
    background-color: #fff;
    box-shadow: 0 4rpx 6rpx rgba(240, 240, 240, 0.6);
    position: relative;
    z-index: 9;

    .item {
        flex: 1;
        text-align: center;
        padding: 20rpx;
        font-size: 28rpx;
        color: #262626;
    }

    .cursor {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 20%;
        height: 6rpx;
        padding: 0 50rpx;
        background-color: #3C9CFF;
        /* 过渡效果 */
        transition: all 0.4s;
    }
}

// swiper
.swiper {
    background-color: #f7f7f8;
}
</style>
