<script setup>
import {ref} from 'vue'
import DaDropdown from '@/components/da-dropdown/index.vue'
import {onShow} from "@dcloudio/uni-app";
import GoodItem from "@/pages/shop/components/goodListItem.vue";
import {getHotGoodsAPI} from "@/services/hot";


const goodList = ref([])

const currentPage = ref(1)
const pageSize = ref(100)
const totalItems = ref(0)
const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    title:{
        type: String,
        default: ''
    }
})

const getGoodList = async () => {
    try {
        let data = {
            page: currentPage.value,
            size: pageSize.value,
            collectId: props.id
        }
        const res = await getHotGoodsAPI(data)
        console.log(res)
        if (res.msg === 'success') {
            let list = res.result.rows
            goodList.value = list
        }
    } catch (error) {
        console.log(error)
    }
}
onShow(() => {
    getGoodList()
    uni.setNavigationBarTitle(
        {
            title: props.title
        }
    )
})
</script>
<template>
    <view class="ProductNew">
        <view class="productblock" style="background: #f5f5f5;margin-top: 32rpx">
            <view class="goodList">
                    <GoodItem :orderGood="item" v-for="item in goodList" :key="item.id" class="goodItem"></GoodItem>
                <mescroll-empty v-if="goodList.length == 0"></mescroll-empty>
            </view>
        </view>
    </view>
</template>
<style lang="scss" scoped>
.ProductNew {
    width: 750rpx;
    background: #f5f5f5 !important;
    min-height: 100vh;


    .goodList {
        padding: 0rpx 32rpx 32rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between; /* 第一个放在最左侧 */

    }

}
</style>
