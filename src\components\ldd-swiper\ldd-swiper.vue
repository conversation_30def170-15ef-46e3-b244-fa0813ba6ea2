<template>
    <view class="" style="position: relative;background: #ffffff">
        <swiper
            class="swiper"
            :indicator-dots="swiperDots"
            :autoplay="autoplay"
            :indicator-color="swiperColor"
            :circular="circular"
            :interval="interval"
            :duration="duration"
            :indicator-active-color="swiperActiveColor"
            @change="swiperChange"
        >
            <swiper-item v-for="(page, pageIndex) in pages" :key="pageIndex" style="width: 100%; height: 200upx;">
                <view class="swiper-item">
                    <view class="swiper-item-view" v-for="(item, index) in page" :key="index" @click="clickItem(item.id)">
                        <image class="swiper-img" :src="item.picture"></image>
                        <text>{{ item.name }}</text>
                    </view>
                </view>
            </swiper-item>
        </swiper>
        <view class="dots" v-if="swiperList.length>10">
            <block v-for="(item, index) in dotNum" :key="index">
                <view
                    class="dot"
                    :class="[index === swiperCurrent ? 'active' : '']"
                    :style="[index === swiperCurrent ? { background: swiperActiveColor } : { background: swiperColor }]"
                ></view>
            </block>
        </view>
    </view>
</template>

<script>
export default {
    name: 'ldd-swiper',
    props: {
        swiperDots: {
            type: Boolean,
            default: false
        },
        swiperColor: {
            type: String,
            default: '#999999'
        },
        swiperActiveColor: {
            type: String,
            default: '#3C9CFF'
        },
        autoplay: {
            type: Boolean,
            default: false
        },
        interval: {
            type: Number,
            default: 5000
        },
        duration: {
            type: Number,
            default: 500
        },
        circular: {
            type: Boolean,
            default: false
        },
        swiperList: {
            type: Array,
            default: () => []
        },
        customize: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            swiperCurrent: 0
        };
    },
    methods: {
        swiperChange(e) {
            this.swiperCurrent = e.detail.current;
        },
        clickItem(item) {
            this.$emit('clickItem', item);
        }
    },
    computed: {
        pages() {
            const pageSize = 10;
            const pages = [];
            for (let i = 0; i < this.swiperList.length; i += pageSize) {
                pages.push(this.swiperList.slice(i, i + pageSize));
            }
            console.log(this.swiperList,pages)
            return pages;
        },
        dotNum() {
            return Math.ceil(this.swiperList.length / 10);
        }
    }
};
</script>

<style>
.swiper {
    width: 100%;
    padding-top: 20upx;
    height: 210rpx;
    background: #f5f5f5;
}
.swiper-item {
    display: flex;
    flex-wrap: wrap;
}

.swiper-item-view {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 20%;
    height: 170upx;
}
.swiper-item-view text{
    display: inline-block;
    width: 100%;
font-size: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.swiper-img {
    width: 100upx;
    height: 100upx;
    margin-bottom: 12rpx;
}

/* 轮播圆点样式修改 */
.dots {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 10upx;
    display: flex;
    justify-content: center;
}

.dots .dot {
    margin: 0 5upx;
    width: 40upx;
    height: 6upx;
    border-radius: 20%;
    transition: all 0.6s;
}

.dots .dot.active {
    width: 40upx;
}
</style>
