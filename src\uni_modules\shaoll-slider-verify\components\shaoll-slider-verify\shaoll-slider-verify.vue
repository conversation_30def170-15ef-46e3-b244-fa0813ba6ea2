<template>
    <view :style="{backgroundColor:dBgColor,height:slideBarHeight}" class="slider-verify" @touchend="touchend">
        <view :style="{color:success?sColor:dColor,fontSize:size}" class="slider-prompt">
            {{ success ? '验证通过' : '向右滑动启动车辆 >>>>' }}
        </view>
        <!-- <view class="slider-prompt" :style="success?'color:'+sColor:'color:'+dColor">{{success?'验证通过':'拖动滑块验证'}}</view> -->
        <view :style="{'width':oldx+btnWidth+'px'}" class="slider-bg"></view>
        <movable-area :animation="true" class="slider-area">
            <movable-view :class="{'movable-btn':true,'movable-success':success}"
                          :disabled="isDisable" :style="{borderColor:success?sBgColor:dBgColor}" :x="x"
                          class="row-center-center"
                          direction="horizontal" @change="onMove">
                <image :src="slidePng" class="slider-icon" mode="aspectFit">
                </image>
            </movable-view>
        </movable-area>
    </view>
</template>

<script>
import slide from './icon-start.svg'

export default {
    props: {
        //是否禁止拖动
        disabled: {
            type: Boolean,
            default: false
        },
        //偏移量
        offset: {
            type: Number,
            default: 3
        },
        size: {
            type: String,
            default: '12px'
        },
        iconSize: {
            type: String,
            default: '14px'
        },
        slideBarHeight: {
            type: String,
            default: '144rpx'
        },
        //滑动轨道默认背景色
        dBgColor: {
            type: String,
            default: '#FFFFFF'
        },
        //滑动轨道滑过背景色
        sBgColor: {
            type: String,
            default: '#000000'
        },
        //默认文字颜色
        dColor: {
            type: String,
            default: '#242424'
        },
        //成功文字颜色
        sColor: {
            type: String,
            default: '#FFFFFF'
        }
    },
    data() {
        return {
            slidePng: slide,
            x: 0,
            oldx: 0,
            success: false, //是否验证成功
            isDisable: this.disabled,
            screenWidth: 0,
            btnWidth: 0,
        };
    },
    mounted() {
        const systemInfo = uni.getSystemInfoSync()
        this.screenWidth = systemInfo.screenWidth
        console.log(this.screenWidth)
        uni.createSelectorQuery().in(this).select(".movable-btn").boundingClientRect(data => {
            this.btnWidth = data.width
            console.log(data.width)
        }).exec()
    },
    methods: {
        // todo  需要考虑默认开关状态
        onMove(e) {
            this.oldx = e.detail.x;
        },
        touchend() {
            console.log('touchend', this.success);
            if (this.success || (this.oldx < 1 && this.oldx !== 0.1)) return
            this.x = this.oldx
            var promptW = 0
            var onTrackW = 0
            uni.createSelectorQuery().in(this).select(".slider-area").boundingClientRect(data => {
                console.log(data.width)
                if (data.width > 0) {
                    promptW = data.width
                    uni.createSelectorQuery().in(this).select(".movable-btn").boundingClientRect(data => {
                        if (data.width > 0) {
                            onTrackW = data.width
                            console.log(this.oldx, (promptW - onTrackW - this.offset));
                            if (this.oldx > (promptW - onTrackW - this.offset)) {
                                this.success = true
                                this.isDisable = true
                                this.verificationSuccess(true)
                            } else {
                                this.$nextTick(() => {
                                    this.x = 0
                                })
                                this.verificationSuccess(false)
                            }
                        }
                    }).exec()
                }
            }).exec()
        },
        verificationSuccess(state) {
            let obj = {
                state: state,
            }
            this.$emit("change", obj)
        },
        //重置初始化状态
        initialization() {
            this.x = 0
            this.success = false
            this.isDisable = false
        }
    }
}
</script>

<style scoped>
.row-center-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.slider-verify {
    position: relative;
    width: 100%;
    height: 144rpx;
    overflow: hidden;
    border-radius: 16rpx;
    padding: 16rpx;
    background: url('./evl.svg') no-repeat left 180rpx  center;
    background-size: 100% 100%;
}

.slider-prompt {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    z-index: 99;
    text-indent: 160rpx;
}

.slider-bg {
    width: 100%;
    height: 112rpx;
    border-radius: 16rpx;
    background: linear-gradient(270deg, #000 0%, #666 100%);
}

.slider-area {
    position: absolute;
    left: 16rpx;
    top: 16rpx;
    height: 112rpx;
    width: 95%;
    z-index: 100;
}

.movable-btn {
    position: absolute;
    width: 112rpx;
    height: 100%;
    box-sizing: border-box;
}

.movable-success {

}

.slider-icon {
    width: 48rpx;
    height: 48rpx;
}
</style>