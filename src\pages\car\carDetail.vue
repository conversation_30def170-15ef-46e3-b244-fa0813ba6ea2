<template>
  <view class="mainBox">
    <view class="listBox">
      <view class="listItem borderTopRadius ">SN
        <view class="nickname">{{ carInfo.sn }}</view>
      </view>
      <view class="listItem  ">IMEI
        <view class="nickname">{{ carInfo.dataPoint.imei || '未获取到' }}</view>
      </view>
      <view class="listItem  ">ICCID
        <view class="nickname">{{ carInfo.dataPoint.iccid || '未获取到' }}</view>
      </view>
      <view class="listItem  borderBottomRadius"> 版本信息
        <view class="nickname">{{ carInfo.iccid || 'v1.0' }}</view>
      </view>
    </view>
    <view class="listBox">
      <view class="listItem border borderTopRadius " @tap="toPage('/pages/car/link?id='+carInfo.id)">紧急联系人
        <view class="nickname">{{ carInfo.tel || '未设置' }}</view>
        <L-arrow></L-arrow>
      </view>
      <view class="listItem border borderTopRadius " @tap="toPage('/pages/car/collision?id=1')">碰撞告警
        <view class="nickname">{{ textList[0] }}</view>
        <L-arrow></L-arrow>
      </view>
    </view>
    <view class="listBox">
      <view class="listItem border borderTopRadius " @tap="toPage('/pages/car/Fence?id=2')">电子围栏
        <view class="nickname">{{ textList[1] }}</view>
        <L-arrow></L-arrow>
      </view>
      <view class="listItem border borderTopRadius " @tap="toPage('/pages/car/battery?id=3')">低电量告警
        <view class="nickname">{{ textList[2] }}</view>
        <L-arrow></L-arrow>
      </view>
      <view class="listItem border borderTopRadius " @tap="toPage('/pages/car/warn?id=4')">断电告警
        <view class="nickname">{{ textList[3] }}</view>
        <L-arrow></L-arrow>
      </view>
    </view>
  </view>
  <view class="btnBox borderTopRadius borderBottomRadius" @click="delDevice">
    <view class="btn">移除车辆</view>
  </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getCarInfo, unbindDevice} from "@/services/device";
import {useCarStatusStore} from '@/stores'
import {onShow} from "@dcloudio/uni-app";

const carStatusStore = useCarStatusStore()
const textList: any = ref([])
const resetText = () => {
  let list = carStatusStore.carStatus
  let arr = []
  for (let i in list) {
    let info = list[i]
    let strList = []
    if (info.id == 1) {
      if (info.flag == true) {
        if (info.info.app) {
          strList.push('推送')
        }
        if (info.info.msg) {
          strList.push('短信')
        }
        if (info.info.tel) {
          strList.push('电话')
        }
      } else {
        strList.push('未设置')
      }
      arr.push(strList.join('+'))
    } else if (info.id == 2) {
      let str = ''
      if (info.flag == true) {
        if (info.info.app) {
          strList.push('推送')
        }
        if (info.info.msg) {
          strList.push('短信')
        }
        if (info.info.tel) {
          strList.push('电话')
        }
        str = strList.join('+')
        str = str + '，范围' + info.info.meter
      } else {
        strList.push('未设置')
        str = strList.join('+')
      }
      arr.push(str)
    } else if (info.id == 3) {
      let str = ''
      if (info.flag == true) {
        if (info.info.app) {
          strList.push('推送')
        }
        if (info.info.msg) {
          strList.push('短信')
        }
        if (info.info.tel) {
          strList.push('电话')
        }
        str = strList.join('+')
        str = str + '，' + info.info.battery
      } else {
        strList.push('未设置')
        str = strList.join('+')
      }
      arr.push(str)
    } else if (info.id == 4) {
      if (info.flag == true) {
        if (info.info.app) {
          strList.push('推送')
        }
        if (info.info.msg) {
          strList.push('短信')
        }
        if (info.info.tel) {
          strList.push('电话')
        }
      } else {
        strList.push('未设置')
      }
      arr.push(strList.join('+'))
    }
  }
  textList.value = arr
}

// 获取路径参数
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
const carInfo = ref({})
// 移除设备
const delDevice = () => {
  uni.showModal({
    title: '确认移除车辆？',
    content: '移除车辆后，将无法接收到该车辆的信息',
    showCancel: true,
    confirmText: '确认',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        // 确认移除车辆
        let res = await unbindDevice(props.id)
        if (res.msg == 'success') {
		      uni.setStorageSync('carId','')
          uni.navigateBack({delta: 1})
        }
      } else if (res.cancel) {
        // 取
      }
    }
  })
}

onShow(() => {
  getCarData()
  resetText()
})
// 获取设备信息
const getCarData = async () => {
  // 获取车辆信息
  let res = await getCarInfo(props.id)
  if (res.msg == 'success') {
    carInfo.value = res.result
  }
}
// 页面跳转
const toPage = (url: string) => {
  uni.navigateTo({
    url: url
  })
}
</script>

<style lang="scss" scoped>
.mainBox {
  width: 750rpx;
}

.listBox {
  padding: 32rpx 32rpx 0;
  border-radius: 16rpx;

  .listItem {
    padding: 0 32rpx;
    background: #FFFFFF;
    height: 112rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--Text-main, #282A2E);
    font-feature-settings: 'case' on;
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Body, 30rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
    letter-spacing: var(--Letter-Spacing-Body, -0.41px);

    .nickname {
      flex: 1;
      text-align: right;
      margin-right: 10rpx;
      color: var(--Text-content, #53565C);
      font-family: var(--Font-Family, "PingFang SC");
      font-size: var(--Font-Size-Footnote, 26rpx);
      font-style: normal;
      font-weight: 400;
      line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
      letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
    }

    .avatar {
      flex: 1;
      text-align: right;
      margin-right: 10rpx;
      width: 64rpx;
      height: 64rpx;
    }
  }

  .border {
    border-bottom: 1px solid #ededed;
  }
}

.btnBox {
  position: fixed;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  padding: 32rpx;
  background: #FFFFFF;

  .btn {
    color: var(--error-u-error-dark, #E45656);
    font-feature-settings: 'case' on;
    /* Regular/Body */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Body, 30rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
    letter-spacing: var(--Letter-Spacing-Body, -0.41px);
  }
}
</style>
