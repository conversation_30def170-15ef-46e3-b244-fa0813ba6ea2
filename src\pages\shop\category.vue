<template>
    <!-- 自定义导航栏 -->
    <view class="content" :style="{height:windowHeight+ 'px'}">
        <XtxTopBar @search="searchKeyWorld" />
        <lr-scroll :scrollList="goods" v-if="goods?.length>0"></lr-scroll>
    </view>
</template>
<style>
.content{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
</style>
<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {getCategoryAllAPI} from "@/services/category";
const systemInfo = uni.getSystemInfoSync();
const windowHeight = systemInfo.windowHeight;
const goods = ref([])
onMounted(async ()=>{
    let res = await getCategoryAllAPI()
    let list = res.result
    console.log(list,res,99999)
    goods.value = list
})
const searchKeyWorld = (e: any) => {
    uni.navigateTo({
        url: '/pages/shop/sort?keyword=' + e
    })
}
</script>