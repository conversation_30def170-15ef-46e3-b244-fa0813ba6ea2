<template>
    <view>
        <!--地图信息    -->
        <map
            id="map"
            :latitude="latitude"
            :longitude="longitude"
            :markers="covers"
            :polyline="polyline"
            :scale="15.5"
            :show-compass="true"
            :style="{ width: '750rpx', height: windowHeight + 'px' }"
        ></map>
        <view v-if="playStatus == false"
              style="display: flex; justify-content: center; align-items: center; width: 750rpx; position: absolute; top: 440rpx; z-index: 99">
            <image src="../../static/imgs/car/play.png" style="width: 96rpx; height: 96rpx" @tap="start"></image>
        </view>
        <view
            style="display: flex; justify-content: center; align-items: center; width: 750rpx; position: absolute; bottom: 60rpx; z-index: 99">
            <view style="background-color: #ffffff; width: 700rpx; min-height: 478rpx; border-radius: 16rpx">
                <text class="title">{{ totalInfo.name }}</text>
                <text class="time">{{ moment(totalInfo.begin * 1000).format('YYYY-MM-DD HH:mm') }} -
                    {{ moment(totalInfo.end * 1000).format('YYYY-MM-DD HH:mm') }}
                </text>
                <view class="list">
                    <view class="item">
                        <text class="itemTitle">{{ formatSecondsToHHMMSS(totalInfo.totalTime) }}</text>
                        <text class="itemDesc" style="color: #969696">时间 h</text>
                    </view>
                    <view class="item">
                        <text class="itemTitle">{{ totalInfo.distance }}</text>
                        <text class="itemDesc" style="color: #969696">里程 km</text>
                    </view>
                    <view class="item">
                        <text class="itemTitle">{{ totalInfo.totalSpeed }}</text>
                        <text class="itemDesc" style="color: #969696">均速 km/h</text>
                    </view>
                    <view class="item">
                        <text class="itemTitle">{{ totalInfo.totalMaxSpeed }}</text>
                        <text class="itemDesc" style="color: #969696">极速 km/h</text>
                    </view>
                </view>
                <view class="topBox">
                    <view class="topInfo">
                        <view class="right">
                            <view style="display: flex;align-items: center;flex-direction: row;min-height: 32rpx">
                                <view class="redDot"></view>
                                <text class="startAddress">{{ totalInfo.fromAddress }}</text>
                            </view>
                            <view class="space"></view>
                            <view style="display: flex;align-items: center;flex-direction: row;min-height: 32rpx">
                                <view class="blueDot"></view>
                                <text class="endAddress">{{ totalInfo.toAddress }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>

import {gps2AMap} from "../../utils/location";
import dcSlider from '@/components/ly-drag-slider/dc-slider.vue';
import moment from "moment/moment";
import {calcDiv, calcMul} from "../../utils/math";

const app = getApp();

export default {
    computed: {
        moment() {
            return moment
        }
    },
    components: {
        dcSlider
    },
    data() {
        return {
            playStatus: false,
            windowHeight: 0,
            latitude: '',
            longitude: '',
            parseIdIndex: 0,
            covers: [],
            map: null,
            // 线
            polyline: [],

            // 坐标数据(从存储空间或者http下载的数据)
            coordinate: [],
            totalInfo: {
                name: '未知',
                begin: 0,
                end: 0,
                fromAddress: '',
                toAddress: '',
                totalTime: '',
                totalSpeed: '',
                totalMaxSpeed: '',
                distance: 0,
            },
            rideList: [],
            dialogTitle: '数据加载中',
            avater: 'data:image/jpg;base64,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'
        };
    },
    onLoad(options) {
        var systemInfo = uni.getSystemInfoSync();
        this.map = uni.createMapContext('map');
        this.windowHeight = systemInfo.windowHeight;
        this.totalInfo.name = options.deviceName
        //这里接收轨迹id列表
        const lineList = uni.getStorageSync(options.uuid)
        // for (let i = 0; i < 10; i++){ //伪造数据
        //   this.rideList.push(
        //       JSON.parse(lineList)[0]
        //   )
        // }
        this.rideList = JSON.parse(lineList)
        this.totalData()
        const member = JSON.parse(uni.getStorageSync('member'))
        if (member && member.userInfo) {
            let userAvatar = member.userInfo.avatar;
            if (userAvatar && userAvatar !== 'null') {
                this.avater = userAvatar + '?x-oss-process=image/circle,r_50/format,png'
            }
        }
        this.parseIdIndex = 0
        //获取数据 弹出对话框
        uni.showLoading({
            title: this.dialogTitle + '：' + this.parseIdIndex + '/' + this.rideList.length
        })
        this.getData()
    },
    onShow() {
    },
    methods: {
        /**
         * 获取本地数据 线路信息
         * @param ride
         */
        getStorageData(ride) {
            var that = this;
            //下载轨迹数据
            //根据轨迹id获取轨道数据
            //先判断本地是否有数据，如果没有去下载并存入本地
            try {
                uni.getStorage({
                    key: 'LINE-' + ride.id,
                    success: function (res) {
                        if (res.data) {
                            let value = that.base64ToArrayBuffer(res.data)
                            that.parseData(value)
                        } else {
                            that.getHttpData(ride.id, ride.fileName)
                        }
                    },
                    fail: function (e) {
                        console.log("fail :", e)
                        that.getHttpData(ride.id, ride.fileName)
                    }
                });
            } catch (e) {
                console.log("catch :", e)
                // error
                that.getHttpData(ride.id, ride.fileName)
            }
        },
        /**
         * 汇总数据
         */
        totalData() {
            let meter = 0
            let time = 0
            let begin = 0
            let end = 0
            let fromAddress = ''
            let toAddress = ''
            for (let item of this.rideList) {
                meter += item.meter
                time += item.time
                if (begin === 0) {
                    begin = item.begin
                }
                end = item.end
                if (fromAddress === '') {
                    fromAddress = item.fromAddress
                }
                toAddress = item.toAddress
            }
            this.totalInfo.begin = begin
            this.totalInfo.end = end
            this.totalInfo.totalTime = time
            let speed = calcDiv(meter, time)
            this.totalInfo.totalSpeed = calcMul(speed, 3.6).toFixed(2)
            this.totalInfo.distance = (meter / 1000).toFixed(2)
            this.totalInfo.fromAddress = fromAddress
            this.totalInfo.toAddress = toAddress

            if (this.rideList.length >= 1) {
                let toResult = gps2AMap(this.rideList[0].toLng, this.rideList[0].toLat)
                let toLat = toResult[1]
                let toLng = toResult[0]
                let fromResult = gps2AMap(this.rideList[this.rideList.length - 1].fromLng, this.rideList[this.rideList.length - 1].fromLat)
                let fromLat = fromResult[1]
                let fromLng = fromResult[0]
                this.latitude = fromLat
                this.longitude = fromLng
                console.log("latitude : " + this.latitude)
                console.log("longitude : " + this.longitude)
            }
        },
        getData() {
            if (this.rideList.length > this.parseIdIndex) {
                this.getStorageData(this.rideList[this.parseIdIndex]);
            } else {
                //获取数据结束，取消对话框
                uni.hideLoading()
                //绘制线路
                this.drawRide()
            }
        },
        drawRide() {
            var that = this;
            let data = that.coordinate; //线路列表
            const polyLineData = []; //线路数据
            const coverData = []; //线路起始点
            const maxSpeed = {
                latitude: 0,
                longitude: 0,
                speed: 0 //时速
            }
            //线路循环
            for (let playIndex = 0; playIndex < data.length; playIndex++) {
                let lines = data[playIndex]
                if (lines.length > 0) {
                    //线路坐标点
                    polyLineData.push({
                        points: [],
                        color: "#00FF00",
                        colorList: [],
                        width: 6
                    })
                    if (playIndex > 0) {
                        coverData.push({
                            id: "line" + playIndex,
                            width: 1,
                            height: 1,
                            latitude: lines[0].latitude,
                            longitude: lines[0].longitude,
                            callout: {
                                content: playIndex + 1 < 10 ? `0${playIndex + 1}` : `${playIndex + 1}`,
                                display: "ALWAYS",
                                color: "#ffffff", //文本颜色
                                fontSize: "10px",
                                bgColor: "#80838A", //背景色
                                padding: '3', //文本边缘留白
                                textAlign: "center",
                                borderRadius: "10px",
                                borderColor: "#ffffff",
                                borderWidth: "2",
                            }
                        })
                    }

                    for (let dataIndex = 0; dataIndex < lines.length; dataIndex++) {
                        polyLineData[playIndex].points.push({
                            latitude: lines[dataIndex].latitude,
                            longitude: lines[dataIndex].longitude
                        })
                        if (lines[dataIndex].speed > 0) {
                            const color = that.getSpeedColor(lines[dataIndex].speed)
                            polyLineData[playIndex].colorList.push(color)
                        }
                        if (maxSpeed.speed < lines[dataIndex].speed) {
                            maxSpeed.latitude = lines[dataIndex].latitude
                            maxSpeed.longitude = lines[dataIndex].longitude
                            maxSpeed.speed = lines[dataIndex].speed
                        }
                    }
                }
            }
            //添加头像 Marker
            if (data.length > 0 && data[0].length > 0) {
                let endLine = data[data.length - 1]
                coverData.push({
                    id: "end",
                    width: 18,
                    height: 18,
                    latitude: endLine[endLine.length - 1].latitude,
                    longitude: endLine[endLine.length - 1].longitude,
                    iconPath: '/static/imgs/car/end.png'
                })
                let line = data[0]
                coverData.push({
                    id: "start",
                    width: 18,
                    height: 18,
                    latitude: line[0].latitude,
                    longitude: line[0].longitude,
                    iconPath: '/static/imgs/car/start.png'
                })

                coverData.push({
                    id: "user",
                    width: 32,
                    height: 32,
                    latitude: line[0].latitude,
                    longitude: line[0].longitude,
                    iconPath: that.avater
                })
            }
            coverData.push({
                id: "maxSpeed",
                width: 1,
                height: 1,
                latitude: maxSpeed.latitude,
                longitude: maxSpeed.longitude,
                callout: {
                    content: maxSpeed.speed.toFixed(2) + 'KM/H',
                    display: "ALWAYS",
                    color: "#ffffff", //文本颜色
                    fontSize: "11px",
                    bgColor: "#4B85FD", //背景色
                    padding: '3', //文本边缘留白
                    textAlign: "center",
                    borderColor: "#ffffff",
                    borderWidth: "2",
                }
            })
            this.totalInfo.totalMaxSpeed = maxSpeed.speed.toFixed(2)
            this.covers = JSON.parse(JSON.stringify(coverData))
            this.polyline = JSON.parse(JSON.stringify(polyLineData))
        },
        /**
         * 去oss下载轨迹数据
         * @param lineId 线路Id
         * @param fileName 文件名
         */
        getHttpData(lineId, fileName) {
            var that = this;
            uni.request({
                url: 'https://evl.oss-cn-beijing.aliyuncs.com/' + fileName,
                responseType: 'arraybuffer',
                success: (res) => {
                    if (res.statusCode === 200) {
                        that.saveData(lineId, res.data)
                        that.parseData(res.data)
                    } else {
                        console.log("statusCode :", res.statusCode)
                        this.parseIdIndex++
                        this.getData()
                    }
                },
                fail: (err) => {
                    console.log("err :", err)
                    this.parseIdIndex++
                    this.getData()
                }
            })
        },
        /**
         * 数据缓存到本地
         * @param lineId
         * @param data
         */
        saveData(lineId, data) {
            let saveBase64 = this.arrayBufferToBase64(data)
            uni.setStorage({
                key: 'LINE-' + lineId,
                data: saveBase64,
                success: function () {
                    console.log('success');
                }
            });
        },
        /**
         * ArrayBuffer转base64
         * @param buffer
         * @returns {string}
         */
        arrayBufferToBase64(buffer) {
            let binary = '';
            const bytes = new Uint8Array(buffer);
            const len = bytes.byteLength;
            for (let i = 0; i < len; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return btoa(binary);
        },
        /**
         * base64转ArrayBuffer
         * @param base64
         * @returns {ArrayBufferLike}
         */
        base64ToArrayBuffer(base64) {
            const binaryString = atob(base64);
            const len = binaryString.length;
            const bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes.buffer;
        },
        /**
         * 解析数据
         * @param data
         */
        parseData(data) {
            const dataView = new DataView(data);
            let points = []
            //解析数据
            //校验第一位是否为f0
            // let offset = this.parseIdIndex / 1000 //test 测试统一数据的偏移量
            if (dataView.getUint8(0) === 240) {
                const pointLen = (dataView.byteLength - 2) / 12;
                let prevLat = 0
                let prevLng = 0
                let prevTs = 0
                for (let i = 0; i < pointLen; i++) {
                    const ts = dataView.getUint32(i * 12 + 2);
                    const lat = dataView.getInt32(i * 12 + 6) / 1000000;
                    const lng = dataView.getInt32(i * 12 + 10) / 1000000;
                    if (prevLng === lng && prevLat === lat) {//点一样过滤掉
                        continue
                    }
                    let speed = 0;
                    if (i > 0) {
                        //计算两点之间的距离
                        const distance = this.getDistance(prevLat, prevLng, lat, lng)
                        //计算时间差
                        const time = ts - prevTs
                        //计算速度
                        if (distance > 0 && time > 0) {
                            speed = (distance * 3600) / time
                        }
                    } else {
                        speed = -1 //第一个点
                    }
                    //把坐标从gps转为高德地图坐标
                    let result = gps2AMap(lng, lat)
                    points.push({
                        latitude: result[1],
                        longitude: result[0],
                        timestamp: ts,
                        speed: speed //时速
                    })
                    prevLat = lat
                    prevLng = lng
                    prevTs = ts
                }
            }
            this.coordinate.push(points)
            this.parseIdIndex++

            //继续查找下一个id的数据 如果id为空则开始播放
            this.getData()
        },
        /**
         * 计算两个经纬度之间的距离
         * @param lat1 第一个点的纬度
         * @param lon1 第一个点的经度
         * @param lat2 第二个点的纬度
         * @param lon2 第二个点的经度
         * @returns {number} 距离，单位为公里
         */
        getDistance(lat1, lon1, lat2, lon2) {
            const R = 6371; // 地球半径，单位为公里
            const dLat = this.deg2rad(lat2 - lat1);
            const dLon = this.deg2rad(lon2 - lon1);
            const a =
                Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            // 距离，单位为公里
            return R * c;
        },
        /**
         * 将角度转换为弧度
         * @param deg 角度
         * @returns {number} 弧度
         */
        deg2rad(deg) {
            return deg * (Math.PI / 180);
        },
        getSpeedColor(speed) {
            if (speed > 0 && speed < 5) {
                return "#FF0000"
            }
            if (speed >= 5 && speed < 15) {
                return "#FFFF00"
            }
            if (speed >= 15) {
                return "#00FF00"
            }
        },
        formatSecondsToHHMMSS(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            const formattedHours = String(hours).padStart(2, '0');
            const formattedMinutes = String(minutes).padStart(2, '0');
            const formattedSeconds = String(secs).padStart(2, '0');

            return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
        },
        start() {
            this.playStatus = true
            this.moveMarker(0)
        },
        moveMarker(index) {
            //绘制头像在线路走
            var that = this;
            if (index < that.polyline.length) {
                let duration = 1500
                if (that.polyline[index].points.length < 50) {
                    duration = 1500
                } else if (that.polyline[index].points.length < 100) {
                    duration = that.polyline[index].points.length * 30
                } else {
                    duration = 3000
                }
                that.map.moveAlong({
                    markerId: 'user',
                    path: that.polyline[index].points,
                    duration: duration,
                    autoRotate: false,
                    fail: function (err) {
                        console.log('fail', err)
                    },
                    complete: function (res) {
                        that.moveMarker(index + 1)
                    }
                })
            } else {
                this.playStatus = false
            }
        }
    }
};
</script>

<style scoped>
.title {
    width: 700rpx;
    color: #242424;
    font-family: 'SF Pro Text';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 28rpx; /* 100% */
    margin-bottom: 20rpx;
    padding: 0 32rpx;
    margin-top: 32rpx;
}

.time {
    width: 700rpx;
    color: #969696;
    font-family: 'SF Pro Text';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 24rpx; /* 100% */
    padding-bottom: 34rpx;
    border-bottom: 1px solid #ededed;
    padding-left: 32rpx;
}

.list {
    width: 700rpx;
    height: 140rpx;
    padding: 32rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    border-bottom: 1px solid #ededed;
}

.item {
    width: 140rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.itemTitle {
    color: #242424;
    text-align: center;
    font-family: DINOT;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 36rpx; /* 112.5% */
    margin-bottom: 12rpx;
}

.itemDesc {
    color: #969696;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 133.333% */
}

.topBox {
    width: 700rpx;
    padding: 32rpx;
}

.topInfo {
    display: flex;
    margin-top: 10rpx;
    flex-direction: row;
}

.left {
    width: 16rpx;
    height: 80rpx;
    margin: 10rpx 18rpx;
}

.address {
    width: 16rpx;
    height: 80rpx;
}

.right {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.startAddress {
    width: 600rpx;
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx; /* 133.333% */
    white-space: wrap;
}

.space {
    height: 50rpx;
    background: #ededed;
    width: 1px;
    margin: 0 12rpx;
}

.endAddress {
    width: 600rpx;
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx; /* 133.333% */
    white-space: wrap;
}
.redDot{
    background: #E45656;
    width: 15rpx;
    height: 15rpx;
    border-radius: 15rpx;
    margin: 0 5rpx;
}
.blueDot{
    background: #3C9CFF;
    width: 15rpx;
    height: 15rpx;
    border-radius: 15rpx;
    margin: 0 5rpx;
}
</style>
