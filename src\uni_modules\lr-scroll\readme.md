# lr-scroll
### 功能介绍：商品分类，列表左右联动



#### 使用举例
```
<view class="content">
    <lr-scroll :scrollList="goods"></lr-scroll>
</view>

.content{
    height: 100%;
}
    
```
### 参数: 

| 名称 | 类型 | 说明 |
|:------:|:-------:|:-------:|
| scrollList | object[] |商品数据|


### 插槽 Slots: 

| 名称 | 说明 |
|:------:|:-------:|
| goods | 右侧商品展示自定义插槽 |



### scrollList数据类型
```
{
    "id": 6905,
    "goods_list": [
        {
            "id": 65825,
            "content": "牛角包买一送一",
            "name": "晨间套餐",
            "images": "https://img0.baidu.com/it/u=2181227728,1888178858&fm=253&fmt=auto&app=138&f=JPEG?w=749&h=500"
        }
    ],
	"icon": "https://img2.baidu.com/it/u=3672068819,4047946089&fm=253&fmt=auto&app=138&f=JPEG?w=300&h=300",
    "name": "每日推荐"
}]
```

