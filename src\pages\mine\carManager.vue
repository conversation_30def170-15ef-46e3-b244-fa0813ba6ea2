<template>
    <u-navbar :is-back="true" title="车辆管理">
        <template v-slot:right>
            <svg class="rightIcon" fill="none" viewBox="0 0 25 24" xmlns="http://www.w3.org/2000/svg" @click="add">
                <path clip-rule="evenodd"
                      d="M23.8144 11.9998C23.8144 5.76347 18.7364 0.685547 12.5001 0.685547C6.26372 0.685547 1.18579 5.76347 1.18579 11.9998C1.18579 18.2362 6.26372 23.3141 12.5001 23.3141C18.7364 23.3141 23.8144 18.2362 23.8144 11.9998ZM2.84143 11.9997C2.84143 6.70224 7.20259 2.34108 12.5001 2.34108C17.7976 2.34108 22.1587 6.70224 22.1587 11.9997C22.1587 17.2972 17.7976 21.6584 12.5001 21.6584C7.20259 21.6584 2.84143 17.2972 2.84143 11.9997ZM13.3278 12.8277V16.414C13.3278 16.8557 12.9417 17.2393 12.5 17.2393C12.0583 17.2393 11.6722 16.8532 11.6722 16.4115V12.8252H8.08584C7.64417 12.8252 7.25802 12.439 7.25802 11.9973C7.25802 11.5557 7.64417 11.1695 8.08584 11.1695H11.6722V7.58318C11.6722 7.14152 12.0583 6.75537 12.5 6.75537C12.9417 6.75537 13.3278 7.14152 13.3278 7.58318V11.1721H16.9142C17.3558 11.1721 17.742 11.5582 17.742 11.9999C17.742 12.4415 17.3558 12.8277 16.9142 12.8277H13.3278Z"
                      fill="#909399"
                      fill-rule="evenodd"/>
            </svg>
        </template>
    </u-navbar>
    <mescroll-body :down="downOption" class="tipBox" top="0" @down="downCallback" @init="mescrollInit"
                   @up="upCallback" :key="refreshNum">
        <view v-for="item in dataList" class="tipItem" @click.stop.prevent="toDetail(item.id)">
            <view class="itemContent">
                <view class="topBox">
                    <image :src="item.carModel?.img" class="imgCar"></image>
                    <view class="infoBox">
                        <text class="textTitle">{{ item.carModel?.brand + ' ' + item.carModel?.model }}</text>
                        <view class="batteryBox">
                            <image class="imgBattery" src="../../static/imgs/mine/battery.svg"></image>
                            <text class="textBattery">{{item.extInfo.battery}}%</text>
                        </view>
                    </view>
                    <L-arrow></L-arrow>
                </view>
                <view class="divider"></view>
                <view v-if="item.userDefault==false" class="radioBox" @click.stop="radioGroupChange(0,item)">
                    <image class="radioImg" src="../../static/imgs/mine/radio.png"></image>
                    <view class="radioText">设为默认车辆</view>
                </view>
                <view v-else class="radioBox">
                    <image class="radioImg" src="../../static/imgs/mine/radioChecked.png"></image>
                    <view class="radioText">设为默认车辆</view>
                </view>
            </view>
        </view>
    </mescroll-body>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {onNavigationBarButtonTap, onPageScroll, onReachBottom} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import {getMyDevice, setDefaultDevice} from "@/services/device";

const {mescrollInit} = useMescroll(onPageScroll, onReachBottom) // 调用mescroll的hook
// 右上角按钮点击事件
onNavigationBarButtonTap(() => {
    uni.navigateTo({
        url: '/pages/index/notificationSettings'
    })
})
const radioGroupChange = async (e: any, item: any) => {
    let res = await setDefaultDevice(item.id)
    if (res.msg == 'success') {
        uni.showToast({
            icon: 'none',
            title: '设置默认车辆成功！'
        })
        refreshNum.value++
        uni.setStorageSync('carId', '')
    }
}

const refreshNum = ref(0)
const add = () => {
    uni.navigateTo({
        url: '/pages/index/addDevice'
    })
}
const toDetail = (id:any) => {
    uni.navigateTo({
        url: '/pages/car/carDetail?id='+id
    })
}
// 请求数据
const filter = ref({
    page: 1,
    size: 10,
})
// 设备列表
const dataList = ref([])

// mescroll配置
const downOption = {
    auto: false //是否在初始化后,自动执行downCallback; 默认true
}
/* mescroll配置自定义下拉刷新的回调,不使用useMescroll的downCallback */
const downCallback = (mescroll: any) => {
    filter.value.page = 1;
    getMyDevice().then((res: any) => {
        if (res.msg == 'success') {
            dataList.value = res.result
        }
        mescroll.endSuccess(); // 请求成功, 结束下拉刷新的状态 (无参)
    }).catch(() => {
        mescroll.endErr(); // 请求失败, 结束加载
    })
}

/* mescroll配置上拉加载的回调: 其中mescroll.num:当前页 从1开始, mescroll.size:每页数据条数,默认10 */
const upCallback = (mescroll: any) => {
    filter.value.page = mescroll.num;
    filter.value.size = mescroll.size;
    getMyDevice().then((res: any) => {
        if (res.msg == 'success') {
            dataList.value = res.result
        }
        mescroll.endSuccess(res.result.length); // 请求成功, 结束加载
    }).catch(() => {
        mescroll.endErr(); // 请求失败, 结束加载
    })
}
// 页面加载
onMounted(() => {
})
</script>

<style lang="scss" scoped>
.rightIcon {
    width: 48rpx;
    height: 48rpx;
    margin: 0 32rpx;
}

.topBar {
    position: fixed;
    top: 85rpx;
    right: 0;
    left: 0;
    z-index: 10;
}

.tipBox {
    padding: 32rpx;

    .tipItem {
        margin-top: 32rpx;

        .itemContent {
            width: 686rpx;
            height: 280rpx;
            flex-shrink: 0;
            border-radius: 24rpx;
            border: 2px solid var(--Border-4, #E6E6E8);
            background: var(--Background-white, #FFF);

            .topBox {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 34rpx 32rpx 33rpx 12rpx;

                .imgCar {
                    width: 169rpx;
                    height: 101rpx;
                    flex-shrink: 0;
                }

                .infoBox {
                    flex: 1;

                    .textTitle {
                        color: #333;
                        font-feature-settings: 'case' on;
                        /* Medium/Subheadline */
                        font-family: "PingFang SC";
                        font-size: 30rpx;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 40rpx; /* 133.333% */
                        letter-spacing: -0.24px;
                    }

                    .batteryBox {
                        display: flex;
                        align-items: center;
                        margin-top: 18rpx;

                        .imgBattery {
                            width: 32rpx;
                            height: 32rpx;
                            flex-shrink: 0;
                        }

                        .textBattery {
                            margin-left: 12rpx;
                            color: var(--Text-content, #53565C);
                            /* Medium/Caption1 */
                            font-family: "PingFang SC";
                            font-size: 24rpx;
                            font-style: normal;
                            font-weight: 500;
                            line-height: 32rpx; /* 133.333% */
                        }
                    }
                }
            }

            .divider {
                border-top: 1rpx dashed var(--Border-3, #D9DBDE);
            }

            .radioBtn {
                margin: 24rpx 32rpx;
            }

        }
    }
}

.radioBox {
    display: flex;
    align-items: center;
    padding: 32rpx;

    .radioImg {
        width: 24px;
        height: 24px;
        border-radius: 50px;
        margin-right: 16rpx;
    }

    .radioText {
        color: var(--Text-content, #53565C);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption2, 22rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
        letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
    }
}
</style>
