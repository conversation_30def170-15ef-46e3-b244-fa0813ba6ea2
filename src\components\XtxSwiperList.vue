<script lang="ts" setup>
import {ref,onMounted} from 'vue'
const activeIndex = ref(0)

// 当 swiper 下标发生变化时触发
const onChange = (ev: any) => {
    activeIndex.value = ev.detail.current
}
const props = defineProps({
    list: {
        type: Array,
        default: () => [ ]
    }
})
const bannerList:any = ref([])
onMounted(()=>{
    if(props.list){
        bannerList.value = props.list
    }
})
</script>

<template>
    <view class="carousel">
        <view v-for="item in bannerList" :key="item.id" class="carouselItem">
            <navigator :url="item.hrefUrl" hover-class="none" class="imgBox">
                <image :src="item.imgUrl" class="banner"></image>
            </navigator>
        </view>
    </view>
</template>

<style lang="scss" scoped>
.carousel{
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding:32rpx;
    .carouselItem{
        width: 158rpx;
        height: 158rpx;
        flex-shrink: 0;
        .imgBox{
            width: 158rpx;
            height: 158rpx;
            .banner{
                width: 158rpx;
                height: 158rpx
            }
        }
    }
}
</style>
