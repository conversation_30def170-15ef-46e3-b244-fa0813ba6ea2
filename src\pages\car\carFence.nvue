<template>
    <view>
        <map id="map1" :circles="circles" :latitude="centerInfo.latitude" :longitude="centerInfo.longitude"
             :markers="markers" :style="{ width: '750rpx', height: windowHeight + 'px' }" class="map1"></map>
        <view style="width: 750rpx;min-height: 354rpx; position: absolute; bottom: 64rpx; z-index: 99;">
            <view
                style="width: 702rpx;  display: flex;flex-direction: row;padding:0 0 20rpx;justify-content: space-between;margin-left: 24rpx;">
                <view class="iconBox">
                    <image class="icon" mode="aspectFit" src="../../static/imgs/car/motor_location.png"
                           @tap="centerCar">
                    </image>
                </view>
                <view class="iconBox">
                    <image class="icon" mode="aspectFit" src="../../static/imgs/car/refresh.png" @tap="resetData">
                    </image>
                </view>
                <view style="flex:1"></view>
                <view class="iconNav">
                    <image class="icon" mode="aspectFit" src="../../static/imgs/car/nav.png" @tap="navLine">
                    </image>
                </view>
            </view>
            <view class="whiteBox">
                <text class="title">{{ carInfo.carModel?.brand }} {{
                        carInfo.carModel?.model
                    }}
                </text>
                <text class="time">上报时间：{{
                        formatSecondsToHHMMSS(carInfo.lastPoint?.ts * 1000)
                    }}
                </text>
                <text class="border"></text>
                <view class="locationBox">
                    <text class="location">车辆位置：</text>
                    <text class="text">{{ address }}
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import moment from "moment/moment";
import {gps2AMap} from "../../utils/location";

export default {
    data() {
        return {
            centerInfo: {},
            carInfo: {},
            latitude: 39.925539,
            longitude: 116.279037,
            windowHeight: 0,
            deviceId: '',
            carLocation: {},
            circles: [],
            markers: [],
            address: '',
            markerList: [{}],
        }
    },
    onLoad(options) {
        this.deviceId = options.deviceId;
        let systemInfo = uni.getSystemInfoSync();
        this.map = uni.createMapContext('map');
        this.windowHeight = systemInfo.windowHeight;
        setTimeout(() => {
            this.resetData()
        }, 200)
    },
    onReady() {
        this.mapContext = uni.createMapContext("map1", this);
    },
    methods: {
        async resetData() {
            let that = this
            let res = await that.getCarInfo(that.deviceId)
            if (res.msg == 'success') {
                that.carInfo = res.result
                if (res.result.lastPoint) {
                    let result = gps2AMap(res.result.lastPoint.lng, res.result.lastPoint.lat)
                    let lat = result[1]
                    let lng = result[0]
                    that.carLocation = {
                        longitude: lng,
                        latitude: lat
                    }
                    that.centerInfo = {
                        longitude: lng,
                        latitude: lat
                    }
                    await that.getAddress(lat, lng);
                    that.circles = [{
                        latitude: lat,
                        longitude: lng,
                        fillColor: '#4B85FD1A',
                        radius: 80,
                        strokeWidth: 2,
                        color: '#4B85FD26'
                    }]
                    that.markerList[0] = {
                        latitude: lat,
                        longitude: lng,
                        id: "car",
                        iconPath: '../../static/imgs/car/location-car.png',
                        width: 30,
                        height: 30
                    }
                    that.markers = JSON.parse(JSON.stringify(that.markerList))
                }
            }
        },
        getCarInfo(deviceId) {
            let profile = JSON.parse(uni.getStorageSync('member'))
            profile = profile.profile
            return new Promise((resolve, reject) => {
                uni.request({
                    url: `http://evl.usr.86.ltd/api/v1/me/device/${deviceId}`,
                    methods: "GET",//仅为示例，并非真实接口地址。
                    header: {
                        'content-type': 'application/json', // 默认值
                        'Authorization': profile.token
                    },
                    success: async (res) => {
                        res = res.data
                        resolve(res)
                    },
                    fail: (err) => {
                        console.log(err)
                    }
                });
            })
        },
        formatSecondsToHHMMSS(time) {
            return moment(time).format('YYYY-MM-DD HH:mm:ss')
        },
        centerCar() {
            this.carLocation.longitude = this.carLocation.longitude + 0.00000000000001
            this.centerInfo = this.carLocation
        },
        async getAddress(lat, lng) {
            let that = this
            const apiKey = 'a3d415243ae2185eb0f16452497e3867'; // 替换为你的高德地图API Key
            this.address = '地址获取中';
            const formatted_address = uni.getStorageSync(lat + '-' + lng)
            if (formatted_address) {
                that.address = formatted_address
                return
            }
            uni.request({
                url: `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${lng},${lat}&key=${apiKey}&radius=1000&extensions=all `,
                methods: "GET",//仅为示例，并非真实接口地址。
                success: (res) => {
                    uni.setStorageSync(lat + '-' + lng, res.data.regeocode.formatted_address)
                    that.address = res.data.regeocode.formatted_address;
                },
                fail: (err) => {
                    console.log(err)
                }
            });
        },
        navLine() {
            let latitude = this.centerInfo.latitude;
            let longitude = this.centerInfo.longitude;
            let name = this.address
            let url = "";
            if (plus.os.name == "Android") {//判断是安卓端
                plus.nativeUI.actionSheet({//选择菜单
                    title: "选择地图应用",
                    cancel: "取消",
                    buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
                }, function (e) {
                    switch (e.index) {
                        case 1:
                            url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                            break;
                        case 2:
                            url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&coord_type=gcj02&src=andr.baidu.openAPIdemo`;
                            break;
                        case 3:
                            url = `androidamap://viewMap?sourceApplication=appname&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                            break;
                        default:
                            break;
                    }
                    if (url != "") {
                        url = encodeURI(url);
                        plus.runtime.openURL(url, function (e) {
                            plus.nativeUI.alert("本机未安装指定的地图应用");
                        });
                    }
                })
            } else {
                plus.nativeUI.actionSheet({
                    title: "选择地图应用",
                    cancel: "取消",
                    buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
                }, function (e) {
                    switch (e.index) {
                        case 1:
                            url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                            break;
                        case 2:
                            url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&content=${name}&src=ios.baidu.openAPIdemo&coord_type=gcj02`;
                            break;
                        case 3:
                            url = `iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                            break;
                        default:
                            break;
                    }
                    if (url != "") {
                        url = encodeURI(url);
                        plus.runtime.openURL(url, function (e) {
                            plus.nativeUI.alert("本机未安装指定的地图应用");
                        });
                    }
                })
            }

        },

    }
}
</script>

<style scoped>
.iconBox {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 64rpx;
    height: 64rpx;
    margin-right: 23rpx;
    border-radius: 16rpx;
    background: #ffffff;
    box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);
}

.iconNav {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 64rpx;
    height: 64rpx;
    border-radius: 16rpx;
    background: #ffffff;
    box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);
}

.icon {
    width: 64rpx;
    height: 64rpx;
}

.whiteBox {
    width: 702rpx;
    min-height: 240rpx;
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);
    margin-left: 24rpx;
}

.title {
    margin-top: 32rpx;
    margin-left: 32rpx;
    margin-bottom: 32rpx;
    color: #242424;
    font-family: "SF Pro Text";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 24rpx;
    height: 30rpx;
}

.time {
    margin-left: 32rpx;
    margin-bottom: 32rpx;
    color: #969696;
    font-family: "SF Pro Text";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 24rpx; /* 100% */
    height: 30rpx;
}

.border {
    width: 702rpx;
    height: 1px;
    flex-shrink: 0;
    border-radius: 8rpx;
    background: #F2F2F2;
}

.locationBox {
    margin: 32rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.location {
    color: #969696;
    font-family: "SF Pro";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 36rpx; /* 150% */
    min-height: 40rpx;
}

.text {
    display: inline-block;
    width: 520rpx;
    white-space: wrap;
    color: #242424;
    font-family: "SF Pro";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 36rpx; /* 150% */
}
</style>
