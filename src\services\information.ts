import { http } from '@/utils/http'
// 获取消息列表
// todo  假接口
export const getInformationList = (sn:any): any => {
    return http({
        url: `/api/v1/me/deviceAlarm/${sn}`,
        method: 'GET',
    })
}
export const readInformation = (id:any): any => {
    return http({
        url: `/api/v1/me/deviceAlarm/read/${id}`,
        method: 'PUT',
    })
}
export const readAllInformation = (sn:any): any => {
    return http({
        url: `/api/v1/me/deviceAlarm/readAll/${sn}`,
        method: 'PUT',
    })
}
