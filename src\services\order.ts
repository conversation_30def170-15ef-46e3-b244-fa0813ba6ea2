const shopURL = 'https://shop.evl.usr.86.ltd/srv'
import { http } from '@/utils/http'

/*
params: {
  orderState:0,
  page:1,
  pageSize:2
}
*/
export const getOrderReviewDetailAPI = (goodsReviewId: any) => {
  return http({
    url: shopURL+'/v1/me/goodsReview/' + goodsReviewId,
    method: 'GET',
  })
}

export const getOrderReviewAPI = (params: any) => {
  return http({
    url: shopURL+'/v1/me/order/goodsReview',
    method: 'POST',
    data: params,
  })
}


export const getSkuAPI = (orderId: any) => {
  return http({
    url:shopURL+ `/v1/sku/${orderId}`,
    method: 'GET',
  })
}

export const getGoodDetailAPI = (data: any) => {
  return http<any>({
    method: 'GET',
    url: shopURL+`/v1/goods/${data.id}`,
  })
}
export const getShippingDetailAPI = (id: any) => {
  return http<any>({
    url: shopURL+`/v1/shipping/${id}`,
    method: 'GET',
  })
}

export const createOrderAPI = (data: any) => {
  return http<any>({
    url: shopURL+'/v1/me/order',
    method: 'POST',
    data,
  })
}

export const cancelOrderAPI = (orderId: any) => {
  return http<any>({
    url: shopURL+`/v1/me/order/cancel/${orderId}`,
    method: 'PUT',
  })
}
export const confirmOrderAPI = (orderId: any) => {
  return http<any>({
    url: shopURL+`/v1/me/order/finish/${orderId}`,
    method: 'PUT',
  })
}
export const getUserOrder = (data: any) => {
  return http<any>({
    url:  shopURL+'/v1/me/order',
    method: 'GET',
    data,
  })
}
export const returnNumberAPI = (data: any) => {
  return http<any>({
    url: shopURL+`/v1/me/order/returnNumber/${data.refundSupportId}`,
    method: 'PUT',
    data
  })
}
export const refundOrderAPI = (params: any) => {
  return http<any>({
    url: shopURL+`/v1/me/order/refund/${params.orderId}`,
    method: 'POST',
    data:params
  })
}
export const getOrderDetailAPI = (orderId: any) => {
  return http<any>({
    url: shopURL+`/v1/me/order/${orderId}`,
    method: 'GET',
  })
}


