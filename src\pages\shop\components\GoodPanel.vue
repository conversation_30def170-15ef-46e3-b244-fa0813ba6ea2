<script lang="ts" setup>
import {ref,onMounted} from 'vue'
import GoodItem from './GoodListItem.vue'
// 定义 props 接收数据
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    type: {
        type: Number,
        default: 0,
    },
    collectId:{
        type: Number,
        default: '',
    },
})
const goodList:any = ref([])
onMounted(()=>{
    console.log(props.list)
    if(props.list){
        goodList.value = props.list
    }
})
const toList = () => {
    uni.navigateTo({
        url: '/pages/shop/goodList?id='+props.collectId+'&title='+(props.type == 0 ? '正在热销' : '为你推荐')
    })
}
</script>

<template>
    <!-- 推荐专区 -->
    <view class="panel hot">
        <view class="topBox" @tap="toList">
            <view class="HotTitle">{{ type == 0 ? '正在热销' : '为你推荐' }}
                <image src="../imgs/bg.svg" class="bg"></image>
            </view>
            <view class="hotDesc">更多</view>
            <L-arrow></L-arrow>
        </view>
        <view class="goodList">
                <GoodItem :orderGood="item" v-for="item in goodList" :key="item.id" class="goodItem"></GoodItem>
        </view>
    </view>
</template>

<style lang="scss" scoped>
/* 热门推荐 */
.hot {
    margin-top: 40rpx;
    padding: 0 32rpx;
    .HotTitle {
        color: #242424;
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height:32rpx;
        position: relative;
        height: 32rpx;
        .bg{
            position: absolute;
            top: -8rpx;
            left:-8rpx;
            width: 25rpx;
            height: 25rpx;
            flex-shrink: 0;
        }
    }
    .goodList {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
}

.topBox{
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    .hotDesc{
        flex:1;
        text-align: right;
        color: var(--Text-tips, #909399);
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 28rpx; /* 116.667% */
    }
}
</style>
