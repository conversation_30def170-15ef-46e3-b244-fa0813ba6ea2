function upload(filePath: string, option: any, callBack: (result: any) => void = () => {}) {
    console.log("upload",option)
    const buildUrl = `https://${option.bucket}.${option.region}.aliyuncs.com`;
    const filed = option.savaPath + option.fileName;
    const formData = {
        OSSAccessKeyId: option.OSSAccessKeyId,
        signature: option.Signature,
        policy: option.policy,
        key:filed,
        success_action_status: 200
    }
    // 文件类型支持图片、视频、音频（ image / video / audio）
    return uni.uploadFile({
        url: "https://" + option.bucket +
            "." + option.region + ".aliyuncs.com",
        filePath: filePath,
        name: "file",
        formData: formData,
        success: (res) => {
            let ossFileName:any = false;
            if (res.statusCode === 200) {
                ossFileName = buildUrl +'/'+filed
            }
            callBack(ossFileName);
        },
        fail: err => {
            console.error("error",err)
            callBack(false);
        }
    })

}
export default {
    upload,
}