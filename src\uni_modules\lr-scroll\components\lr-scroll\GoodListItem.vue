<template>
    <view class="goodBox">
        <view class="goodItem" @click="toDetail(orderGood.id)" v-for="orderGood in list">
            <view class="imgBox">
                <image :src="orderGood.picture" alt="" class="imgItem"/>
            </view>
            <view class="infoBox">
                <view class="name ellipsis-2">
                    {{ orderGood.name }}
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getGoodsAPI} from "@/services/goods";
onMounted(()=>{
    console.log(props.categoryId)
    getCateGoryGoodList()
})
const props = defineProps({
    categoryId: {
        type: Object,
        required: true,
    },
})
const  list = ref([])
const getCateGoryGoodList = async () => {
let res = await  getGoodsAPI({categoryId: props.categoryId,page:1,size:100})
    let arr = res.result.rows
    list.value = arr
    console.log(arr)
}
const toDetail = (id: any) => {
    uni.navigateTo({
        url: '/pages/shop/detail?id=' + id,
    })
}
</script>

<style lang="scss" scoped>
.goodBox {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, minmax(146rpx, 1fr));
    gap: 20rpx;
    justify-content: flex-start;
}

.goodItem {
    width: 146rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 20rpx;
}
.imgBox{
    width:    146rpx;
    height:    146rpx;
    margin-bottom: 10rpx;
}
.imgItem {
    width:    146rpx;
    height:  146rpx;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
}


.infoBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-top: 16rpx;
    margin-bottom: 16rpx;

    .name {
        width: 100%;
        color: #242424;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 24rpx; /* 100% */
        text-align: center;
    }

    .ellipsis-2 {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.priceBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    .price {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .new {
            color: #E45656;
            font-family: "SF Pro Display";
            font-size: 28rpx;
            font-weight: 600;
            line-height: 100%; /* 20px */
            letter-spacing: 0.2rpx;
        }

        .oldPrice {
            margin-left: 16px;
            color: #666;
            font-family: "SF Pro Display";
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 20px */
            letter-spacing: 0.2rpx;
            text-decoration: line-through;
        }
    }
}

</style>
