import { http } from '@/utils/http'
const shopURL = 'https://shop.evl.usr.86.ltd/srv'
// 获取集合列表
export const getCollectAPI = (params: any): any => {
  return http({
    url: '/v1/collect',
    method: 'GET',
    data: params,
  })
}
export const getHotGoodsAPI = ({
  collectId,
  page,
  size,
}: {
  collectId: string
  page: number
  size: number
}): any => {
  return http({
    url: shopURL+'/v1/collectGoods',
    data: {
      collectId,
      page,
      size,
    },
  })
}
