import { http } from '@/utils/http'
// 登录/注册
export const loginAPI = (params: any): any => {
    return http({
        url: '/api/auth/signin',
        method: 'POST',
        data: params,
    })
}
// 获取验证码
export const getCodeAPI = (params: any): any => {
    return http({
        url: '/api/auth/tel/code',
        method: 'POST',
        data: params,
    })
}
// 验证码登录/注册
export const codeLoginAPI = (params: any): any => {
    return http({
        url: '/api/auth/tel/sinInOrUp',
        method: 'POST',
        data: params,
    })
}
// 忘记密码
export const resetPasswordAPI = (params: any): any => {
    return http({
        url: '/api/auth/tel/password',
        method: 'PUT',
        data: params,
    })
}



