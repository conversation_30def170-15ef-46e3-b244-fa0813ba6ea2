import {http} from '@/utils/http'
// 获取我的设备
export const getMyDevice = (): any => {
    return http({
        url: '/api/v1/me/device', method: 'GET',
    })
}
// 增加设备
export const addDeviceSn = (data: any): any => {
    return http({
        url: '/api/v1/me/device/bind', method: 'PUT', data
    })
}
// 解绑设备
export const unbindDevice = (deviceId: any): any => {
    return http({
        url: `/api/v1/me/device/unbind/${deviceId}`, method: 'PUT',
    })
}
// 获取设备信息
export const getCarInfo = (deviceId: any): any => {
    return http({
        url: `/api/v1/me/device/${deviceId}`, method: 'GET',
    })
}
// 获取行程信息
export const getRideRecord = (data: any): any => {
    const {deviceId} = data
    delete data.deviceId
    return http({
        url: `/api/v1/me/rideRecord/${deviceId}`, method: 'GET', data
    })
}
export const getDeviceCharge = (data: any): any => {
    const {deviceId} = data
    delete data.deviceId
    return http({
        url: `/api/v1/me/deviceCharge/${deviceId}`, method: 'GET', data
    })
}
// 获取当日行程信息
export const getRideRecordToday = (deviceId: any): any => {
    return http({
        url: `/api/v1/me/device/daySummay/${deviceId}`, method: 'GET',
    })
}
// 设为默认设备
export const setDefaultDevice = (deviceId: any): any => {
    return http({
        url: `/api/v1/me/device/setDefault/${deviceId}`, method: 'PUT',
    })
}

export const getBrandAPI = (data: any): any => {
    return http({
        url: `/api/v1/me/brand`, method: 'GET',data
    })
}
export const getModelAPI = (data: any): any => {
    return http({
        url: `/api/v1/me/carModel`, method: 'GET',data
    })
}
//  获取设备信息
export const getCarInfoBySn = (sn:any): any => {
// 获取当日行程信息
        return http({
            url: `/api/v1/me/deviceSn/${sn}`,
            method: 'GET',
        })
}

export const getDeviceConsume = (deviceId:any): any => {
        return http({
            url: `/api/v1/me/deviceConsume/${deviceId}`,
            method: 'GET',
        })
}
export const getDeviceEnergy = (deviceId:any): any => {
        return http({
            url: `/api/v1/me/deviceEnergy/${deviceId}`,
            method: 'GET',
        })
}
export const getDeviceTel = (data:any): any => {
        return http({
            url: `/api/v1/me/device/tel/${data.deviceId}`,
            method: 'PUT',
            data
        })
}
// 发送设备指令
export const deviceCmd = (deviceId: any,params: any): any => {
    return http({
        url: `/api/v1/me/device/cmd/${deviceId}`,
        method: 'PUT',
        data: params,
    })
}


