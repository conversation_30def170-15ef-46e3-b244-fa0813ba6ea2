<template>
    <view class="content">
        <l-echart ref="lineChart" class="line-chart"></l-echart>
    </view>
</template>

<script>
import * as echarts from 'echarts/core';
import {LineChart} from 'echarts/charts';
import {GridComponent, TooltipComponent,} from 'echarts/components';
import {CanvasRenderer} from 'echarts/renderers';
import {calcDiv} from "@/utils/math";
// 注册必须的组件
echarts.use([
    TooltipComponent,
    GridComponent,
    LineChart,
    CanvasRenderer
]);
export default {
    data() {
        return {}
    },
    // 接收参数
    props: {
        consumeList: {
            type: Array,
            default: () => ([])
        }
    },
    // 动态处理consumeList,deep
    watch: {
        consumeList: {
            handler(newVal) {
                this.loadLineData(newVal)
            },
        }
    },
    mounted() {
        //加载柱状图数据--示例1
        this.loadLineData(this.consumeList)
    },
    methods: {
        //加载折线图数据
        loadLineData(list) {
            // list  提取consume 组成新数组
            let yData = list.map(item => item.consume)
            let xData = list.map(item =>  calcDiv(item.meter,1000))
            if(list.length == 0){
                yData=[6.5,6.5,6.5,6.5,6.5,6.5,6.5,6.5,6.5,6.5]
                xData=[2,3,4,5,6,7,8,9,0,1]
            }
            //这里请求服务器拿到数据
            let res = {
                xData,
                yData
            }
            console.log(res, 'chart')
            //这里option配置参考文档：https://echarts.apache.org/zh/option.html
            let option = {
                xAxis: {
                    type: 'category',
                  // // x轴数据文字颜色
                    axisLabel: {
                        show: false // 隐藏x轴文字
                    },
                    //x轴上面刻度线隐藏
                    axisTick: {
                        show: false,
                    },
                    //这里是x轴数据
                },
                //设置网格
                grid: {
                    top: 10,
                    bottom: 10,
                    left:0,
                    right:0
                },
                //y轴设置
                yAxis: {
                    type: 'value',
                    // y轴标签文字颜色
                    axisLabel: {
                        show: false // 隐藏y轴文字
                    },
                    // // y轴分割线设置为虚线
                    splitLine: {
                        show: true,
                    },
                    min:4,
                    max:8
                },
                //设置曲线的颜色
                color: ['#2b85e4'],
                series: [{
                    //这里是数据
                    data: res.yData,
                    type: 'line',
                }]
            };

            this.$refs.lineChart.init(echarts, chart => {
                chart.setOption(option);
            });
        },
    },
}
</script>

<style lang="scss" scoped>
.content {
    width: 284rpx;
    height: 210rpx;
}
</style>
