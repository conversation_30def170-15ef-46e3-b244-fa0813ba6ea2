<template>
    <div class="carBox" >
                <mescroll-empty v-if="deviceList.length == 0"></mescroll-empty>
        <view v-for="(item,index) in deviceList" class="carItem borderTopRadius borderBottomRadius">
            <view class="carImg">
                <image :src="item.carModel.img"></image>
            </view>
            <view class="carContent">
                <view class="carTitle">{{item.carModel.brand}}{{item.carModel.model}}</view>
                <view class="carUnit">
                    <image class="icon" src="../../static/imgs/index/battery.png"></image>
                    {{item.extInfo.battery}}%
                </view>
            </view>
            <view class="carForm">
                <u-switch v-model="item.flag" @change="dealChange($event,index)"></u-switch>
            </view>
        </view>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getMyDevice} from "@/services/device";
import { watch } from 'vue';
const mainInfo:any = ref({})
import {useCarStatusStore} from '@/stores'
const carStatusStore = useCarStatusStore()
let list = carStatusStore.carStatus
const mainList = ref(list)
for(let i in list ){
    if(list[i].id==5){
        mainInfo.value = list[i]
    }
}
const deviceList = ref([])
// 获取页面初始列表
const getDataList = async () => {
    const deviceRes = await getMyDevice()
    console.log(mainInfo.value.info)
    let arr = mainInfo.value.info.carList
    if (deviceRes.msg == 'success') {
        let list = deviceRes.result
        list.map((item:any) => {
            item.flag = false
        })
        for(let i in list){
            for(let n in arr){
                if(arr[n] == list[i].id){
                    list[i].flag = true
                }
            }
        }
        deviceList.value = list
    }
}
const dealChange = (e:any,index:number) => {
    console.log(e,index)
    let arr = mainInfo.value.info.carList
    // 判断 deviceList.value[index].id 是否存在，不存在添加，存在删除
    if(arr.indexOf(deviceList.value[index].id) == -1){
        arr.push(deviceList.value[index].id)
    }else{
        arr.splice(arr.indexOf(deviceList.value[index].id),1)
    }
    arr= []
    console.log()

}
// 页面加载
onMounted(() => {
    getDataList()
})
watch(()=>mainInfo.value,()=>{
    mainList.value[4] = mainInfo.value
    carStatusStore.setCarStatus(mainList.value)
},{
    deep:true
})
</script>

<style lang="scss" scoped>
.carBox {
    width: 750rpx;
    padding: 32rpx;

    .carItem {
        background: #FFFFFF;
        padding: 20rpx;
        height: 176rpx;
        margin-bottom: 32rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .carImg {
            width: 170rpx;
            height: 101rpx;
            flex-shrink: 0;

            image {
                width: 170rpx;
                height: 101rpx;
            }
        }

        .carContent {
            flex: 1;
            margin-left: 32rpx;

            .carTitle {
                color: #333;
                font-feature-settings: 'case' on;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 40rpx; /* 133.333% */
                letter-spacing: -0.24px;
                margin-bottom: 18rpx;
            }

            .carUnit {
                color: var(--Text-content, #53565C);
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                display: flex;
                align-items: center;
                .icon {
                    width: 32rpx;
                    height: 32rpx;
                    flex-shrink: 0;
                    margin-right: 20rpx;
                }
            }
        }

        .carForm {

        }
    }
}
</style>
