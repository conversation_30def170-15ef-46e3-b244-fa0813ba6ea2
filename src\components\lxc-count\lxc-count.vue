<template>
    <view :class="status ? 'count-box-light' : 'count-box-gray'" class="count-box">
        <view :class="[myValue <= min ? 'light' : 'gray']" class="count-less count-pub" @longpress='longpressLess'
              @touchend="handletouchend" @tap.stop="less">-
        </view>
        <input v-model="myValue" class="count-input" type="number" @blur="onBlue" @focus="onFocus"/>
        <view :class="[myValue >= max ? 'light' : 'gray']" class="count-add count-pub" @longpress='longpressAdd'
              @touchend="handletouchend" @tap.stop="add">+
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            myValue: 0,
            status: false,
            timer: null
        }
    },
    props: {
        // 计数器中的值
        value: {
            type: Number,
            default: 0
        },
        max: {
            type: Number,
            default: 10000
        },
        min: {
            type: Number,
            default: 0
        },
        // 点击当前数据的索引
        index: {
            type: Number
        },
        delayed: {
            type: Number,
            default: 200
        }
    },
    created() {
        this.myValue = this.value
    },
    watch: {
        value(val) {
            this.myValue = val
        }
    },
    methods: {
        onBlue() {
            if (+this.myValue >= this.max) {
                this.myValue = this.max
                this.status = false
            } else if (+this.myValue <= this.min) {
                this.myValue = this.min
                this.status = false
            } else {
                this.status = true
                this.myValue = +this.myValue
            }
            if (!isNaN(this.myValue)) {
                this.$emit('handleCount', this.myValue, this.index)
            } else {
                this.$emit('handleCount', 0, this.index)
            }

        },
        onFocus() {
            this.status = true
        },
        add() {
            this.addPublick()
        },
        addPublick() {
            if (this.myValue >= this.max) {
                this.status = false
                this.myValue = this.max
                clearInterval(this.timer)
            } else {
                this.status = true
                this.myValue++
            }
            this.$emit('handleCount', this.myValue, this.index)
        },
        longpressAdd() {
            this.timer = setInterval(() => {
                this.addPublick()
            }, this.delayed)
        },
        less() {
            this.lessPublick()
        },
        lessPublick() {
            if (this.myValue <= this.min) {
                clearInterval(this.timer)
                this.status = false
                this.myValue = this.min
            } else {
                this.status = true
                this.myValue--
            }
            this.$emit('handleCount', this.myValue, this.index)
        },
        longpressLess() {
            this.timer = setInterval(() => {
                this.lessPublick()
            }, this.delayed)
        },
        handletouchend() {
            clearInterval(this.timer)
        }
    }
}
</script>
<style>
.count-box {
    position: relative;
    display: flex;
    width: 216rpx;
    height: 60rpx;
    justify-content: space-between;
    align-items: center;
    gap: 22rpx;
    border-radius: 72rpx;
    border: 2rpx solid #EDEDED;
}

.count-pub {
    width: 80rpx;
    width: 80rpx;
    height: 100%;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.count-less {
    left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.count-add {
    right: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.count-input {
    flex: 1;
    height: 100%;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    padding: 6rpx 10rpx;
    box-sizing: border-box;
    color: #808080;
    font-size: 26rpx;
    text-align: center;
}
</style>
