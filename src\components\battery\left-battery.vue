<template>
  <view class="battery">
    <view class="active" :style="{width: battery+'%',background:bgColor}">
      <image v-show="battery>=50" class="batteryIcon" src="../../static/imgs/car/icon-light.svg"></image>
    </view>
    <view class="batteryNum" :style="{color: battery < 30 ? '#282A2E' : '#FFF'}">{{ battery }}
      <text :style="{color: battery < 30 ? '#282A2E' : '#FFF'}">%</text>
    </view>
    <image v-show="battery<50" class="rightIcon" :src="battery<30?'../../static/imgs/car/icon-light-r.svg':'../../static/imgs/car/icon-light-y.svg'"></image>
  </view>
</template>

<script setup>
import {computed} from "vue";

const props = defineProps({
  battery: {
    type: Number,
    default: () => ({})
  }
})
const bgColor = computed(() => {
  if(props.battery<30){
    return '#FF7070'
  }else if (props.battery >=30 && props.battery <50){
    return '#E5A23C'
  }else{
    return '#34A853'
  }
})
</script>


<style scoped lang="scss">
.battery {
  position: relative;
  width: 384rpx;
  height: 80rpx;
  flex-shrink: 0;
  background: #EFEFEF;
  border-radius: 16rpx;
  overflow: hidden;

  .active {
    height: 80rpx;
    flex-shrink: 0;
    background: #34A853;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .batteryIcon {
      width: 16rpx;
      height: 28rpx;
      flex-shrink: 0;
        margin-right: 10rpx;
    }
  }

  .batteryNum {
    position: absolute;
    left: 22rpx;
    top: 12rpx;
    color: #FFF;
    font-family: "DIN Alternate";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 100%; /* 28px */
    letter-spacing: 0.28px;
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;

    text {
      color: #FFF;
      text-align: right;
      font-family: "DIN Alternate";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
  }
  .rightIcon {
    position: absolute;
    left: 342rpx;
    top: 26rpx;
    width: 16rpx;
    height: 28rpx;
    flex-shrink: 0;
  }
}
</style>
