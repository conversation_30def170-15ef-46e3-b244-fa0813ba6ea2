<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getMoneyOutAPi, getUserMoneyInfoAPI} from '@/services/pay'
import moment from "moment";
import {formatDate} from "@/utils/date";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import {onPageScroll, onReachBottom} from "@dcloudio/uni-app";

const total = ref(0)
const size = ref(10)
const userAddress = ref([])
const page = ref(1)
const pageChange = (e) => {
    page.value = e.current
    getChargeList()
}
const typeStr: any = ref(2)
// 地址
const getChargeList = async () => {
    try {
        const res = await getMoneyOutAPi(walletId.value, {
            page: page.value,
            size: size.value,
            type: typeStr.value
        })
        userAddress.value = res.result.rows || []
        total.value = res.result.count
    } catch (error) {
        console.log(error)
    }
}
const walletId = ref('')
const userInfo = ref({})
onMounted(async () => {
    const res = await getUserMoneyInfoAPI()
    userInfo.value = res.result
    walletId.value = res.result.id
    getChargeList()

})
const formatTime = (time) => {
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
const changeType = (type: any) => {
    typeStr.value = type
    page.value = 1
    getChargeList()
}
// mescroll配置
const downOption = {
    auto: false //是否在初始化后,自动执行downCallback; 默认true
}

/* mescroll配置自定义下拉刷新的回调,不使用useMescroll的downCallback */
const downCallback = async (mescroll: any) => {
    page.value= 1
    userAddress.value = []
    const res = await getMoneyOutAPi(walletId.value, {
        page: page.value,
        size: size.value,
        type: typeStr.value
    })
    if(res.msg=='success'){
        userAddress.value = res.result.rows || []
        mescroll.endSuccess(); // 请求成功, 结束下拉刷新的状态 (无参)
    }else{
        mescroll.endErr(); // 请求失败, 结束加载
    }
}

/* mescroll配置上拉加载的回调: 其中mescroll.num:当前页 从1开始, mescroll.size:每页数据条数,默认10 */
const upCallback = async (mescroll: any) => {
    page.value= mescroll.num;
    const res = await getMoneyOutAPi(walletId.value, {
        page: page.value,
        size: size.value,
        type: typeStr.value
    })
    if(res.msg=='success'){
        if(mescroll.num==1){
            userAddress.value = res.result.rows || []
        }else{
            userAddress.value =   userAddress.value.concat(res.result.rows)
        }
        console.log('userAddress',userAddress.value.length,res.result.count)
        mescroll.endSuccess(res.result.count); // 请求成功, 结束加载
    }else{
        mescroll.endErr(); // 请求失败, 结束加载
    }
}
const {mescrollInit} = useMescroll(onPageScroll, onReachBottom) // 调用mescroll的hook
</script>

<template>
    <view class="mainBox">
        <view class="topBox">
            <view class="listBox">
                <view class="listItem">
                    <view class="listTitle">金额（元）</view>
                    <view class="listDesc">{{ userInfo.balance / 100 }}</view>
                </view>
            </view>
            <view class="tabBox">
                <view :class="[typeStr==2?'tabItem active':'tabItem']" @click="changeType(2)">支出</view>
                <view :class="[typeStr==1?'tabItem active':'tabItem']" @click="changeType(1)">收入</view>
            </view>
        </view>

        <mescroll-body :down="downOption" top="0" @down="downCallback"
                       @init="mescrollInit" @up="upCallback">
            <view class="myRefund">
                <view v-for="item in userAddress" class="flexBox">
                    <view class="leftBox">
                        <image v-if="item.type==1||item.type==3" class="icon" src="./imgs/add.svg"></image>
                        <image v-else class="icon" src="./imgs/reduce.svg"></image>
                        <text :class="item.type==2?'text':'textAdd'">{{ item.type == 1 || item.type == 3 ? '收入' : '支出' }}</text>
                    </view>
                    <view class="rightBox">
                        <view v-if="item.type==1||item.type==3" class="money">+ {{item.amount/100}}</view>
                        <view v-else class="money">- {{item.amount/100}}</view>
                        <view class="time">{{ formatTime(item.createdAt) }}</view>
                    </view>
                </view>
            </view>
        </mescroll-body>
    </view>
</template>

<style lang="scss" scoped>
.mainBox {
    min-height: 100vh;
    background: #f5f5f5;
}
.topBox{
    background: #ffffff;
    padding: 32rpx;
}

.formItem {
    padding: 0 20rpx;
    margin-bottom: 36rpx;

    .ItemHead {
        color: var(--Normal-Black-333, #333);
        font-family: 'PingFang HK';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 18px */
        margin-bottom: 16rpx;
        display: flex;
        justify-content: space-between;

        p {
            color: var(--Normal-Black-666, #666);
            font-family: 'PingFang HK';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 18px */
        }
    }

    .inputForm {
        display: flex;
        height: 56rpx;
        padding: 20rpx 16rpx;
        align-items: center;
        justify-content: space-between;
        border-radius: 12rpx;
        border: 1px solid var(--Border-Border-Primary, #ededed);
        background: var(--Backgroung-Background-Primary, #f9f9f9);

        input {
            flex: 1;
            width: 100%;
            border-width: 0px;
            outline-width: 0px;
            background: var(--Backgroung-Background-Primary, #f9f9f9);
        }

        span {
            display: block;
            color: var(--Brand-Green-Primary, #3C9CFF);
            text-align: right;
            font-family: 'PingFang HK';
            font-size: 16rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 16px */
            min-width: 80rpx;
            flex-shrink: 0;
        }
    }
}

.pagination-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 28px;
    background: #FFFFFF;
    padding: 20rpx 0;
}

.moneySuccess {
    color: var(--Brand-Green-Highlight, #19d454);
    text-align: right;
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 14px */
}

.title {
    width: 100%;
    height: 88rpx;
    background: #f2f2f2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 36rpx;
    box-sizing: border-box;

    .title-text {
        width: 100%;
        color: #333;
        font-family: 'PingFang HK';
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 48px */
        display: flex;
        justify-content: space-between;
        align-items: center;

        p {
            color: var(--Brand-Gray-Blue, #6a7d8c);
            font-family: 'PingFang HK';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }
}

.listBox {
    display: flex;
    justify-content: center;
    margin-bottom: 24rpx;
    padding: 0 36rpx;

    .listItem {
        width: 686rpx;
        height: 192rpx;
        flex-shrink: 0;
        border-radius: 16rpx;
        background: #3C9CFF;
        box-shadow: 0px 1rpx 2rpx 0px rgba(0, 0, 0, 0.05);
        padding: 32rpx;

        .listTitle {
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 32rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 16px; /* 100% */
            margin-bottom: 24rpx;
        }

        .listDesc {
            color: #FFF;
            font-family: D-DIN-PRO;
            font-size: 72rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 72rpx; /* 100% */
        }
    }
}

.tabBox {
    display: flex;
    width: 686rpx;
    height: 64rpx;
    padding: 4rpx;
    align-items: flex-start;
    flex-shrink: 0;
    border-radius: 4px;
    background: var(--Info-bg, #F5F5F5);

    .active {
        display: flex;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        align-self: stretch;
        border-radius: 4px;
        background: var(--Background-white-black, #FFF);

        color: var(--Primary-text, #3C9CFF);
        font-feature-settings: 'case' on;

        /* Regular/Body */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 17px);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 22px); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }

    .tabItem {
        display: flex;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        align-self: stretch;

        display: flex;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        align-self: stretch;
    }

}

.myRefund {
    margin: 36rpx;


    .flexBox {
        width: 686rpx;
        height: 120rpx;
        flex-shrink: 0;
        border-radius: 16rpx;
        background: var(--Background-white, #FFF);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 36rpx;
        margin-bottom: 24rpx;

        .leftBox {
            display: flex;
            align-items: center;

            .icon {
                width: 20px;
                height: 20px;
                flex-shrink: 0;
                margin-right: 8rpx;
            }

            .text {
                background: var(--Background-white, #FFF);
                color: #576B95;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 15px */
            }

            .textAdd {
                color:  #3C9CFF;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 15px */
            }
        }

        .rightBox {
            display: flex;
            flex-direction: column;

            .money {
                color: var(---1, #333);
                text-align: right;
                font-family: D-DIN-PRO;
                font-size: 40rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 40rpx; /* 100% */
                letter-spacing: 0.5px;
                margin-bottom: 8rpx;
            }

            .time {
                color: var(--Text-tips, #909399);
                font-feature-settings: 'case' on;
                /* Regular/Caption2 */
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Caption2, 22rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
                letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
            }
        }
    }
}
</style>
