<template>
    <view class="goods-sku">
        <view class="rightMore" @click="openPopup()">
            <view class="labelText">规格</view>
            <view class="contentText">{{ activeSkuObj.specsText }}</view>
<!--            <view class="rightImg"></view>-->
            <u-icon :size="24" name="arrow-right"></u-icon>
        </view>
        <view class="skuBlock">
            <view v-for="item in skuList" v-if="skuList.length > 0" @click="selSku(item)">
                <image
                    v-if="item.inventory > 0"
                    :class="activeSkuObj.skuId == item.id ? 'active' : ''"
                    :src="item.picture"
                    alt=""
                />
            </view>
        </view>
        <uni-popup ref="popup" background-color="#fff" type="bottom">
            <view class="popupBlock">
                <view class="popupTop">
                    <view class="popupTopLeft">
                        <image :src="props.goods.picture" alt="" class="popupTopImg"/>
                    </view>
                    <view class="popupTopRight">
                        <view class="popupRightTitle">{{ props.goods.name }}</view>
                        <view class="specifications"> {{ activeName }}</view>
                        <view class="popupRightPrice">
                            <Price :goods="props.goods" :skuObj="changeSkuObj"></Price>
                        </view>
                    </view>
                </view>

                <view class="specs">
                    <view v-for="(item, index) in goods.specs" :key="item.id" class="specs-content">
                        <view class="specs-name">{{ goods.specs[index].name }}</view>
                        <view style="width: 100%; min-height: 50px">
                            <view
                                v-for="val in item.values"
                                :key="val.name"
                                :class="{ selected: val.selected, disabled: val.disabled }"
                                class="specs-item"
                                @click="clickSpecs(item, val)"
                            >
                                <view class="specs-item-Left">
                                    <image v-if="val.picture" :src="val.picture" alt="" class="specs-item-img"/>
                                    <view class="specs-item-name">{{ val.name }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="count">
                    <view>数量</view>
                    <view class="count-content">
                        <view class="count-content-left" @click="skuCountChange(-1)">-</view>
                        <view class="count-content-center">{{ props.bugcount }}</view>
                        <view class="count-content-right" @click="skuCountChange(1)">+</view>
                    </view>
                </view>
                <view class="downBtn" @click.stop="closePopup()">确定共{{ props.bugcount }}件</view>
            </view>
        </uni-popup>
    </view>
</template>
<script lang="ts" setup>
import getPowerSet from './power-set'
import {onMounted, ref, reactive} from 'vue'
import Price from '@/pages/shop/components/Price.vue'

const activeSkuObj: any = ref({})
const spliter = '★'
// 激活的规格名称
const activeName = ref('')
// 传给父组件的值
const changeSkuObj = reactive({
    // 使用 reactive 创建响应式对象
    skuId: null,
    price: 0,
    oldPrice: 0,
    inventory: 0,
    skuInfo: null,
    specsText: '',
})
// 激活的规格价格
const activePrice = ref(0)
const activeOldPrice = ref(0)
// sku数量
const skuCount = ref(1)

// 激活库存
const activeInventory = ref(0)
const emit = defineEmits<{
    (e: 'change', sku: any): void
    (e: 'bugcountChange', value: number): void
}>()
// 根据skus数据得到路径字典对象
const getPathMap = (skus: any) => {
    const pathMap: any = {}
    if (skus && skus.length > 0) {
        skus.forEach((sku: any) => {
            // 1. 过滤出有库存有效的sku
            if (sku.inventory) {
                // 2. 得到sku属性值数组
                const specs = sku.specs.map((spec: any) => spec.value)
                // 3. 得到sku属性值数组的子集
                const powerSet = getPowerSet(specs)
                // 4. 设置给路径字典对象
                powerSet.forEach((set) => {
                    const key = set.join(spliter)

                    // 如果没有就先初始化一个空数组
                    if (!pathMap[key]) {
                        pathMap[key] = []
                    }
                    pathMap[key].push(sku.id, sku.picture, sku.price, sku.oldPrice, sku.inventory)
                })
            }
        })
    }
    return pathMap
}

// 初始化禁用状态
function initDisabledStatus(specs: any, pathMap: any) {
    if (specs && specs.length > 0) {
        specs.forEach((spec: any) => {
            spec.values.forEach((val: any) => {
                // 设置禁用状态
                val.disabled = !pathMap[val.name]
                val.picture = pathMap[val.name][1]
                val.price = pathMap[val.name][2]
                val.oldPrice = pathMap[val.name][3]
                val.inventory = pathMap[val.name][4]
            })
        })
    }
}

// 得到当前选中规格集合
const getSelectedArr = (specs: any) => {
    const selectedArr: any = []
    specs.forEach((spec: any, index: any) => {
        const selectedVal = spec.values.find((val: any) => val.selected)
        if (selectedVal) {
            selectedArr[index] = selectedVal.name
        } else {
            selectedArr[index] = undefined
        }
    })
    return selectedArr
}

// 更新按钮的禁用状态
const updateDisabledStatus = (specs: any, pathMap: any) => {
    // 遍历每一种规格
    specs.forEach((item: any, i: any) => {
        // 拿到当前选择的项目
        const selectedArr = getSelectedArr(specs)
        // 遍历每一个按钮
        item.values.forEach((val: any) => {
            if (!val.selected) {
                selectedArr[i] = val.name
                // 去掉undefined之后组合成key
                const key = selectedArr.filter((value: any) => value).join(spliter)
                val.disabled = !pathMap[key]
            }
        })
    })
}
// 修改数量

const props = defineProps({
    goods: {
        type: Object,
        default: () => ({specs: [], skus: []}),
    },
    bugcount: Number, // 购买数量
})
const skuList = ref(props.goods.skus)
let pathMap: any = {}
// 弹出层
const popup = ref<{
    open: (type?: UniHelper.UniPopupType) => void
    close: () => void
}>()
// 打开弹出层
const openPopup = () => {
    popup.value?.open()
}
// 关闭弹出层
const closePopup = () => {
    console.log('关闭弹出层')

    popup.value?.close()
}
//选中规格
const selectedSpecs = ref({})
onMounted(() => {
    // 得到所有字典集合
    pathMap = getPathMap(props.goods.skus)

    // 组件初始化的时候更新禁用状态
    initDisabledStatus(props.goods.specs, pathMap)
    for (let i in props.goods.specs) {
        let num = 0
        for (let m in props.goods.specs[i].values) {
            if (props.goods.specs[i].values[m].disabled == false && num == 0) {
                num++
                clickSpecs(props.goods.specs[i], props.goods.specs[i].values[m])
            }
        }
    }
})
const clickSpecs = (item: any, val: any) => {
    if (val.disabled) return false
    // selectedSpecs.value[val.name] = val

    // 选中与取消选中逻辑
    if (val.selected) {
        return
    } else {
        item.values.forEach((bv: any) => {
            bv.selected = false
        })
        val.selected = true
        // activePrice.value = val.price
        // activeOldPrice.value = val.oldPrice
        activeInventory.value = val.inventory
    }

    // 点击之后再次更新选中状态
    updateDisabledStatus(props.goods.specs, pathMap)
    // 把选择的sku信息传出去给父组件
    // 触发change事件将sku数据传递出去
    const selectedArr = getSelectedArr(props.goods.specs).filter((value: any) => value)
    // 如果选中得规格数量和传入得规格总数相等则传出完整信息(都选择了)
    // 否则传出空对象
    if (selectedArr.length === props.goods.specs.length) {
        // 从路径字典中得到skuId
        const skuId = pathMap[selectedArr.join(spliter)][0]
        const sku = props.goods.skus.find((sku: any) => sku.id === skuId)
        Object.assign(changeSkuObj, {
            // 使用 Object.assign 更新响应式对象
            skuId: sku.id,
            price: sku.price,
            oldPrice: sku.oldPrice,
            inventory: sku.inventory,
            skuInfo: sku,
            specsText: sku.specs.reduce((p: any, n: any) => `${p} ${n.name}：${n.value}`, ''),
        })
        console.log(changeSkuObj, 888)
        activeSkuObj.value = changeSkuObj
        emit('change', changeSkuObj)
    } else {
        emit('change', {})
    }
}
const selSku = (item: any) => {
    let skuId = item.id
    const sku = props.goods.skus.find((sku: any) => sku.id === skuId)
    Object.assign(changeSkuObj, {
        // 使用 Object.assign 更新响应式对象
        skuId: sku.id,
        price: sku.price,
        oldPrice: sku.oldPrice,
        inventory: sku.inventory,
        skuInfo: sku,
        specsText: sku.specs.reduce((p: any, n: any) => `${p} ${n.name}：${n.value}`, ''),
    })
    activeSkuObj.value = changeSkuObj
    emit('change', changeSkuObj)
    const {specs} = props.goods
    let selSpecs = item.specs
    console.log(specs, selSpecs, 888)
    for (let i in specs) {
        for (let n in selSpecs) {
            console.log(specs[i].name, selSpecs[n].name, 888)
            if (specs[i].name == selSpecs[n].name) {
                const val = specs[i].values.find((val: any) => val.name == selSpecs[n].value)
                clickSpecs(specs[i], val)
            }
        }
    }
}
// 修改 bugcount
const updateBugCount = (newCount: number) => {
    emit('bugcountChange', newCount)
}

// 修改数量的方法
const skuCountChange = (count: number) => {
    const newCount = (props.bugcount as number) + count
    console.log(newCount)

    if (newCount < 1) {
        return
    }
    if (newCount > activeInventory.value) {
        console.log('超出库存')

        return
    }
    updateBugCount(newCount)
}
</script>

<style lang="scss" scoped>
@mixin sku-state-mixin {
    margin-right: 10rpx;
    cursor: pointer;

    &.selected {
        border: 2rpx solid #3C9CFF;
    }

    &.disabled {
        opacity: 0.6;
        border-style: dashed;
        cursor: not-allowed;
    }
}

.goods-sku {
    padding-top: 24rpx;
    box-sizing: border-box;
    width: 100%;
    min-height: 280rpx;

    .skuBlock {
        margin-top: 20rpx;
        display: flex;
        flex-wrap: wrap;

        image {
            width: 128px;
            height: 128px;
            flex-shrink: 0;
            border-radius: 4px;
            margin-right: 12rpx;
            border: 2px solid var(--Brand-Green-Primary, #ffffff);
        }

        .active {
            border: 2px solid var(--Brand-Green-Primary, #3C9CFF);
        }

        .SkuBlockItem {
            margin-top: 10rpx;
            display: flex;

            .SkuTopLeft {
                min-width: 100rpx;
                margin-top: 10rpx;
                color: #999;
                font-family: 'PingFang SC';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 24rpx; /* 24px */
            }

            .SkuBotton {
                position: relative;
                overflow: auto;
                width: 100%;
                display: flex;
                flex-wrap: nowrap;

                .selected {
                    border-color: #3C9CFF;
                }

                .SkuButtonItem {
                    margin-left: 10rpx;
                    padding: 0 12rpx;
                    @include sku-state-mixin;

                    .selected {
                        border-color: #3C9CFF;
                    }

                    .skuButtonImg {
                        width: 128rpx;
                        height: 128rpx;
                    }
                }
            }
        }
    }

    dl {
        display: flex;
        padding: 20rpx 0;
        align-items: center;

        dt {
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
            margin-right: 32rpx;
        }

        dd {
            flex: 1;
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */

            > image {
                width: 50rpx;
                height: 50rpx;
                margin-bottom: 4rpx;
                @include sku-state-mixin;
            }

            > span {
                display: inline-block;
                height: 30rpx;
                line-height: 28rpx;
                padding: 5rpx 20rpx;
                margin-bottom: 4rpx;
                font-size: 24rpx;
                @include sku-state-mixin;
            }
        }
    }

    :deep(.uni-popup uni-view[name='content']) {
        border-top-left-radius: 12rpx;
        border-top-right-radius: 12rpx;
        overflow: hidden;
    }

    .popupBlock {
        position: relative;
        padding: 0 38rpx;
        padding-top: 40rpx;
        height: 65vh;

        overflow: auto;

        .popupTop {
            display: flex;
            border-bottom: 1px solid #ededed;

            .popupTopLeft {
                .popupTopImg {
                    width: 128rpx;
                    height: 128rpx;
                }
            }

            .popupTopRight {
                margin-left: 24rpx;

                .popupRightTitle {
                    color: #333;
                    font-family: 'PingFang SC';
                    font-size: 28rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 100%; /* 28px */
                }

                .specifications {
                    margin-top: 16rpx;
                    color: #666;
                    font-family: 'PingFang SC';
                    font-size: 22rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 100%; /* 22px */
                }

                .popupRightPrice {
                    margin-top: 20rpx;
                    display: flex;
                    align-items: center;

                    .popupRightPriceNew {
                        color: #3C9CFF;
                        font-family: 'PingFang HK';
                        font-size: 40rpx;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 100%; /* 40px */
                    }

                    .popupRightPriceOld {
                        margin-left: 24rpx;
                        color: #999;
                        font-family: 'PingFang HK';
                        font-size: 24rpx;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 24rpx; /* 24px */
                        text-decoration: line-through;
                    }
                }
            }
        }

        .lineview {
            margin-top: 44rpx;
            width: 100%;
            height: 2rpx;
            background-color: #ededed;
        }

        .desc {
            margin-top: 56rpx;

            .desc-title {
                color: #333;
                font-family: 'PingFang SC';
                font-size: 28rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 28px */
            }

            .desc-content {
                padding: 5rpx 24rpx;
                border-radius: 4rpx;
                border: 2rpx solid #3C9CFF;
                background: #fff;
                margin-top: 24rpx;
                color: #333;
                font-family: 'PingFang SC';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 100%; /* 24px */
            }
        }

        .specs {
            height: calc(65vh - 554rpx);
            overflow: auto;
            margin-top: 48rpx;

            .specs-title {
                color: #000;
                font-family: 'PingFang SC';
                font-size: 28rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 28px */
            }

            .specs-content {
                margin-top: 24rpx;

                .specs-name {
                    color: #000;
                    font-family: 'PingFang SC';
                    font-size: 28rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 100%; /* 28px */
                }

                .specs-item {
                    margin-top: 24rpx;
                    height: 64rpx;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 16rpx;
                    @include sku-state-mixin;

                    .specs-item-Left {
                        width: 100%;
                        display: flex;
                        align-items: center;

                        .specs-item-img {
                            width: 60rpx;
                            height: 60rpx;
                        }

                        .specs-item-name {
                            margin-left: 16rpx;
                            color: #333;
                            text-align: center;
                            font-family: 'PingFang SC';
                            font-size: 24rpx;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 100%; /* 24px */
                        }
                    }
                }
            }
        }

        .count {
            margin-top: 46rpx;

            .count-content {
                margin-top: 24rpx;
                width: 216rpx;
                height: 60rpx;
                border-radius: 72rpx;
                border: 2rpx solid #666;
                display: flex;
                align-items: center;

                .count-content-left {
                    flex: 2;
                    color: #999999;
                    text-align: center;
                }

                .count-content-center {
                    flex: 3;
                    color: #000;
                    font-size: 28rpx;
                    text-align: center;
                }

                .count-content-right {
                    color: #000;
                    flex: 2;
                    text-align: center;
                }
            }
        }

        .downBtn {
            margin-top: 50rpx;
            width: 100%;
            height: 88rpx;
            text-align: center;
            line-height: 88rpx;
            border-radius: 16rpx;
            border: 1px solid #3C9CFF;
            background: #3C9CFF;
            color: #fff;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
        }
    }

    .rightMore {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 648rpx;
        height: 44rpx;

        .labelText {
            min-width: 100rpx;
            flex-shrink: 0;
            color: #999;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 24px */
        }

        .contentText {
            flex: 1;
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
        }

        .rightImg {
            width: 24rpx;
            height: 24rpx;
            background: url("./moreUnfold.png");
            background-size: 100% 100%;
            flex-shrink: 0;
        }
    }
}
</style>
