<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" >开始时间

                <view class="nickname">
                    <picker
                        mode="selector"
                        :range="timeOptions"
                        :value="startTimeIndex"
                        @change="handleStartTimeChange"
                    >
                        <view >
                            {{formatTime(timeOptions[startTimeIndex] ) }}
                        </view>
                    </picker>
                </view>
                <L-arrow></L-arrow>

            </view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" >结束时间
                <view class="nickname">
                    <picker
                        mode="selector"
                        :range="timeOptions"
                        :value="endTimeIndex"
                        :start="endTimeStartIndex"
                        @change="handleEndTimeChange"
                    >
                        <view class="picker">

                            {{formatTime( timeOptions[endTimeIndex] )}}
                        </view>
                    </picker>
                </view>
                <L-arrow></L-arrow>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, computed,watch } from 'vue';
const mainInfo:any = ref({})
import {useCarStatusStore} from '@/stores'
const carStatusStore = useCarStatusStore()
let list = carStatusStore.carStatus
const mainList = ref(list)
for(let i in list ){
    if(list[i].id==5){
        mainInfo.value = list[i]
    }
}
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    })
}
const formatTime = (time: string) => {
// 判断时间是20：00 之前  时间添加前缀，第二天
    let arr = time.split(":");
    if(Number(arr[0])<20){
        return '第二天 '+time;
    }else{
        return time
    }
}

// 生成时间选项
const generateTimeOptions = () => {
    const options = [];
    let startHour = 20;
    let startMinute = 0;

    // 从20:00到23:45
    for (let hour = startHour; hour < 24; hour++) {
        for (let minute = startMinute; minute < 60; minute += 5) { // 每5分钟一个选项
            const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            options.push(timeString);
        }
        startMinute = 0; // 重置分钟
    }

    // 从00:00到08:00
    for (let hour = 0; hour <= 8; hour++) {
        for (let minute = 0; minute < 60; minute += 5) { // 每5分钟一个选项
            const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            options.push(timeString);
        }
    }

    return options;
};


const timeOptions = generateTimeOptions();
// 开始时间和结束时间索引
const startTimeIndex:any = ref(0);
const endTimeIndex:any  = ref(0);
for(let i in timeOptions){
    if(timeOptions[i]==mainInfo.value.info.begin){
        startTimeIndex.value = i
    }
    if(timeOptions[i]==mainInfo.value.info.end){
        endTimeIndex.value = i
    }
}



// 计算结束时间的起始索引
const endTimeStartIndex = computed(() => startTimeIndex.value);

// 处理开始时间变化
const handleStartTimeChange = (e: any) => {
    startTimeIndex.value = e.detail.value;
    mainInfo.value.info.begin = timeOptions[startTimeIndex.value]
    if (endTimeIndex.value < startTimeIndex.value) {
        endTimeIndex.value = startTimeIndex.value;
        mainInfo.value.info.end = timeOptions[startTimeIndex.value]
    }
};

// 处理结束时间变化
const handleEndTimeChange = (e: any) => {
    endTimeIndex.value = e.detail.value;
    mainInfo.value.info.end = timeOptions[endTimeIndex.value]
};



watch(()=>mainInfo.value,()=>{
    mainList.value[4] = mainInfo.value
    carStatusStore.setCarStatus(mainList.value)
},{
    deep:true
})

</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;
    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .nickname {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

    }
}


</style>
