<script lang="ts" setup>
import {computed, ref} from 'vue'

const form = ref({
    oldPassword: '',
    password: '',
    rePassword: ''
})
const status = computed(() => {
    return !(form.value.oldPassword && form.value.password && form.value.rePassword)
})
const submit = () => {
    // todo 这里发送请求到服务器修改密码
    console.log('修改密码:', form.value)
}
</script>

<template>
    <view class="mainBox">
        <view class="top">
            忘记密码
        </view>
        <view class="desc">
            请输入 6-16 位密码，只允许输入数字和字母
        </view>
        <view class="formBox">
            <view class="formItem">
                <u-input v-model="form.oldPassword" :border="false" :clearable="false" placeholder="请输入旧密码"
                         type="password"/>
            </view>
            <view class="formItem">
                <u-input v-model="form.password" :border="false" :clearable="false" placeholder="请输入新密码"
                         type="password"/>
            </view>
            <view class="formItem">
                <u-input v-model="form.rePassword" :border="false" :clearable="false" placeholder="请再次输入新密码"
                         type="password"/>
            </view>
        </view>
        <view class="flex-1"></view>
        <view class="submitBtn">
            <u-button :disabled="status" type="primary" @click="submit">
                完成
            </u-button>
        </view>
    </view>

</template>

<style lang="scss" scoped>
html, body {
    background: #f5f5f5;
}

.mainBox {
    width: 750rpx;
    padding: 32rpx;

    .flex-1 {
        flex: 1;
    }
}

.top {
    color: var(--Text-main, #282A2E);
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 68rpx; /* 121.429% */
    letter-spacing: 0.364px;
    margin-top: 48rpx;
}

.desc {
    color: var(--Text-main, #282A2E);
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption1, 24rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
    letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    margin-top: 16rpx;
}

.formBox {
    margin-top: 60rpx;

    .formItem {
        margin-bottom: 32rpx;
        display: flex;
        width: 686rpx;
        height: 96rpx;
        padding: 0px 24rpx;
        align-items: center;
        flex-shrink: 0;
        border-radius: 8rpx;
        border: 2rpx solid var(--Border-4, #E6E6E8);
    }


}

.submitBtn {
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    width: 686rpx;
    height: 96rpx;

    :deep(button) {
        width: 100%;
        height: 100%;
    }
}
</style>