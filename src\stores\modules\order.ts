import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useOrderStore = defineStore(
  'order',
  () => {
    const orderInfo = ref({});
    // 获取最新购物车列表action
    const setOrderInfo = async (info:any) => {
        orderInfo.value = info
    };
    //
    const getOrderInfo = async () => {
        return orderInfo.value
    }

    return {
        orderInfo, setOrderInfo, getOrderInfo,
    };
  },
  {
    persist: {
      storage: {
        getItem(key) {
          return uni.getStorageSync(key)
        },
        setItem(key, value) {
          uni.setStorageSync(key, value)
        },
      },
    },
  },
)

