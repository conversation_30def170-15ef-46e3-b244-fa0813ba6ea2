<template>
        <view class="top">请输入12位设备SN码</view>
        <view class="formBox">
            <u-input v-model="sn" :border="false"  placeholder="请输入设备SN码" type="number"></u-input>
            <image src="../../static/imgs/mine/clear.png" class="deleteIcon" v-if="sn.length>0" @tap="sn = ''"></image>
        </view>
        <view class="btnBox">
            <u-button  type="primary" @click="showCode = true" :disabled="status" @tap="bindDevice">绑定</u-button>
        </view>

</template>

<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
const sn = ref('')
// 获取页面初始列表
const getResetInfo = () => {
    let res = ''
    console.log(res)
    sn.value = ''
}
// 页面加载
onMounted(() => {
    // getResetInfo()
})
const status = computed(() => {
    return !(sn.value)
})
const bindDevice = () => {
    uni.navigateTo({
        url:'/pages/addDevice/snResult?type=sn&sn='+sn.value
    })
}
</script>

<style lang="scss" scoped>
.top {
    color: var(--Text-main, #282A2E);
    /* Medium/Title1 */
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 64rpx; /* 121.429% */
    letter-spacing: 0.364px;
    padding: 48rpx 32rpx 0;
}

.formBox {
    display: flex;
    margin: 32rpx;
    height: 112rpx;
    padding: 0px 24rpx;
    gap: 4px;
    flex-shrink: 0;
    border-radius: 16rpx;
    border: 1px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white, #FFF);
    justify-content: space-between;
    align-items: center;
    .deleteIcon{
        width: 32rpx;
        height: 32rpx;
    }
}
.btnBox{
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
}
</style>
