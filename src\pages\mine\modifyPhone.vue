<template>
    <L-top-height></L-top-height>
 <view class="navTop">
     <svg @click="backPressHandler" xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
         <path d="M11.4817 0.648156L4.57687 7.55299C4.4532 7.67666 4.38574 7.83593 4.38574 8.00082C4.38574 8.16572 4.4532 8.32499 4.57687 8.44678L11.4217 15.3516C11.5435 15.4734 11.7272 15.5427 11.9295 15.5427C12.1094 15.5427 12.2425 15.4865 12.3755 15.3516C12.6303 15.0968 12.6303 14.7127 12.3755 14.4578L5.91661 8.00082L12.3755 1.54194C12.6303 1.28711 12.6303 0.902988 12.3755 0.648156C12.1207 0.393323 11.7365 0.393323 11.4817 0.648156Z" fill="#909399"/>
     </svg>
 </view>
    <view v-if="step==0">
        <view class="top">请验证当前手机号</view>
        <view class="topPhone">
            当前手机号为
            <text>{{ oldPhone }}</text>
        </view>
    </view>
    <view v-if="step==1">
        <view class="top">验证</view>
        <view class="topPhone">
            已向您尾号
            <text>{{ phoneLastFour }}</text>
            的手机发送验证码
        </view>
        <view class="codeBox">
            <u-message-input maxlength="6" v-model="code" focus></u-message-input>
        </view>
        <view class="topPhone">
            获取不到验证码？
            <text @click="getCodeAgain">重新获取</text>
        </view>
    </view>
    <view v-if="step==2">
        <view class="top">更换手机号</view>
        <view class="topPhone">
            当前手机号为
            <text>{{ phone }}</text>
        </view>
        <view class="formBox">
            <u-input v-model="phone" :border="false" maxlength="11" placeholder="请输入手机号" type="number"></u-input>
            <image src="../../static/imgs/mine/clear.png" class="deleteIcon" v-if="phone.length>0" @tap="phone = ''"></image>
        </view>
    </view>
    <view v-if="step==3">
        <view class="top">验证</view>
        <view class="topPhone">
            已向您尾号
            <text>{{ phoneNewLastFour }}</text>
            的手机发送验证码
        </view>
        <view class="codeBox">
            <u-message-input maxlength="6" v-model="codeNew" focus></u-message-input>
        </view>
        <view class="topPhone">
            获取不到验证码？
            <text @click="getCodeAgain">重新获取</text>
        </view>
    </view>
    <view class="btnBox">
        <u-button  type="primary" @click="showCode = true" @tap="getCode" v-if="step==0">获取验证码</u-button>
        <u-button  type="primary" @click="showCode = true" :disabled="codeStatus" @tap="actionNext" v-if="step==1">下一步</u-button>
        <u-button  type="primary" @click="showCode = true" :disabled="status" @tap="getCodeAgain" v-if="step==2">获取验证码</u-button>
        <u-button  type="primary" @click="showCode = true" :disabled="mainStatus" @tap="confirmPhone" v-if="step==3">完成</u-button>
    </view>
</template>

<script lang="ts" setup>
import {computed, ref} from 'vue'
import {getCodeAPI} from "@/services/login";
import {modifyPhone} from "@/services/user";
const phone = ref('')
const code = ref('')
const codeNew = ref('')
const step = ref(0)
const props = defineProps({
    tel: {
        type: String,
        default: ''
    },
})
const oldPhone = ref(props.tel)
const codeStatus = computed(() => {
    return !(code.value)
})
const status = computed(() => {
    return !(phone.value)
})
const mainStatus = computed(() => {
    return !(codeNew.value)
})
// 获取手机号后四位
const phoneLastFour = computed(() => {
    return oldPhone.value.slice(-4)
})
const phoneNewLastFour = computed(() => {
    return phone.value.slice(-4)
})

// 发送验证码
const getCode = async () => {
    let res = await getCodeAPI({tel: props.tel})
    if (res.msg == 'success') {
        uni.showToast({icon: 'none', title: '验证码已发送到:' + props.tel})
        step.value=1
    }
}
const actionNext = () => {
    step.value = 2
}
// 新手机号验证码
const getCodeAgain  = async () => {
    let res = await getCodeAPI({tel: phone.value})
    if (res.msg == 'success') {
        uni.showToast({icon: 'none', title: '验证码已发送到:' + phone.value})
        step.value = 3
    }
}

const confirmPhone =async  () => {
    console.log(phone.value,code.value,codeNew.value,props.tel)
    let res = await modifyPhone({tel1: phone.value, code1: codeNew.value, tel: props.tel,code: code.value})
    if (res.msg == 'success') {
        uni.showToast({icon: 'none', title: '修改成功'})
        uni.navigateBack()
    }
}
// 拦截返回事件
const backPressHandler = () => {
    if (step.value == 0) {
        uni.navigateBack({delta:1})
        return true; // 阻止默认返回行为
    }else{
        step.value--
        return false; // 允许默认返回行为
    }
}
</script>

<style lang="scss" scoped>
.navTop{
    padding: 32rpx;
    height: 88rpx;
}
.top {
    color: var(--Text-main, #282A2E);
    /* Medium/Title1 */
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 64rpx; /* 121.429% */
    letter-spacing: 0.364px;
    padding: 48rpx 32rpx 0;
}
.topPhone{
    padding: 14rpx 32rpx 0;
    color: var(--Text-main, #282A2E);
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption1, 24rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
    letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    text{
        color: var(--Primary-text, #3C9CFF);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption1, 24rpx);
        font-style: normal;
        font-weight: 600;
        line-height: var(--Line-Height-Caption1, 32rpx);
        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    }
}

.formBox {
    display: flex;
    margin: 32rpx;
    height: 112rpx;
    padding: 0px 24rpx;
    gap: 4px;
    flex-shrink: 0;
    border-radius: 16rpx;
    border: 1px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white, #FFF);
    justify-content: space-between;
    align-items: center;
    .deleteIcon{
        width: 32rpx;
        height: 32rpx;
    }
}
.codeBox{
    padding: 58rpx 32rpx 32rpx;
    :deep(.u-char-flex){
        justify-content: flex-start;
    }
}
.btnBox{
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
}
</style>
