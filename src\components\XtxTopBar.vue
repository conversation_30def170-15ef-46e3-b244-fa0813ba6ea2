<script lang="ts" setup>
// 获取屏幕边界到安全区域距离
import {onShow} from "@dcloudio/uni-app";

const {safeAreaInsets} = uni.getSystemInfoSync()
import NavSearch from '@/static/images/navsearch.svg'
import NavCart from '@/static/images/navcart.svg'
import Category from '@/static/images/category.svg'
// 新增状态控制遮罩显示
import {ref} from 'vue'

const toPage = (page: string) => {
    uni.navigateTo({
        url: page,
    })
}
const props = defineProps({
    word: {
        type: String,
        default: '',
    },
})
const keyword = ref('')
onShow(() => {
    keyword.value = props.word||''
})
//  定义emit
const emit = defineEmits(['search'])
const searchKey = () => {
    let query = keyword.value
    emit('search', query)
}
</script>

<template>
    <view>
        <view :key="Math.random()" :style="{ paddingTop: safeAreaInsets!.top + 10 + 'px' }" class="navbar">
            <view class="searchMask">
                <view class="searchBox" @click.stop>
                    <image :src="NavSearch" alt="" @click.stop="searchKey"/>
                    <input v-model="keyword" placeholder="请输入关键词" type="text" @blur="searchKey" @keydown.enter="searchKey" />
                </view>
            </view>
            <view class="RightImg">
                <view class="rightItem">
                    <image :src="NavCart" alt="" @click="toPage('/pages/shop/cart')"/>
                    <view class="text">购物车</view>
                </view>
                <view class="rightItem">
                    <image :src="Category" alt="" @click="toPage('/pages/shop/category')"/>
                    <view class="text">分类</view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped>
/* 自定义导航条 */
.navbar {
    position: relative;
    width: 750rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0rpx 38rpx 20rpx;
    z-index: 99;
    background: #FFFFFF;

    .searchMask {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .searchBox {
            width: 100%;
            height: 64rpx;
            flex-shrink: 0;
            margin-right: 24rpx;
            background: #F2F2F2;
            border-radius: 12rpx;
            display: flex;
            align-items: center;

            input {
                flex: 1;
                font-size: 24rpx;
                height: 64rpx;
                line-height: 60rpx;

                color: #969696;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;

            }

            image {
                width: 24rpx;
                height: 24rpx;
                margin-right: 8rpx;
                margin-left: 32rpx;
            }
        }
    }

    .RightImg {
        display: flex;
        align-items: center;
        gap: 0 32rpx;

        .rightItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            image {
                width: 40rpx;
                height: 40rpx;
            }

            .text {
                color: var(--Text-main, #282A2E);
                font-family: "PingFang SC";
                font-size: 20rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 24rpx; /* 120% */
            }
        }


    }
}
</style>
