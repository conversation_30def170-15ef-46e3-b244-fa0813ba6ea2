import { createI18n } from "vue-i18n";
import zhCN from "./zh-Cn";
import enUS from "./en";

const systemInfo = uni.getSystemInfoSync();
console.log(systemInfo);

let _lan = systemInfo.language || 'zh-CN'; // 从系统信息中获取语言设置
console.log('当前语言为:', _lan);

const i18n = createI18n({
	fallbackLocale: 'zh-CN',
	legacy: false,
	locale: _lan,
	globalInjection: true,
	messages: {
		"zh-CN": zhCN,
		en: enUS,
	},
});

export default i18n;
     