<template>
    <view class="content">
        <swiper :autoplay="autoplay"
                :duration="duration"
                class="swiper">
            <swiper-item>
                <view class="swiper-item">
                    <view class="title">车辆数据统计</view>
                    <view class="desc">
                        📊 智能监测，数据一目了然
                    </view>
                    <view class="swiper-item-img">
                        <image mode="aspectFit" src="../../static/imgs/guide/car.png"></image>
                    </view>
                    <image class="order" mode="aspectFit" src="../../static/imgs/guide/one.png"></image>
                </view>
            </swiper-item>
            <swiper-item>
                <view class="swiper-item">
                    <view class="title">全天候精准定位</view>
                    <view class="desc">🔒 全时安全守护，智能防护升级</view>
                    <view class="swiper-item-img" style="margin-right: 50rpx">
                        <image mode="aspectFit" src="../../static/imgs/guide/run.png"></image>
                    </view>
                    <image class="order" mode="aspectFit" src="../../static/imgs/guide/two.png"></image>
                </view>
            </swiper-item>
            <swiper-item>
                <view class="swiper-item">
                    <view class="title">轨迹回放与行程记录</view>
                    <view class="desc">🚲 轻松回顾出行路线，分享骑行乐趣</view>
                    <view class="swiper-item-img">
                        <image mode="aspectFit" src="../../static/imgs/guide/line.png"></image>
                    </view>
                    <view class="orderBtn" @tap="launchFlag()">立即体验</view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
export default {
    data() {
        return {
            background: ['color1', 'color2', 'color3'],
            autoplay: false,
            duration: 500,
            jumpOver: '跳过',
            experience: '立即体验'
        }
    },
    methods: {
        launchFlag: function () {
            /**
             * 向本地存储中设置launchFlag的值，即启动标识；
             */
            uni.setStorage({
                key: 'launchFlag',
                data: true,
            });
            uni.navigateTo({
                url: '/pages/role/role'
            });

        }
    }
}
</script>
<style scoped lang="scss">
page,
.content {
    width: 100%;
    height: 100vh;
    background: url("../../static/imgs/guide/bg.png");
    background-size: 100% 100%;
    padding: 0;
    overflow: hidden;
}

.swiper {
    width: 100%;
    height: 100%;
    background: #FFFFFF;
}
.title{
    color: #000;

    /* Medium/LargeTitle */
    font-family: "PingFang SC";
    font-size: 68rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 82rpx; /* 120.588% */
    letter-spacing: 0.374px;
    margin-bottom: 40rpx;
    margin-top: 100rpx;
}
.desc{
    color: #000;
    font-feature-settings: 'case' on;
    /* Regular/Subheadline */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Subheadline, 30rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Subheadline, 40rpx); /* 133.333% */
    letter-spacing: var(--Letter-Spacing-Subheadline, -0.24px);
    margin-bottom: 64rpx;
}
.order{
    display: inline-flex;
    align-items: center;
    width: 76rpx;
    height: 68rpx;
    margin-top: 74rpx;
}
.orderBtn{
    display: flex;
    width: 190rpx;
    height: 68rpx;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 22px;
    background: #3c9cff;
    color: var(--Text-white, #FFF);
    text-align: center;
    font-family: "PingFang HK";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 24rpx; /* 100% */
    margin-top: 74rpx;
}
.swiper-item {
    width: 100%;
    height: 100vh;
    text-align: center;
    position: relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    background: url("../../static/imgs/guide/bg.png");
    background-size: 100% 100%;
}

.swiper-item-img {
    height: 800rpx;
    flex-shrink: 0;
}

.swiper-item-img image {
    height: 800rpx;
}

.uniapp-img {
    height: 20%;
    background: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.uniapp-img image {
    width: 40%;
}

.jump-over, .experience {
    position: absolute;
    height: 60upx;
    line-height: 60upx;
    padding: 0 40upx;
    border-radius: 30upx;
    font-size: 32upx;
    color: #454343;
    border: 1px solid #454343;
    z-index: 999;
}

.jump-over {
    right: 45upx;
    top: 125upx;
}

.experience {
    right: 50%;
    margin-right: -105upx;
    bottom: 0;
}
</style>
