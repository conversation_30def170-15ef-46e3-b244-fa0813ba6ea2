<script lang="ts" setup>
import {onHide, onLaunch, onShow} from "@dcloudio/uni-app";
import {getVersion} from "@/services/version";

onLaunch(() => {
    console.log("App Launch");
    checkUpdate()
});
onShow(() => {
    console.log("App Show");
});
onHide(() => {
    console.log("App Hide");
});


const updateApp = (versionData: any) => {
    if (versionData.isWgt == 'true') {
        uni.showLoading({
            title: '正在下载',
            mask: true,
        })
        uni.downloadFile({
            url: versionData.url,
            success: downloadResult => {
                if (downloadResult.statusCode === 200) {
                    uni.showLoading({
                        title: '安装中..',
                        mask: true,
                    })
                    plus.runtime.install(
                        downloadResult.tempFilePath, {
                            force: true,
                        },
                        function () {
                            console.log('install success...')
                            uni.hideLoading()
                            plus.runtime.restart()
                        },
                        function (e) {
                            console.log(e)
                            uni.hideLoading()
                            uni.showToast({
                                title: e.message || '安装失败',
                                duration: 1500,
                                icon: 'none'
                            })
                        },
                    )
                } else {
                    uni.hideLoading()
                    uni.showToast({
                        title: '下载失败',
                        duration: 1500,
                        icon: 'none'
                    })
                }
            },
            fail(e) {
                uni.hideLoading()
                uni.showToast({
                    title: e.message || '下载失败',
                    duration: 1500,
                    icon: 'none'
                })
            },
        })
        return;
    }
    const systemInfo = uni.getSystemInfoSync()
    const downloadUrl =
        systemInfo.platform == 'android' ?
            versionData.url :
            versionData.url2
    plus.runtime.openURL(downloadUrl)
}
const checkUpdate = () => {
    plus.runtime.getProperty(plus.runtime.appid, async widgetInfo => {
        let res = await getVersion()
        let version = widgetInfo.versionCode
        if (res.msg == 'success') {
            let versionInfo = res.result
            console.log(versionInfo.v > version,versionInfo.v , version)
            if(versionInfo.v > version){
                if (versionInfo.isWgt === 'true') {
                    updateApp(versionInfo)
                } else {
                    uni.showModal({
                        title: '新版本发布',
                        content: '检查到有新版本,需要更新吗？',
                        confirmText: '立即更新',
                        showCancel: false,
                        success: res => {
                            if (res.confirm) {
                                updateApp(versionInfo)
                            }
                        },
                    })
                }
            }else{

            }
        }
    })
}
</script>
<style>
@import '@/styles/fonts.scss';
@use "./uni_modules/vk-uview-ui/index";
@use "./static/css/common";
html, body {
    background: #f5f5f5;
}

* {
    box-sizing: border-box;
}

.borderTopRadius {
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
}

.borderBottomRadius {
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
}
</style>
