<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="showModal(0)">微信
                <view class="bind">绑定</view>
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="showModal(1)">QQ
                <view class="unbind">解除已绑定</view>
                <L-arrow></L-arrow>
            </view>
        </view>
    </view>
    <u-modal v-model="show" :content="content" :mask-close-able="true" :show-cancel-button="true"
             @confirm="dealUnBind"></u-modal>
</template>

<script lang="ts" setup>
import {ref} from 'vue'

const show = ref(false)
const activeType = ref(0)
const roleInfo = ref({
    status: 1
})
const content = ref('解绑 微信/QQ 账号后将无法继续使用它登录App')
const dealUnBind = () => {
    if (activeType.value == 0) {
        unbindWx()
    } else {
        unbindQQ()
    }
}
const unbindWx = () => {

}
const unbindQQ = () => {

}

const showModal = (type: number) => {
    activeType.value = type
    // 微信
    if (type == 0) {
        // todo  判断账号是否绑定
        if (roleInfo.value.status == 0) {
            bindWx()
        } else {
            show.value = true
        }

    }
    //  QQ
    if (type == 1) {
        // todo  判断账号是否绑定
        if (roleInfo.value.status == 0) {
            bindQQ()
        } else {
            show.value = true

        }
    }
}
const bindWx = () => {
    uni.login({
        provider: 'weixin',
        success: (res) => {
            uni.getUserInfo({
                provider: 'weixin',
                success: (infoRes) => {
                    console.log(infoRes)
                }
            })
        }
    })
}
const bindQQ = () => {
    uni.login({
        provider: 'qq',
        success: (res) => {
            uni.getUserInfo({
                provider: 'qq',
                success: (infoRes) => {
                    console.log(infoRes)
                }
            })
        }
    })
}

</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .bind {
            flex: 1;
            text-align: right;
            color: var(--Primary-text, #3C9CFF);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

        .unbind {
            flex: 1;
            text-align: right;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }
    }

    .border {
        border-bottom: 1px solid #ededed;
    }
}
</style>
