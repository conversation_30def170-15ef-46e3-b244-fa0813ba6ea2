import type { BannerResponse } from '@/types/home'
import { http } from '@/utils/http'
/**
 *
 * @returns banner
 * 首页-轮播图-小程序
 * @param type 位置 0：首页 1：频道页
 * @param page 页码
 * @param size 每页数量
 */
export const getBannerAPI = (type: number = 0, page?: number, size?: number) => {
  return http<BannerResponse>({
    method: 'GET',
    url: '/v1/banner',
    data: {
      type,
      page,
      size,
    },
  })
}
// 设计师列表
export const getDesignerList = (params: any): any => {
  return http({
    url: '/v1/designer',
    data: params,
  })
}
// 申请设计师
export const designerApplyAPI = (params: any): any => {
  return http({
    url: '/v1/me/designerApply',
    method: 'POST',
    data: params,
  })
}
export const Subscribe = (params: any): any => {
  return http({
    url: '/v1/subscribe',
    method: 'POST',
    data: params,
  })
}

