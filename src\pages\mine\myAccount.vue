<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem border borderTopRadius ">头像
                <view class="avatar">
                    <u-avatar :src="userInfo.avatar" class="avatarItem" size="64"
                              @click="uploadAvatar"></u-avatar>
                </view>
                <L-arrow></L-arrow>
            </view>
            <view class="listItem border" @click="toPage(`/pages/mine/modifyNickname?nickname=${userInfo.nickname}`)">昵称
                <view class="nickname">{{ userInfo.nickname }}</view>
                <L-arrow></L-arrow>
            </view>
            <view class="listItem borderBottomRadius" @click="toPage(`/pages/mine/modifyPhone?tel=${userInfo.tel}`)">手机号
                <view class="nickname">{{ userInfo.tel }}</view>
                <L-arrow></L-arrow>
            </view>
        </view>

        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="toPage('/pages/mine/reset')">修改密码
                <L-arrow></L-arrow>
            </view>
        </view>
<!--        <view class="listBox">-->
<!--            <view class="listItem borderTopRadius borderBottomRadius" @click="toPage('/pages/mine/threeRole')">第三方登录-->
<!--                <view class="nickname">未绑定/已绑定</view>-->
<!--                <L-arrow></L-arrow>-->
<!--            </view>-->
<!--        </view>-->
    </view>
    <u-modal v-model="show" :content="content" :mask-close-able="true" :show-cancel-button="true"
             @confirm="confirmOut"></u-modal>
    <view class="btnBox borderTopRadius borderBottomRadius" @click="show=true">
        <view class="btn">退出登录</view>
    </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {useMemberStore} from "@/stores";
import {getUserInfo,modifyUser} from '@/services/user'
import {onShow} from "@dcloudio/uni-app";
import AliOss from "@/utils/upload.ts";
import {
    v4 as uuidv4
} from 'uuid';
import {getOssParamsAPI} from "@/services/upload";

const ossParams = ref({
    accessid: '',
    policy: '',
    signature: '',
    fileName: '',
    savaPath:''
})

const show = ref(false)
const content = '是否退出登录'
const userInfo: any = ref({})
const memberStore = useMemberStore()
const resetData = async () => {
    const res = await getUserInfo()
    if (res.msg == 'success') {
        userInfo.value = res.result
    }
    console.log(userInfo.value)
    // todo 自动设置头像
    if (userInfo.value.avatar == null) {
    }
}
//  获取OSS参数

const getOssParams = async () => {
    const res:any = await getOssParamsAPI()
    if(res.msg=='success'){
        memberStore.setUserInfo(res.result)
        ossParams.value = res.result
    }
}
onShow(() => {
    getOssParams()
    resetData()
})
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    })
}
// 头像 URL
const avatarUrl = ref<string>('')
// 上传头像函数
const uploadAvatar = () => {
    uni.chooseImage({
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
            const tempFilePaths = res.tempFilePaths;
            avatarUrl.value = tempFilePaths[0];
            // 这里可以添加上传图片到服务器的逻辑
            uploadToServer(tempFilePaths[0]);
        }
    });
}
// 上传图片到服务器的函数
const uploadToServer = async (filePath: string) => {
    let url =await uploadFile(filePath)
    console.log(url)
    if(url){
        let res = await modifyUser({avatar:url})
        if(res.msg=='success'){
            resetData()
        }
    }
}
// 上传文件到oss
const uploadFile = (filePath: string) => {
    ossParams.value.fileName =  uuidv4()+ '.png'
    ossParams.value.savaPath = "avatar/"
    return new Promise((resolve, reject) => {
        AliOss.upload(filePath, ossParams.value, (path:any) => {
            if (path) {
                resolve(path);
            } else {
                reject('');
            }
        })
    })

}
const confirmOut = () => {
    memberStore.clearProfile()
    memberStore.clearUserInfo()
    uni.reLaunch({
        url: '/pages/role/login'
    })
}
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .nickname {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

        .avatar {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            width: 64rpx;
            height: 64rpx;
        }
    }

    .border {
        border-bottom: 1px solid #ededed;
    }
}

.btnBox {
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
    padding: 32rpx;
    background: #FFFFFF;

    .btn {
        color: var(--error-u-error-dark, #E45656);
        font-feature-settings: 'case' on;
        /* Regular/Body */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }
}
</style>
