<template>
    <text>
        {{formatTime}}
    </text>
</template>

<script setup lang="ts">
import {onMounted} from 'vue';
import {useCountDown} from '@/composables/useCountDown'
const {formatTime, start} = useCountDown()
const props = defineProps({
  order: {
    type: Object,
    required: true,
  }
});
const calculateTimeDifferenceInSeconds = (targetDate:any) => {
  const now: any = new Date();
  const target: any = new Date(targetDate);
  const differenceInMilliseconds = target - now;
  return Math.floor(differenceInMilliseconds / 1000);
}
const getOrderDetailAction = async (id: any) => {
  const countdown = calculateTimeDifferenceInSeconds(props.order.payLatestTime)
  // 初始化倒计时秒数
  start(countdown)
}
onMounted(() => {
  getOrderDetailAction(props.order.id)
})
</script>

<style scoped>

</style>
