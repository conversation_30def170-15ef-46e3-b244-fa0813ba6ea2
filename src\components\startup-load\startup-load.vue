<template>
  <view v-if="state===0"  class="startupContent">
    <text class="startupText">车辆已启动</text>
    <image class="startupBgIcon" src="../../static/imgs/car/frame205.svg"></image>
    <image class="startupBgIcon" src="../../static/imgs/car/frame206.svg"></image>
    <image class="startupBgIcon" src="../../static/imgs/car/frame207.svg"></image>
    <image class="startupBgIcon" src="../../static/imgs/car/frame208.svg"></image>
  </view>
  <view v-else class="startupContent">
    <image :animation="iconAnim" class="animationIcon" src="../../static/imgs/car/frame205.svg"></image>
  </view>
</template>

<script setup>
import {ref} from "vue";
const state = ref(1) //0 动画 1结束
const iconAnim = ref({})
//1.动画中
function startAnimation() {
  let animation = uni.createAnimation()
  animation.translateX(350).opacity(0).
  step({
    duration:1000,
    timingFunction:'linear'
  })
  animation.translateX(0).opacity(1).
  step({
    duration:30,
    timingFunction:'step-end'
  })
  animation.translateX(350).opacity(0).
  step({
    duration:1000,
    timingFunction:'linear'
  })
  iconAnim.value = animation.export()
  setTimeout(()=>{
    state.value = 0
  },2100)
}
startAnimation()
//2.动画结束后，显示文字，显示图标
</script>


<style scoped lang="scss">
.startupContent {
  display: flex;
  width: 100%;
  height: 144rpx;
  border-radius: 16rpx;
  align-items: center;
  background: linear-gradient(270deg, #000 0%, #666 100%);

  .startupText {
    margin-left: 24rpx;
    margin-right: 32rpx;
    text-align: center;
    color: var(--Text-white, #FFF);
    font-feature-settings: 'case' on;
    /* Medium/Subheadline */
    font-family: "PingFang SC";
    font-size: 30rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 40rpx; /* 133.333% */
    letter-spacing: -0.24px;
  }

  .startupBgIcon {
    height: 100%;
    flex: 1;
  }
  .animationIcon{
    height: 100%;
    width: 120rpx;
  }
}
</style>
