<template>
    <view class="goodItem" @click="toDetail(orderGood.goodsId)">
        <view class="imgBox">
            <image :src="orderGood.goods.picture" alt="" class="imgItem"/>
<!--            <view class="tag">-->
<!--                <template v-for="info in orderGood.tags">-->
<!--                    <view v-if="info=='Hot'" class="Hot">{{ info }}</view>-->
<!--                    <view v-else-if="info=='New'" class="New">{{ info }}</view>-->
<!--                    <view v-else class="Sale">{{ info }}</view>-->
<!--                </template>-->
<!--            </view>-->
        </view>
        <view class="infoBox">
            <view class="name ellipsis-2">
                {{ orderGood.goods.name }}
            </view>
        </view>
        <view class="priceBox">
            <view class="price">
                <text class="new"><text class="unit">￥</text>{{ orderGood.goods.price / 100 }}</text>
<!--                <text class="oldPrice">￥{{ orderGood.goods.oldPrice / 100 }}</text>-->
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
const props = defineProps({
    orderGood: {
        type: Object,
        required: true,
    },
})
const toDetail = (id: any) => {
    uni.navigateTo({
        url: '/pages/shop/detail?id=' + id,
    })
}
</script>

<style lang="scss" scoped>
.goodItem {
    width: 336rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    padding: 32rpx;
    margin-bottom: 16rpx;
    border-radius: 24rpx;
}
.imgBox{
    width: 336rpx;
    height: 336rpx;
    margin-bottom: 10rpx;
    position: relative;
    .tag {
        position: absolute;
        right: 5rpx;
        top: 0;
        display: flex;

        .Hot {
            display: flex;
            width: 80rpx;
            height: 40rpx;
            justify-content: center;
            align-items: center;
            color: #FFFFFF;
            text-align: center;
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 16px */
            letter-spacing: 0.16px;
            border-radius: 40rpx;
            border: 1rpx solid #19D454;
            background: #19D454;
            margin-right: 10rpx;
        }

        .New {
            display: flex;
            width: 80rpx;
            height: 40rpx;
            justify-content: center;
            align-items: center;
            color: #FFFFFF;
            text-align: center;
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 16px */
            letter-spacing: 0.16px;
            border-radius: 40rpx;
            border: 1px solid #FF4500;
            background: #FF4500;
            margin-right: 10px;
        }
    }
}
.imgItem {
    width: 336rpx;
    height: 336rpx;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
}


.infoBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-top: 16rpx;
    margin-bottom: 16rpx;

    .name {
        overflow: hidden;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: "PingFang SC";
        font-size: 22rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 33rpx;
    }

    .ellipsis-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 控制显示的行数 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.priceBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    .price {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .unit{
            color: var(--fa-5151, #FA5151);
            text-align: right;
            font-family: "PingFang SC";
            font-size: 20rpx;
            font-style: normal;
            font-weight: 600;
            line-height:  22rpx;
        }

        .new {
            color: var(--fa-5151, #FA5151);
            text-align: right;
            font-family: "PingFang SC";
            font-size: 30rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 30rpx;
        }

        .oldPrice {
            margin-left: 16px;
            color: #666;
            font-family: "SF Pro Display";
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 20px */
            letter-spacing: 0.2rpx;
            text-decoration: line-through;
        }
    }
}

</style>
