<!--suppress TypeScriptCheckImport -->
<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {buyOrderAPI, createOrderAPI, getNearStoreComboAPI} from "@/services/rent";
const payId = ref('')
const submitPay = async () => {
    let resPay = await buyOrderAPI(payId.value)
    console.log(resPay)
    if (resPay.msg == 'success') {
        uni.redirectTo({
            url: '/pages/rent/order'
        })
    }
}
const createOrder = async () => {
    let res = await createOrderAPI({
        storeId: props.store,
        menuId: props.id
    })
    if (res.msg == 'success') {
        payId.value = res.result.id
    }
}
const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    store: {
        type: String,
        default: ''
    },
})
const selCombo: any = ref(null)
onMounted(async () => {
    const resList = await getNearStoreComboAPI(props.store)
    let arr = resList.result.rows
    let list = arr.filter((item: any) => item.menuId == props.id)
    console.log(resList, props)
    if (list.length != 0) {
        selCombo.value = list[0]
    }
    createOrder()
})
</script>

<template>
    <view v-if="selCombo" class="storeBox">
        <view class="title">{{ selCombo?.store?.name }}</view>
        <view class="name">{{ selCombo?.menu?.name }}</view>
        <view class="desc">{{ selCombo.menu.description }}</view>
        <view class="priceMain">
            <view class="price">
                <view class="priceText">租金</view>
                <view class="priceNum">
                    <view class="unit">￥</view>
                    {{ selCombo.menu.price / 100 }}
                </view>
            </view>
            <view class="price">
                <view class="priceText">天数</view>
                <view class="priceNum">{{ selCombo.menu.days }}
                    <view class="unit">天</view>
                </view>
            </view>
        </view>
    </view>

    <view v-if="selCombo" class="infoBox">
        <view class="flexBox">
            <view class="label">租金</view>
            <view class="content">￥{{ selCombo.menu.price / 100 }}</view>
        </view>
        <view class="flexBox">
            <view class="label">天数</view>
            <view class="content">{{ selCombo.menu.days }}天</view>
        </view>
        <view class="flexBox">
            <view class="label">优惠金额</view>
            <view class="content red">-￥0.00</view>
        </view>
        <view class="total">合计：￥{{ selCombo.menu.price / 100 }}</view>
    </view>


    <view v-if="selCombo" class="orderBox">
        <view class="orderMain">
            <view class="leftBox">
                <view class="price">
                    <text class="unit">￥</text>
                    {{ selCombo.menu.price / 100 }}
                </view>
                <view class="priceDesc">(优惠￥0.00)</view>
            </view>
            <view class="rightBox">
                <u-button type="primary" @click="submitPay">提交支付</u-button>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped>
.storeBox {
    width: 686rpx;
    height: 270rpx;
    flex-shrink: 0;
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 2rpx 4rpx 0px rgba(0, 0, 0, 0.05);
    margin: 32rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        /* Medium/Body */
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx; /* 129.412% */
        letter-spacing: -0.408px;
        margin-bottom: 32rpx;
    }

    .name {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        /* Medium/Subheadline */
        font-family: "PingFang SC";
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 40rpx; /* 133.333% */
        letter-spacing: -0.24px;
    }

    .desc {
        color: var(--Text-main, #999);
        font-feature-settings: 'case' on;
        /* Medium/Subheadline */
        font-family: "PingFang SC";
        font-size: 26rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 40rpx; /* 133.333% */
        letter-spacing: -0.24px;
        margin-bottom: 30rpx;
    }

    .priceMain {
        display: flex;

        .price {
            flex: 1;
            display: flex;
            align-items: flex-end;
        }

        .priceText {
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 100% */
        }

        .priceNum {
            display: flex;
            align-items: flex-end;
            color: #F00;
            font-family: "PingFang SC";
            font-size: 40rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 35rpx; /* 100% */
            .unit {
                color: #F00;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 24rpx; /* 100% */
            }
        }
    }
}

.infoBox {
    width: 686rpx;
    height: 242rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 2rpx 4rpx 0px rgba(0, 0, 0, 0.05);
    margin: 0 32rpx 32rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 24rpx;

    .flexBox {
        display: flex;
        justify-content: space-between;

        .label {
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption1, 24rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
            letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .content {
            color: var(--Text-content, #53565C);
            text-align: right;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption1, 24rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
            letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .red {
            color: #F00;
        }
    }

    .total {
        color: var(--Text-content, #53565C);
        text-align: right;
        /* Regular/Caption1 */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption1, 24rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    }
}

.orderMain {
    position: fixed;
    bottom: 0;
    width: 750rpx;
    height: 130rpx;
    flex-shrink: 0;
    background: #FFF;
    box-shadow: 0px 4rpx 4rpx 0px rgba(0, 0, 0, 0.25);
    display: flex;

    .leftBox {
        flex: 1;
        padding: 0 32rpx;

        .price {
            color: #F00;
            font-family: "PingFang SC";
            font-size: 40rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx;
            margin-bottom: 20rpx;
            margin-top: 40rpx;

            .unit {
                color: #F00;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 24rpx; /* 100% */
            }
        }

        .priceDesc {
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 100% */
        }
    }

    .rightBox {
        width: 378rpx;
        padding: 24rpx 32rpx;
        flex-shrink: 0;
    }

}
</style>
