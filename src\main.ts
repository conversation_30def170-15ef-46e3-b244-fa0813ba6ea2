import { createSSRApp } from "vue";
import App from "./App.vue";
import 'animate.css';
// 导入 Pinia 实例
import { createPinia } from 'pinia'
import pinia from './stores' // 假设你的 Pinia 配置在这里
// @ts-ignore
import uView from './uni_modules/vk-uview-ui';
// 导入 i18n 实例
import i18n from './local'
export function createApp() {
  const app = createSSRApp(App);
  // 使用 Pinia
  app.use(createPinia())
  app.use(pinia) // 如果你有自定义的 Pinia 配置
  // 使用 i18n
  app.use(i18n)
  // 使用 uView UI
  app.use(uView)
  return {
    app,
  };
}
