<script setup>
const props = defineProps({
  goods: {
    type: Object,
    default: () => ({
      skuInfo: {},
    }),
  },
  skuObj: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<template>
  <view>
    <view v-if="skuObj.skuInfo && skuObj.skuInfo?.isPreSale == false" class="g-price">
      <text class="priceNow">{{ skuObj.skuInfo?.price / 100 }} </text>
      <text v-if="skuObj.skuInfo?.price != skuObj.skuInfo?.oldPrice" class="priceOld">{{
        skuObj.skuInfo?.oldPrice / 100
      }}</text>
    </view>
    <view v-if="!skuObj.skuInfo" class="g-price">
      <text class="priceNow">{{ goods.price / 100 }} </text>
      <text v-if="goods.price != goods.oldPrice" class="priceOld">{{ goods.oldPrice / 100 }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.g-price {
  margin-top: 10px;

  text {
    &::before {
      content: '¥';
      font-size: 14px;
    }

    &.priceNow {
      color: #3C9CFF;
      margin-right: 10px;
      font-size: 22px;
    }

    &.priceOld {
      color: #999;
      text-decoration: line-through;
      font-size: 16px;
    }
  }
}
.saleBanner {
  width: 500px;
  height: 48px;
  padding: 16px;
  flex-shrink: 0;
  border-radius: 8px;
  background: #fd0000;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  margin-top: 16px;

  .title {
    flex: 1;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 16px */
  }

  .saleCount {
    color: #fff;
    text-align: right;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%; /* 14px */
    margin-left: 10px;
    display: flex;
  }

  .saleTime {
    margin-left: 10px;
    margin-right: 10px;
  }
}
.prePrice {
  display: flex;
  align-items: baseline;

  .num {
    color: #fd0000;
    font-family: 'PingFang HK';
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%; /* 24px */
    margin-right: 18px;
  }

  .span {
    color: #fd0000;
    font-family: 'PingFang HK';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 16px */
    margin-right: 5px;
  }

  .tip {
    display: flex;
    width: 120px;
    height: 24px;
    padding: 5px 7px;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    background: rgba(253, 0, 0, 0.1);
    flex-shrink: 0;
    color: #fd0000;
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 14px */
  }
}

#nextPrice {
  color: #fd0000;
  font-family: 'PingFang HK';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 100%; /* 16px */
  display: flex;

  .num {
    margin-right: 18px;
  }
}

.g-service {
  background: #f5f5f5;
  width: 500px;
  padding: 16px 12px;
  margin-top: 16px;

  dl {
    padding-bottom: 31px;
    display: flex;
    align-items: center;

    &:last-child {
      padding-bottom: 0px;
    }

    dt {
      min-width: 52px;
      color: #999;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 100%; /* 14px */
    }

    dd {
      color: #666;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 14px */

      &:last-child {
        text {
          margin-right: 10px;

          &::before {
            content: '•';
            color: #19d454;
            margin-right: 2px;
          }
        }

        a {
          color: #19d454;
        }
      }
    }
  }
}
</style>
