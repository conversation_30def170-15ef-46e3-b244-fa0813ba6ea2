<script lang="ts" setup>
import {ref, watch} from 'vue'
import {calcMul} from "@/utils/math";
import {onShow} from "@dcloudio/uni-app";

const props = defineProps({
  carInfo: {
    type: Object,
    default: {
      speed: 0
    }
  },
})
onShow(() => {
  console.log(9999666, props.carInfo)

})
const speedNow: any = ref('0');
// 使用 getter 函数精准监听
watch(
  () => props.carInfo.dataPoint.speed,
  (newVal) => {
    console.log('speed 状态变化:', newVal, calcMul(newVal, 3.6))
    speedNow.value = calcMul(Number(newVal), 3.6).toFixed(2)
    // 你的业务逻辑
  }, {immediate: true}
)
</script>

<template>
  <view class="speedBox">
    <view class="num">{{ speedNow }}</view>
    <view class="unit">km/H</view>
    <view class="right">
      <image class="icon" src="../../static/imgs/car/speedWhite.svg"></image>
      <view class="desc">时速</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.speedBox {
  position: fixed;
  z-index: 999;
  bottom: 32rpx;
  left: 32rpx;
  padding: 28rpx 32rpx;
  width: 686rpx;
  height: 52px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  background: conic-gradient(from -8deg at 50% 50%, #0B20A4 0deg, #1130A8 23.782326579093933deg, #204FB6 159.36291217803955deg, #2B65BE 176.33824825286865deg, #3170BE 195.0632643699646deg, #387EC4 258.72918605804443deg, #4499DA 360deg);

  .num {
    color: var(--Text-white, #FFF);
    text-align: center;
    font-family: "DIN Alternate";
    font-size: 48rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 48rpx; /* 100% */
    letter-spacing: 2px;
  }

  .unit {
    color: #83BBE2;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 34rpx; /* 106.25% */
  }


  .right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .icon {
      width: 36px;
      height: 36px;
      flex-shrink: 0;
      margin-right: 16rpx;
    }

    .desc {
      color: #83BBE2;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 28rpx; /* 100% */
    }
  }
}
</style>
