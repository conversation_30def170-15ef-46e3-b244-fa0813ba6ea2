<script lang="ts" setup>
import {computed, ref, watch} from 'vue'

const form = ref({
    phone: '13082754889',
    code: '',
})
const isSending = ref(false)
const countdown = ref(60)

const sendVerificationCode = async () => {
    if (isSending.value) return
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(form.value.phone)) {
        uni.showToast({icon: 'none', title: '请输入有效的手机号'})
        return
    }
    isSending.value = true
    // todo 这里发送请求到服务器获取验证码
    console.log('验证码已发送到:', form.value.phone)
    countdown.value = 60
    let timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
            clearInterval(timer)
            isSending.value = false
            countdown.value = 60
        }
    }, 1000)
}
const submit = () => {
    // todo 这里发送请求到服务器绑定
    console.log('绑定三方账号:', form.value)
}
const status = computed(() => {
    return !(form.value.phone && form.value.code )
})
</script>

<template>
    <L-top-height></L-top-height>
    <L-top-close></L-top-close>
    <view class="top">
        微信/QQ登录
    </view>
    <view class="formBox">
        <view class="formItem">
            <u-input v-model="form.phone" :border="false" :clearable="false" :maxlength="11" placeholder="请输入手机号"
                     type="number"/>
        </view>
        <view class="formFlex">
            <view class="codeInput">
                <u-input v-model="form.code" :border="false" :clearable="false" placeholder="请输入验证码"
                         type="number"/>
            </view>
            <view class="btnBox">
                <u-button   v-if="isSending" plain type="primary">
                    {{ countdown }}秒后重发
                </u-button>
                <u-button  v-else  type="primary" @click="sendVerificationCode">
                    获取验证码
                </u-button>
            </view>
        </view>
    </view>
    <view class="submitBtn">
        <u-button  type="primary" @click="submit" :disabled="status">
            确定并登录
        </u-button>
    </view>
</template>

<style lang="scss" scoped>
html,body{
    background: #FFFFFF;
}
.top {
    color: var(--Text-main, #282A2E);
    /* Medium/Title3 */
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 68rpx; /* 121.429% */
    letter-spacing: 0.364px;
    padding: 48rpx 32rpx 112rpx;
}

.formBox {
    margin: 0 32rpx;

    .formItem {
        margin-bottom: 30rpx;
        display: flex;
        width: 686rpx;
        height: 96rpx;
        padding: 0px 24rpx;
        align-items: center;
        flex-shrink: 0;
        border-radius: 8rpx;
        border: 2rpx solid var(--Border-4, #E6E6E8);
    }

    .formFlex {
        width: 686rpx;
        height: 96rpx;
        margin-bottom: 30rpx;
        display: flex;
        justify-content: space-between;

        .codeInput {
            display: flex;
            flex: 1;
            height: 96rpx;
            padding: 0px 24rpx;
            align-items: center;
            flex-shrink: 0;
            border-radius: 8rpx;
            border: 2rpx solid var(--Border-4, #E6E6E8);
            margin-right: 30rpx;
        }

        .btnBox {
            width: 256rpx;
            height: 96rpx;

            :deep(button) {
                width: 100%;
                height: 100%;
            }
        }
    }

}

.submitBtn {
    margin: 30rpx 32rpx;
    width: 686rpx;
    height: 96rpx;

    :deep(button) {
        width: 100%;
        height: 100%;
    }
}
</style>