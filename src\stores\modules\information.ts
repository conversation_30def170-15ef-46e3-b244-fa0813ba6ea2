import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useInformationStore = defineStore(
    'information',
    () => {
        // 消息
        const information = ref<any>({car:10,system:5})
        // 更新消息
        const setInformation = (val: any) => {
            information.value = val
        }

        // 记得 return
        return { information, setInformation }
    },
    {
        // 配置持久化
        persist: {
            // 调整为兼容多端的API
            storage: {
                setItem(key, value) {
                    uni.setStorageSync(key, value)
                },
                getItem(key) {
                    return uni.getStorageSync(key)
                },
            },
        },
    },
)