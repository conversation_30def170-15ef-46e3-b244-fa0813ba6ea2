<template>
    <view class="main">
        <code-elf-guide v-if="guidePages"></code-elf-guide>
    </view>
</template>

<script lang="ts" setup>
import codeElfGuide from '@/components/code-elf-guide/code-elf-guide.vue'
import {ref} from "vue";
import {onLoad} from "@dcloudio/uni-app";

const guidePages = ref(false)
const loadExecution = () => {
    /**
     * 获取本地存储中launchFlag的值
     * 若存在，说明不是首次启动，直接进入首页；
     * 若不存在，说明是首次启动，进入引导页；
     */
    try {
        // 获取本地存储中launchFlag标识
        const value = uni.getStorageSync('launchFlag');
        console.log(value)
        if (value) {
            uni.navigateTo({
                url: '/pages/role/role'
            });
        } else {
            guidePages.value = true
        }
    } catch (e) {
        uni.setStorage({
            key: 'launchFlag',
            data: true,
            success: function () {
                console.log('error时存储launchFlag');
            }
        });
        guidePages.value = true
    }
}
onLoad(()=>{
    loadExecution()
})
</script>

<style>
page, .main {
    width: 100%;
    height: 100%;
}
</style>
