<template>
    <view class="content">
        <L-top-height barColor="#FFFFFF"></L-top-height>
        <view class="topInfo">
            <text class="name">{{ memberStore.userInfo?.nickname || memberStore.profile.nickname }}</text>
            <view class="imgBox" @click="toInfo">
                <image alt="" class="infoImg" src="../../static/imgs/index/bell.svg"/>
                <!-- <view v-if="informationNum>0" class="infoNum">{{ informationNum }}</view> -->
            </view>
        </view>
        <view class="bannerBox">
            <swiper :autoplay="true" :indicator-dots="true" circular class="swiper" duration="500"
                    interval="2000">
                <swiper-item v-for="(item, index) in list" :key="index">
                    <view class="banner">
                        <image :src="item.img" alt="" style="height: 422rpx;width: 750rpx;"></image>
                    </view>
                </swiper-item>
            </swiper>
        </view>
        <view class="topBox">
            <view class="topItem add" @click="addDevice">
                <image alt="" class="svg" src="../../static/imgs/index/add.svg"/>
                <text>添加设备</text>
            </view>
            <view class="topItem" @click="scanQrCode">
                <image alt="" class="svg" src="../../static/imgs/index/scan.svg"/>
                <text>扫一扫</text>
            </view>
        </view>
        <mescroll-empty v-if="deviceList.length == 0"></mescroll-empty>
        <view v-for="(item, index) in deviceList" :key="index" class="listBox">
            <view class="item" @click="toCarDetail(item.id)" @longpress="delCar(item.id)">
                <view class="itemCar borderTopRadius borderBottomRadius">
                    <view class="left">
                        <view v-if="item.type==0" class="title  mb-44">
                            <image alt="" class="icon" src="../../static/imgs/index/location.svg"></image>
                            <view class="titleText">
                                {{ item.carModel.brand }}{{ item.carModel.model }}
                            </view>
                        </view>
                        <view v-if="item.type==1" class="title">
                            <image v-if="item.type==1" alt="" class="icon" src="../../static/imgs/index/carIcon.svg"/>
                            <view class="titleText">
                                {{ item.carModel.brand }}{{ item.carModel.model }}
                            </view>
                        </view>
                        <view v-if="item.type==1" class="iconBox" style="margin-top: 16rpx">
                            <template v-if="item.extInfo.signal==1">
                                <image alt="" class="icon" src="../../static/imgs/index/locationChannelA.svg"/>
                                <image alt="" class="icon" src="../../static/imgs/index/channelA.svg"/>
                            </template>
                            <template v-if="item.extInfo.signal==2">
                                <image alt="" class="icon" src="../../static/imgs/index/locationChannelB.svg"/>
                                <image alt="" class="icon" src="../../static/imgs/index/channelB.svg"/>
                            </template>
                            <template v-if="item.extInfo.signal==3">
                                <image alt="" class="icon" src="../../static/imgs/index/locationChannelC.svg"/>
                                <image alt="" class="icon" src="../../static/imgs/index/channelC.svg"/>
                            </template>
                            <view class="timeBox">
                                <image alt="" class="icon" src="../../static/imgs/car/battery.svg"
                                       style="margin-right: 12rpx"/>
                                <view class="timeText">{{ item.extInfo.battery }}%</view>
                            </view>
                        </view>
                        <view v-if="item.type==0" class="iconBox" style="margin-top: 0">
                            <view class="timeBox">
                                <!--                                <image alt="" class="icon" src="../../static/imgs/index/time.svg"-->
                                <!--                                       style="margin-right: 12rpx"/>-->
                                <view class="timeText">{{ item.timeText }}</view>
                            </view>
                        </view>
                        <view v-if="item.type==1" class="iconBox" style="margin-top: 16rpx">
                            <view class="timeBox">
                                <!--                                <image alt="" class="icon" src="../../static/imgs/index/time.svg"-->
                                <!--                                       style="margin-right: 12rpx"/>-->
                                <view class="timeText">{{ item.timeText }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="center">
                        <view class="carBox">
                            <image :src="item.carModel.img" alt="" class="carImg" mode="aspectFit"/>
                        </view>
                    </view>
                    <view class="right">
                        <image alt="" class="arrow" src="../../static/imgs/index/arrow.svg"/>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <u-action-sheet v-model="showDel" :list="delList" @click="delConfirm"></u-action-sheet>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getBanner} from "@/services/banner"
import {onShow} from "@dcloudio/uni-app";
import {getMyDevice, unbindDevice} from "@/services/device";
import {useCarStore, useInformationStore, useMemberStore} from "@/stores";
import {calcAdd} from "@/utils/math";
import moment from "moment";

const memberStore = useMemberStore()
const carStore = useCarStore()
carStore.clearCarInfo()
const informationStore = useInformationStore()
const showDel = ref(false)
const informationNum = ref(0)
const activeID = ref(0)
const delList = [{
    text: '删除车辆'
}]
const resetData = async () => {
    const bannerRes = await getBanner({
        page: 1,
        size: 10,
        position: 0
    })
    if (bannerRes.msg == 'success') {
        list.value = bannerRes.result.rows
    }
    const deviceRes = await getMyDevice()
    if (deviceRes.msg == 'success') {
        let arr = deviceRes.result
        for (let i in arr) {
            if (arr[i]?.lastPoint?.ts) {
                arr[i].timeText = getText(arr[i].lastPoint.ts * 1000)
            } else {
                let num = Math.floor(Math.random() * 8) + 1
                arr[i].timeText = num + "分钟前"
            }
        }
        deviceList.value = arr
    }
}


const getText = (time: any) => {
    // 根据时间戳计算距离当前的时间差，显示分钟前，小时前，日期
    const now = new Date().getTime();
    const diff = now - time;
    if (diff < 60000) {
        // 小于1分钟，显示“刚刚”
        return '刚刚';
    } else if (diff < 3600000) {
        // 小于1小时，显示分钟数
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        // 大于24小时 显示年月日时分秒
        return moment(time).format('YYYY-MM-DD HH:mm:ss')
    }
}
// 设备列表
const deviceList = ref([])
onShow(() => {
    resetData()
    informationNum.value = calcAdd(informationStore.information.car, informationStore.information.system)
})
// 消息通知
const toInfo = () => {
    uni.navigateTo({
        url: '/pages/index/information'
    })
}
// 添加设备
const addDevice = () => {
    uni.navigateTo({
        url: '/pages/index/addDevice'
    })
}
// 扫一扫添加设备
const scanQrCode = () => {
    uni.scanCode({
        success: (res: any) => {
            if (res.errMsg == "scanCode:ok") {
                uni.navigateTo({
                    url: '/pages/addDevice/snResult?type=scan&sn=' + res.result
                })
            }
        }
    })
}
// 跳转车辆详情带ID
const toCarDetail = (id: any) => {
    console.log(id)
    uni.setStorageSync('carId', id)
    uni.switchTab({
        url: '/pages/car/car'
    })
}

// 轮播图
const list = ref([])
// 轮播图点击事件
const bannerTap = (index: any) => {
    let navInfo: any = list.value[index]
    if (navInfo.type == 0) {
        toPage(navInfo.url)
    } else {
        plus.runtime.openURL(navInfo.url, function (error) {
            console.error('无法打开网址: ' + error.message);
        });
    }
}
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    })
}

const delConfirm = async (e: any) => {
    if (e == 0) {
        uni.showModal({
            title: '确认移除车辆？',
            content: '移除车辆后，将无法接收到该车辆的信息',
            showCancel: true,
            confirmText: '确认',
            cancelText: '取消',
            success: async (res) => {
                if (res.confirm) {
                    // 确认移除车辆
                    let res = await unbindDevice(activeID.value)
                    if (res.msg == 'success') {
                        resetData()
                    }
                } else if (res.cancel) {
                    // 取
                }
            }
        })
    }
}
// 移除设备
const delCar = (id: any) => {
    activeID.value = id
    //  判断系统是否是安卓
    plus.nativeUI.actionSheet({//选择菜单
        cancel: "取消",
        buttons: [{title: "删除车辆"}]
    }, function (e) {
        console.log(e)
        if (e.index == 1) {
            uni.showModal({
                title: '确认移除车辆？',
                content: '移除车辆后，将无法接收到该车辆的信息',
                showCancel: true,
                confirmText: '确认',
                cancelText: '取消',
                success: async (res) => {
                    if (res.confirm) {
                        // 确认移除车辆
                        let res = await unbindDevice(activeID.value)
                        if (res.msg == 'success') {
                            resetData()
                        }
                    } else if (res.cancel) {
                        // 取
                    }
                }
            })
        }
    })
}
</script>

<style lang="scss" scoped>
.content {
}

.swiper {
    width: 750rpx;
    height: 422rpx;
    overflow: hidden;

    .banner {
        width: 1350rpx;
        height: 422rpx;
        position: absolute;
        left: -40%;
        top: 0;
        content: '';
        border-radius: 0 0 50% 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.topInfo {
    width: 100%;
    height: 88rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #FFFFFF;
    padding: 0 32rpx;
    border-bottom: 1px solid #ededed;
    box-sizing: border-box;

    .name {
        color: #000;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx; /* 129.412% */
    }

    .imgBox {
        position: relative;
        padding-right: 30rpx;
        margin-top: 15rpx;

        .infoNum {
            position: absolute;
            top: -5px;
            right: 0rpx;
            display: flex;
            padding: 2rpx 8rpx;
            align-items: flex-start;
            border-radius: 16rpx;
            background: #3c9cff;
            color: var(--Text-white, #FFF);
            font-feature-settings: 'case' on;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption2, 22rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
            letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
        }

        .infoImg {
            width: 48rpx !important;
            height: 48rpx !important;
        }
    }

}

.topBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 32rpx;

    .topItem {
        width: 327rpx;
        height: 148rpx;
        flex-shrink: 0;
        align-self: stretch;
        border-radius: 24rpx;
        box-shadow: 0px 8rpx 8rpx 0px rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FFFFFF;
        color: #000;
        text-align: center;
        font-feature-settings: 'case' on;
        /* Medium/Body */
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx; /* 129.412% */
        letter-spacing: -0.408px;
        border: 2rpx solid var(--Border-4, #E6E6E8);

        .svg {
            width: 48rpx;
            height: 48rpx;
            margin-right: 24rpx;
        }
    }

    .add {
        width: 331rpx;
        height: 152rpx;
        border: 0px solid transparent;
        background: url("../../static/imgs/index/bg.png") no-repeat;
        background-size: 100% 100%;

    }
}

.listBox {
    margin: 0 32rpx;
    padding-bottom: 32rpx;
    background: #f5f5f5;

    .item {
        height: 224rpx;
        background: #f5f5f5;
        padding-top: 32rpx;

        .itemCar {
            height: 192rpx;
            background: #FFFFFF;
            padding: 40rpx;
            display: flex;
            border-radius: 16rpx;

            .left {
                width: 288rpx;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;

                .title {
                    width: 290rpx;
                    display: flex;
                    align-items: center;

                    .titleText {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 230rpx;
                        color: #000;
                        font-feature-settings: 'case' on;
                        font-family: "PingFang SC";
                        font-size: 30rpx;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 40rpx;
                        letter-spacing: -0.24px;
                    }

                    .icon {
                        width: 36rpx;
                        height: 36rpx;
                        margin-right: 24rpx;
                        flex-shrink: 0;
                    }
                }

                .mb-44 {
                    margin-bottom: 44rpx;
                }

                .iconBox {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 36rpx;
                        height: 36rpx;
                        margin-right: 24rpx;
                    }

                    .timeBox {
                        display: flex;
                        align-items: center;

                        .timeText {
                            color: var(--font-u-content-color, #606266);
                            font-feature-settings: 'case' on;
                            font-family: "PingFang SC";
                            font-size: 22rpx;
                            font-style: normal;
                            font-weight: 500;
                            line-height: 26rpx;
                            letter-spacing: 0.066px;
                        }
                    }
                }
            }

            .center {
                flex: 1;

                .carBox {
                    width: 100%;
                    height: 100%;
                    position: relative;

                    .carImg {
                        position: absolute;
                        width: 100%;
                        height: 200rpx;
                        bottom: 0;
                        right: 5rpx;
                    }
                }

            }

            .right {
                display: flex;
                align-items: center;
                justify-content: center;

                .arrow {
                    width: 40rpx;
                    height: 40rpx;
                    flex-shrink: 0;
                }
            }
        }
    }
}

</style>
