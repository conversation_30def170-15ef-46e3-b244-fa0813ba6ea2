<script setup>
import {getGoodsAPI} from '@/services/goods'
import {ref} from 'vue'
import DaDropdown from '@/components/da-dropdown/index.vue'
import {onShow} from "@dcloudio/uni-app";
import GoodItem from "@/pages/shop/components/GoodListSort.vue";

const isHot = ref(false)
const isNew = ref(false)
const activeItem = ref(0)
const goodList = ref([])

const currentPage = ref(1)
const pageSize = ref(12)
const totalItems = ref(0)
const orderBy = ref([
    {
        title: '综合',
        value: 'asc',
        type: 'cell',
        prop: 'commentCount',
        showAll: false,
        showIcon: true,
        options: [
            {label: '综合', value: 'asc'},
            {label: '评论', value: 'commentCount'},
        ],
    },
    {
        title: '销量',
        value: '',
        type: 'cell',
        prop: 'sale',
        showAll: false,
        showIcon: true,
        options: [
            {label: '销量', value: 'sale'},
        ],
    },
    {
        title: '价格',
        type: 'sort',
        prop: 'price',
        value: '',
        options: [
            {label: '价格'},
        ],
    },
])
const props = defineProps({
    keyword: {
        type: String,
        default: '',
    },
    category: {
        type: String,
        default: '',
    }
})
const keyword = ref(props.keyword)
const category = ref(props.category)
const getGoodList = async () => {
    try {
        let data = {
            page: currentPage.value,
            size: pageSize.value,
            categoryId:category.value||'',
            sort: '+' + orderBy.value[activeItem.value].value,
            q:keyword.value||'',
        }
        if(activeItem.value==0&& orderBy.value[activeItem.value].value=='asc'){
            data.sort = '+'
        }
        // 排序
        if (data.sort == '+') {
            delete data.sort
        }
        if(activeItem.value==2&& orderBy.value[activeItem.value].value=='asc'){
            data.sort = '+price'
        }
        if(activeItem.value==2&& orderBy.value[activeItem.value].value=='desc'){
            data.sort = '-price'
        }
        const res = await getGoodsAPI(data)
        if (res.msg === 'success') {
            totalItems.value = res.result.count
            let list = res.result.rows
            console.log( res.result)
            for(let i in list){
                list[i].tags  = getGoodDetail(list[i])
            }
            goodList.value = list
        }
    } catch (error) {
        console.log(error)
    }
}
const getGoodDetail = async (item) => {
    let arr = []
    if (item.isPreSale) {
        if (item.isNew) {
            arr.push('新品预售')
        } else {
            arr.push('预售')
        }
    } else {
        if (item.isNew) {
            arr.push('New')
        }
    }
    if (item.isHot) {
        arr.push('Hot')
    }
    return arr
};
onShow(() => {
    category.value = props.category
    keyword.value = props.keyword
    getGoodList()
})

const checkPresale = ref([])
const updateNum = ref(0)
const handleConfirm = (v) => {
    console.log('handleConfirm ==>', v)
    // 获取v的唯一对象值
    let list = JSON.parse(JSON.stringify(orderBy.value))
    let value = ''
    for(let i in v ){
        value = v[i]
    }
    for (let i in v) {
        for (let n in list) {
            if (i == list[n].prop) {
                activeItem.value = n
                if(n==2){
                    if(value == 'asc'){
                        list[2].value = value
                    }else if(value == 'desc'){
                        list[2].value = value
                    }else{
                        list[2].value = ''
                    }
                }else{
                    list[n].value = list[n].options[0].value
                    list[n].title = list[n].options[0].label
                    if(value == 'commentCount'&&n==0){
                        list[n].value = list[n].options[1].value
                        list[n].title = list[n].options[1].label
                    }
                }
                goodList.value = []
            } else {
                list[n].value = ''
                list[n].title = list[n].options[0].label||'价格'
            }
        }
    }

    updateNum.value++
    orderBy.value = list
    getGoodList()
    console.log(list)
}
const handleClose = (v) => {
    console.log('handleClose ==>', v)
}
const handleOpen = (v) => {
    console.log('handleOpen ==>', v)
}
const  searchKeyWorld = (e) => {
console.log(e)
    keyword.value = e
    getGoodList()
}
</script>
<template>
    <XtxTopBar @search="searchKeyWorld" :word="props.keyword" />
    <view class="ProductNew">
        <!-- 自定义导航栏 -->
        <DaDropdown
            :key="updateNum"
            :dropdownMenu="orderBy"
            :duration="300"
            textColor="#909399"
            themeColor="#000000"
            @close="handleClose"
            @confirm="handleConfirm"
            @open="handleOpen">
            <template #slot1="{item,index}">
                <view style="padding: 40px">自定义插槽内容</view>
            </template>
        </DaDropdown>
        <view class="productblock" style="background: #f5f5f5;margin-top: 32rpx">
            <view class="goodList">
                <view v-if="goodList.length > 0" class="collectItemContent">
                        <GoodItem :orderGood="item" v-for="item in goodList" :key="item.id" class="goodItem"></GoodItem>
                </view>
                <view v-else class="noContent">
                    <view class="noContentText">暂无数据</view>
                </view>
            </view>
        </view>
    </view>
</template>
<style lang="scss" scoped>
.ProductNew {
    width: 750rpx;
    background: #f5f5f5 !important;
    min-height: 100vh;


    .order {
        width: 750rpx;
        padding: 24rpx 36rpx;
        box-sizing: border-box;

        .left {
            display: flex;

            .order-item {
                width: 150rpx;
                height: 72rpx;
                box-sizing: border-box;
                display: flex;
                padding: 20rpx 24rpx;
                justify-content: center;
                align-items: center;
                gap: 8rpx;
                border-radius: 8rpx;
                border: 2rpx solid #ebebeb;
                background: var(--Normal-White, #fff);
                color: var(--Normal-Black-333, #333);
                font-family: 'PingFang HK';
                font-size: 20rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 20px */
                margin-right: 20rpx;
            }

            .activeItem {
                width: 150rpx;
                height: 72rpx;
                box-sizing: border-box;
                display: flex;
                padding: 20rpx 24rpx;
                justify-content: center;
                align-items: center;
                gap: 8rpx;
                border-radius: 8rpx;
                border: 2rpx solid var(--normal-black-30, rgba(0, 0, 0, 0.3));
                background: var(--Normal-Black-333, #333);
                color: var(--Normal-White, #fff);
                font-family: 'PingFang HK';
                font-size: 20rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 20px */
                margin-right: 20rpx;
            }
        }

        .right {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            margin-top: 32rpx;

            :deep(.uni-checkbox-input) {
                width: 24rpx;
                height: 24rpx;
            }

            :deep(.uni-checkbox-input svg) {
                width: 24rpx;
                height: 24rpx;
            }

            :deep(.checklist-text span) {
                color: var(--Normal-White, #000);
                font-family: 'PingFang HK';
                font-size: 20rpx;
                font-style: normal;
                font-weight: 500;
                margin-right: 20rpx;
                margin-bottom: 5rpx;
            }

            :deep(.checklist-box) {
                margin-left: 25rpx;
                margin-right: 0;
            }

            :deep(.checklist-group) {
                justify-content: flex-end;
            }
        }
    }

    .goodList {
        padding: 0rpx 36rpx 32rpx;

        .collectItemContent {
            width: 100%; /* 确保宽度为100% */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between; /* 第一个放在最左侧 */
            .goodItem {
                display: flex;
                width: 324rpx;
                flex-direction: column;
                align-items: flex-start;
                gap: 16rpx;
                flex-shrink: 0;
                background: #FFFFFF;
                margin-bottom: 30rpx;

                .goodItemImg {
                    position: relative;

                    .tagBlock {
                        width: 100%;
                        position: absolute;
                        top: 24rpx;
                        left: 17rpx;
                        height: 30rpx;
                        display: flex;
                        gap: 0px 12rpx;

                        .Hot {
                            width: 80rpx;
                            height: 40rpx;
                            border-radius: 100rpx;
                            background: #16bd45;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: var(--Normal-White, #FFF);
                            font-family: "SF Pro Display";
                            font-size: 18rpx;
                            font-style: normal;
                            font-weight: 700;
                            letter-spacing: 0.18px;
                        }

                        .new {
                            width: 80rpx;
                            height: 40rpx;
                            border-radius: 100rpx;
                            background: #ff4500;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #fff;
                            text-align: center;
                            font-family: 'PingFang HK';
                            font-size: 16rpx;
                            font-style: normal;
                            font-weight: 700;
                        }
                    }

                    .cart {
                        cursor: pointer;
                        position: absolute;
                        right: 0px;
                        top: 0px;
                        width: 44px;
                        height: 44px;
                        background: #16bd45;
                        border-radius: 50%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    image {
                        width: 100%;
                        height: 324rpx;
                        border-radius: 8rpx 8rpx 0px 0px;
                        border: 1rpx solid #EFEFEF;
                    }
                }

                .green {
                    color: var(--Brand-Green-Highlight, #19D454);
                    font-family: "SF Pro Display";
                    font-size: 16rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 125%; /* 20px */
                    letter-spacing: 0.16px;
                    margin: 10rpx 16rpx;
                }

                .grey {
                    color: var(--Brand-Green-Highlight, #999);
                    font-family: "SF Pro Display";
                    font-size: 16rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 125%; /* 20px */
                    letter-spacing: 0.16px;
                    margin: 10rpx 16rpx;
                }

                .titleGood {
                    color: var(--Title-Text, #262B34);
                    font-family: "PingFang HK";
                    font-size: 20rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 125%; /* 25px */
                    letter-spacing: 0.2px;
                    margin: 0 16rpx 10rpx;
                }


                .price {
                    display: flex;
                    align-items: center;
                    margin: 0 16rpx 24rpx;

                    .now {
                        color: var(--Brand-Green-Primary, #3C9CFF);
                        font-family: "SF Pro Display";
                        font-size: 20rpx;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 100%; /* 20px */
                        letter-spacing: 0.2px;
                        margin-right: 16rpx;
                    }

                    .oldPrice {
                        color: #666;
                        font-family: "SF Pro Display";
                        font-size: 20rpx;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 100%; /* 20px */
                        letter-spacing: 0.2px;
                        text-decoration: line-through;
                    }
                }
            }


        }
    }

    .noContent {
        width: 100%;
        min-height: 400px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}
</style>
