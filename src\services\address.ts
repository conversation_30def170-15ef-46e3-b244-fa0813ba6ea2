const shopURL = 'https://shop.evl.usr.86.ltd/srv'
import { http } from '@/utils/http'

/**
 * 添加收货地址
 * @param data 请求参数
 */
export const postMemberAddressAPI = (data: any) => {
  return http({
    method: 'POST',
    url: shopURL+'/v1/me/address',
    data,
  })
}

/**
 * 获取收货地址列表
 */
export const getAddressListAPI = (data:any) => {
  return http<any>({
    method: 'GET',
    url: shopURL+'/v1/me/address',
    data
  })
}


/**
 * 修改收货地址
 * @param id 地址id(路径参数)
 * @param data 表单数据(请求体参数)
 */
export const putMemberAddressByIdAPI = (id: string, data: any) => {
  return http({
    method: 'PUT',
    url: shopURL+`/v1/me/address/${id}`,
    data,
  })
}


export const deleteMemberAddressByIdAPI = (id: string) => {
  return http({
    method: 'DELETE',
    url: shopURL+`/v1/me/address/${id}`,
  })
}
