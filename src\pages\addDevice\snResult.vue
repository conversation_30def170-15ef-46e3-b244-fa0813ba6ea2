<template>
	<view class="mainBox">
		<view class="top">
			已搜索到设备
		</view>

		<view class="carBox" v-if="carInfo.type==0">
			<view class="carItem" v-if="carStore.carInfo.model" @click="toSel">
				<image class="carImg" :src="carStore.carInfo.img" mode="aspectFit"></image>
				<view class="carText">{{ carStore.carInfo.model }}</view>
			</view>
			<view class="carItem" v-else @click="toSel">
				<view class="carPlus">
					<image class="plus" src="../../static/imgs/addDevice/plus.svg" mode="aspectFit"></image>
				</view>
				<view class="carText">请选择绑定车型</view>
			</view>
		</view>
		<view class="carBox" v-if="carInfo.type==1">
			<view class="carItem">
				<image class="carImg" :src="carInfo.carModel.img" mode="aspectFit"></image>
				<view class="carText">{{carInfo.carModel.model }}</view>
			</view>
		</view>
		<view class="listBox">
			<view class="listItem borderTopRadius borderBottomRadius">车辆SN
				<view class="text">{{ sn }}</view>
			</view>
		</view>
		<view class="listBox">
			<view class="auto borderTopRadius borderBottomRadius">车辆电压
				<u-radio-group v-model="voltage" :wrap="true" style="width: 300rpx">
					<u-radio  :name="item.value"      v-for="(item, index) in voltageList"
      :key="index">{{item.desc}}</u-radio>
				</u-radio-group>
			</view>
		</view>
    <view class="listBox" v-if="voltage==9999">
      <view class="listItem borderTopRadius borderBottomRadius">自定义电压
        <u-number-box :min="36" :max="120" v-model="num" style="margin-left: 10px"></u-number-box>
      </view>
    </view>
		<view class="btnBox">
			<u-button
				:type="isCarModelSelected ? 'primary' : 'info'"
				:disabled="!isCarModelSelected"
				@click="toResult">
				绑定
			</u-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, computed } from 'vue'
	import UButton from "@/uni_modules/vk-uview-ui/components/u-button/u-button.vue";
	import { addDeviceSn, getCarInfoBySn } from "@/services/device";
	import { useCarStore } from '@/stores'
	import { onShow } from "@dcloudio/uni-app";
	const carStore = useCarStore()
	const props = defineProps({
		type: {
			type: String,
			default: 'sn'
		},
		sn: {
			type: String,
			default: ''
		}
	})
	const voltageList = [
		{
			desc: '48V',
			value: 48000,
			content: '48000'
		},
		{
			desc: '60V',
			value: 60000,
			content: '60000'
		},
		{
			desc: '72V',
			value: 72000,
			content: '72000'
		}, {
			desc: '自定义',
			value: 9999,
			content: '0'
		}
	]
  const num = ref(36)
	const voltage = ref(48000)
	const carInfo = ref({})

	// 计算属性：检查是否已选择车型
	const isCarModelSelected = computed(() => {
		return !!(carStore.carInfo.modelID)
	})
	const toSel = () => {
		uni.navigateTo({
			url: '/pages/addDevice/selBrand'
		})
	}
	// 页面跳转函数
	const toResult = async () => {
		// 检查是否已选择车型
		if (!isCarModelSelected.value) {
			uni.showToast({
				icon: 'none',
				title: '请先选择车型',
				duration: 2000
			})
			return
		}

		let res = await addDeviceSn({
			sn: props.sn,
			carModelId: carStore.carInfo.modelID,
      voltage: voltage.value == 9999 ? num.value*1000 : voltage.value
		})
		if (res && res.msg === 'success') {
			navigateToPage(0);
		} else if (res && res.msg === '设备已被绑定') {
			navigateToPage(2);
		} else if (props && props.type) {
			const status = props.type === 'scan' ? 3 : 1;
			navigateToPage(status);
		} else {
			uni.showToast({
				icon: 'none',
				title: 'res or props is invalid'
			})
		}
	}
	const navigateToPage = (status : any) => {
		uni.navigateTo({
			url: `/pages/addDevice/bindResult?status=${status}`
		});
	}
	onShow(async () => {
		let res = await getCarInfoBySn(props.sn)
		if (res.msg == 'success') {
			if (res.result) {
				carInfo.value = res.result
			} else {
				uni.showToast({
					icon: 'none',
					title: '设备不存在',
					duration: 2000
				})
			}
		} else {
			uni.showToast({
				icon: 'none',
				title: '设备不存在',
				duration: 2000
			})
		}
	})
</script>

<style lang="scss" scoped>
	.mainBox {
		width: 750rpx;
	}

	.top {
		color: var(--Text-main, #282A2E);
		font-family: "PingFang SC";
		font-size: 56rpx;
		font-style: normal;
		font-weight: 500;
		line-height: 68rpx;
		/* 121.429% */
		letter-spacing: 0.364px;
		padding: 48rpx 32rpx;
	}

	.listBox {
		padding: 32rpx 32rpx 0;
		border-radius: 16rpx;

		.listItem {
			padding: 0 32rpx;
			background: #FFFFFF;
			height: 112rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			color: var(--Text-main, #282A2E);
			font-feature-settings: 'case' on;
			font-family: var(--Font-Family, "PingFang SC");
			font-size: var(--Font-Size-Body, 30rpx);
			font-style: normal;
			font-weight: 400;
			line-height: var(--Line-Height-Body, 44rpx);
			/* 129.412% */
			letter-spacing: var(--Letter-Spacing-Body, -0.41px);

			.text {
				flex: 1;
				text-align: right;
				color: var(--Text-content, #53565C);
				font-family: var(--Font-Family, "PingFang SC");
				font-size: var(--Font-Size-Footnote, 26rpx);
				font-style: normal;
				font-weight: 400;
				line-height: var(--Line-Height-Footnote, 36rpx);
				/* 138.462% */
				letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
			}

		}
    .auto{
      padding: 0 32rpx;
      background: #FFFFFF;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--Text-main, #282A2E);
      font-feature-settings: 'case' on;
      font-family: var(--Font-Family, "PingFang SC");
      font-size: var(--Font-Size-Body, 30rpx);
      font-style: normal;
      font-weight: 400;
      line-height: var(--Line-Height-Body, 44rpx);
      /* 129.412% */
      letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }
	}

	.carBox {
		padding: 32rpx;
		height: 160rpx;
		margin-bottom: 64rpx;

		.carItem {
			padding: 26rpx 24rpx;
			background: #FFFFFF;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.carImg {
				height: 116rpx;
				width: 186rpx;
			}

			.carPlus {
				height: 116rpx;
				width: 186rpx;
				border-radius: 16rpx;
				border: 1px solid var(--Primary-disable, #94C9FF);
				opacity: 0.2;
				background: var(--Primary-disable, #94C9FF);
				display: flex;
				align-items: center;
				justify-content: center;

				.plus {
					width: 60rpx;
					height: 60rpx;
					flex-shrink: 0;
				}
			}

			.carText {
				margin-left: 24rpx;
				flex: 1;
				color: #3c9cff;
				font-family: var(--Font-Family, "PingFang SC");
				font-size: var(--Font-Size-Headline, 34rpx);
				font-style: normal;
				font-weight: 600;
				line-height: var(--Line-Height-Headline, 44rpx);
				/* 129.412% */
				letter-spacing: var(--Letter-Spacing-Headline, -0.41px);
			}
		}

	}

	.btnBox {
		position: fixed;
		bottom: 32rpx;
		left: 32rpx;
		right: 32rpx;
	}
</style>
