<template>
    <view class="top">没有搜索到设备</view>
    <view class="mainBox">
        <view class="cardBox borderBottomRadius borderTopRadius">
            <view class="cardTitle">你可以尝试以下操作</view>
            <view class="desc">1、确保设备已开机并将手机靠近设备</view>
            <view class="desc">2、确保他人已断开连接，蓝牙指示灯熄灭或闪烁即可连接</view>
            <view class="desc">3、将设备重启后再次连接</view>
            <view class="desc">4、在手机设置中关闭蓝牙后再打开</view>
        </view>
    </view>
    <view class="btnBox">
        <u-button  type="primary" @click="showCode = true" @tap="toBlueAdd" class="mb-32">重新搜索</u-button>
        <u-button plain  type="primary" @click="showCode = true" @tap="bindDevice">手动输入SN码绑定设备</u-button>
    </view>

</template>

<script lang="ts" setup>
import {onMounted} from 'vue'
// 获取页面初始列表
const getResetInfo = () => {
    let res = ''
    console.log(res)
    phone.value = ''
}
// 页面加载
onMounted(() => {
    // getResetInfo()
})
const toBlueAdd = () => {
   uni.navigateBack({delta:1})
}
const bindDevice = () => {
    uni.redirectTo({
        url: '/pages/addDevice/anAdd'
    })
}
</script>

<style lang="scss" scoped>
.top {
    color: var(--Text-main, #282A2E);
    /* Medium/Title1 */
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 64rpx; /* 121.429% */
    letter-spacing: 0.364px;
    padding: 48rpx 32rpx 0;
}
.mainBox{
    padding: 32rpx;
    .cardBox{
        padding: 32rpx;
        background: #FFFFFF;
        .cardTitle{
            color: #000;
            font-feature-settings: 'case' on;
            font-family: "PingFang SC";
            font-size: 30rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 40rpx; /* 133.333% */
            letter-spacing: -0.24px;
            margin-bottom: 28rpx;
        }
        .desc{
            color: #000;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption1, 24rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
            letter-spacing: var(--Letter-Spacing-Caption1, 0px);
            margin-bottom: 24rpx;
        }
    }
}


.btnBox{
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
    .mb-32{
        margin-bottom: 32rpx;
    }
}
</style>
