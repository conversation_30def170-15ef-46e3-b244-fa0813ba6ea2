@font-face {
  font-family: 'erabbit';
  src: url('https://at.alicdn.com/t/c/font_4009193_lnbhmo3yos.woff?t=1681201348304') format('woff'),
    url('https://at.alicdn.com/t/c/font_4009193_lnbhmo3yos.ttf?t=1681201348304') format('truetype'),
    url('https://at.alicdn.com/t/c/font_4009193_lnbhmo3yos.svg?t=1681201348304#erabbit')
      format('svg');
}

[class^='icon-'],
[class*=' icon-'] {
  font-family: 'erabbit' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mail:before {
  content: '\e644';
}

.icon-weixin:before {
  content: '\e620';
}

.icon-phone:before {
  content: '\e618';
}

.icon-camera-plus:before {
  content: '\e636';
}

.icon-close:before {
  content: '\e6e1';
}

.icon-help:before {
  content: '\e617';
}

.icon-cart:before {
  content: '\e6d1';
}

.icon-settings:before {
  content: '\e6cf';
}

.icon-clock:before {
  content: '\e609';
}

.icon-delete:before {
  content: '\e6ce';
}

.icon-checked:before {
  content: '\e6cc';
}

.icon-ring:before {
  content: '\e6cd';
}

.icon-locate:before {
  content: '\e6cb';
}

.icon-mark:before {
  content: '\e6ca';
}

.icon-check:before {
  content: '\e6c9';
}

.icon-handset:before {
  content: '\e6c8';
}

.icon-gift:before {
  content: '\e6c7';
}

.icon-currency:before {
  content: '\e6c6';
}

.icon-comment:before {
  content: '\e6c5';
}

.icon-caret:before {
  content: '\e6c3';
}

.icon-right:before {
  content: '\e6c2';
}

.icon-left:before {
  content: '\e6c1';
}

.icon-down:before {
  content: '\e6c0';
}

.icon-up:before {
  content: '\e6bf';
}

.icon-sort:before {
  content: '\e616';
}

.icon-clear:before {
  content: '\e6be';
}

.icon-user:before {
  content: '\e6bb';
}

.icon-scan:before {
  content: '\e6bc';
}

.icon-search:before {
  content: '\e632';
}

.icon-preview:before {
  content: '\e61d';
}

.icon-heart:before {
  content: '\e647';
}

.icon-filter:before {
  content: '\e629';
}

.icon-home:before {
  content: '\e8b9';
}

@font-face {
    font-family: 'PingFang SC';
    src: url('../static/css/font/PingFangHK-Medium.otf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Zen Dots';
    src: url('../static/css/font/ZenDots-Regular.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
  font-family: 'DIN Alternate';
  src: url('../static/css/font/DIN Alternate Bold.ttf');
  font-weight: 400;
  font-style: normal;
  font-display: swap; /* 控制加载期间的字体显示行为 */
}