import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useMemberStore = defineStore(
    'member',
    () => {
        // 会员信息
        const profile = ref<any>({})

        // 保存会员信息，登录时使用
        const setProfile = (val: any) => {
            profile.value = val
        }

        // 清理会员信息，退出时使用
        const clearProfile = () => {
            profile.value = null
        }

        const userInfo = ref<any>({})
        // 保存会员信息，登录时使用
        const setUserInfo = (val: any) => {
            userInfo.value = val
        }

        // 清理会员信息，退出时使用
        const clearUserInfo = () => {
            userInfo.value = null
        }
        // 记得 return
        return { profile, setProfile, clearProfile,userInfo,setUserInfo,clearUserInfo }
    },
    {
        // 配置持久化
        persist: {
            // 调整为兼容多端的API
            storage: {
                setItem(key, value) {
                    uni.setStorageSync(key, value)
                },
                getItem(key) {
                    return uni.getStorageSync(key)
                },
            },
        },
    },
)