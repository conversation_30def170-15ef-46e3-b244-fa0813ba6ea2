const shopURL = 'https://shop.evl.usr.86.ltd/srv'
import { http } from '@/utils/http'

/**
 * 商品详情
 * @param id 商品id
 */
export const getGoodsByIdAPI = (id: string) => {
  return http({
    method: 'GET',
    url: shopURL+`/v1/goods/${id}`,
  })
}

export const getGoodsReviewAPI = (params:any) => {
  return http<any>({
    url: shopURL+`/v1/goodsReview/${params.goodsId}`,
    data:params,
    method: "GET",
  })
}
export const getGoodsAPI = (params:any) => {
  return http<any>({
    url: shopURL+"/v1/goods",
    data:params,
    method: "GET",
  })
}



