<template>
  <u-navbar :is-back="true" title="消息">
    <template v-slot:right>
      <div class="barFlex">
        <image class="rightIcon" src="../../static/imgs/index/delete.svg" @tap="showDate"></image>
        <text class="barText" @tap="readAll">一键已读</text>
      </div>
    </template>
  </u-navbar>
  <view class="content">
    <view class="topBar">
      <u-tabs-swiper ref="tabs1" :current="current" :is-scroll="false" :list="list" :offset="[23,75]"
                     active-color="#282A2E"
                     bar-height="8" bar-width="64" count="unread" inactive-color="#53565C"
                     @change="tabsChange"></u-tabs-swiper>
    </view>
    <mescroll-body v-if="current==0" :down="downOption" top="0" @down="downCallback"
                   @init="mescrollInit" @up="upCallback">
      <view class="listBox">
        <view v-for="(item,i) in carMsgList" :key="item.id" class="itemContent" @click="dealTip(item)"
              @longpress="openActionSheet(item.id)">
          <view class="titleBox">
            <text :class="item.read==false ?'textTitle activeText':'textTitle'">
              {{ item.type == 1 ? '车辆发生震动' : '车辆已断电' }}
            </text>
            <text class="textSubtitle">{{ carName }}</text>
          </view>
          <text class="textContent">{{
              item.type == 1 ? '车辆发生震动告警，请及时检查。最后上报地点:' + address : '车辆无法检测电池信息，请及时检查。最后上报地点:' + address
            }}
          </text>
          <view class="clickBox">
            <text class="textDate">
              {{ formatDate(item.createTime, 'YYYY-MM-DD HH:mm:ss') }}
            </text>
            <view class="clickTips">查看车辆位置</view>
            <L-arrow></L-arrow>
          </view>
        </view>
      </view>

    </mescroll-body>
    <mescroll-body v-if="current==1" :down="downOption" top="0" @down="downCallback"
                   @init="mescrollInit" @up="upCallback">
      <view class="listBox">
        <view v-for="(item,i) in sysMsgList" :key="item.id" class="itemContent" @click="dealTip"
              @longpress="openActionSheet(item.id)">
          <view class="titleBox">
            <text class="textTitle">{{ item.title }}</text>
            <text class="textSubtitle">{{ item.subtitle }}</text>
          </view>

          <text class="textContent">{{ item.content }}</text>

          <view class="clickBox">
            <text class="textDate">
              {{ formatDate(item.timeStamp, 'YYYY-MM-DD HH:mm:ss') }}
            </text>
            <view class="clickTips">{{ getClickTips(item.type) }}</view>
            <L-arrow></L-arrow>
          </view>
        </view>
      </view>
    </mescroll-body>
  </view>
  <u-action-sheet v-model="showAction" :list="infoList" @click="actionConfirm"></u-action-sheet>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {onLoad, onNavigationBarButtonTap, onPageScroll, onReachBottom} from "@dcloudio/uni-app";
import {formatDate} from "@/utils/date";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import {useInformationStore} from "@/stores";
import {getInformationList,readInformation,readAllInformation} from "@/services/information";

const informationStore = useInformationStore()
const {mescrollInit} = useMescroll(onPageScroll, onReachBottom) // 调用mescroll的hook
// 右上角按钮点击事件
onNavigationBarButtonTap(() => {
  uni.navigateTo({
    url: '/pages/index/dnd'
  })
})
const showAction = ref(false)
const infoList = [{
  text: '标为已读'
}, {
  text: '删除消息',
  color: '#FF7070'
}]
// tab 列表
const list = ref([{
  name: '车辆消息',
  unread: 0
}, {
  name: '系统消息',
  unread: 0
}])
// tab 选中项
const current = ref(0)
const tabs1: any = ref(null)
const openActionSheet = () => {
  // showAction.value = true
  //  判断系统是否是安卓
  plus.nativeUI.actionSheet({//选择菜单
    cancel: "取消",
    buttons: [{title: "标为已读"}, {title: "删除消息"}]
  }, function (e) {
    console.log(e)
    if (e.index == 1) {
      uni.showToast({
        icon: 'none',
        title: '标为已读成功'
      })
    }
    if (e.index == 2) {
      uni.showToast({
        icon: 'none',
        title: '删除消息成功'
      })
    }
  })
}
// 处理通知
const dealTip = (item: any) => {
  readInformation(item.id).then((data: any) => {
console.log(data)
getDataList()
  }).catch((err) => {
    console.log(err)
  })
    if (plus.os.name == "Android") {
      uni.navigateTo({
        url: '/pages/car/carFence?deviceId='+ carID.value
      })
    } else {
      uni.navigateTo({
        url: '/pages/car/carFenceIOS?deviceId=' + carID.value
      })
    }
}
// 请求数据
const filter = ref({
  page: 1,
  size: 10,
  status: 1
})
const carMsgList = ref([])
const sysMsgList = ref([])
//0 骑行报告 1 车辆共享 2 异常移动提醒 3 充电提醒  11 账户异常 12 版本更新
const arrayList = ref([carMsgList.value, sysMsgList.value])
const getClickTips = (type: any) => {
  switch (type) {
    case 0:
      return "查看骑行"
  }
}
const mainSn = ref('')
const address = ref('')
const carName = ref('')
const carID = ref('')
onLoad((option: any) => {
  console.log(option)
  mainSn.value = option.sn
  address.value = option.carAddress
  carName.value = option.carName
  carID.value = option.carID
})
// mescroll配置
const downOption = {
  auto: false //是否在初始化后,自动执行downCallback; 默认true
}
/* mescroll配置自定义下拉刷新的回调,不使用useMescroll的downCallback */
const downCallback = (mescroll: any) => {
  getInformationList(mainSn.value).then((data: any) => {
    carMsgList.value = carMsgList.value.concat(data.result.rows)
    // todo  未处理数据
    mescroll.endSuccess(); // 请求成功, 结束下拉刷新的状态 (无参)
  }).catch(() => {
    mescroll.endErr(); // 请求失败, 结束加载
  })
}

/* mescroll配置上拉加载的回调: 其中mescroll.num:当前页 从1开始, mescroll.size:每页数据条数,默认10 */
const upCallback = (mescroll: any) => {
  getInformationList(mainSn.value).then((curPageData: any) => {
    carMsgList.value = carMsgList.value.concat(curPageData.result.rows)
    // todo  未处理数据
    mescroll.endSuccess(carMsgList.value.length); // 请求成功, 结束加载
  }).catch(() => {
    mescroll.endErr(); // 请求失败, 结束加载
  })
}
// 获取页面初始列表
const getDataList = () => {
  getInformationList(mainSn.value).then((curPageData: any) => {
    carMsgList.value = curPageData.result.rows
  }).catch(() => {
  })
}
// 页面加载
onMounted(() => {
})
// swiper-item左右移动，通知tabs的滑块跟随移动
const transition = (e: any) => {
  let dx = e.detail.dx;
  tabs1.value.setDx(dx);
}
// tabs通知swiper切换
const tabsChange = (index: any) => {
  current.value = index;
  filter.value.status = index;
}

const animationfinish = (e: any) => {
  let cur = e.detail.current;
  tabs1.value.setFinishCurrent(cur);
  current.value = cur;
}
// 一件已读
const readAll = () => {
  // list.value 的unread都设置成0
  // list.value = list.value.map(item => ({
  //   ...item,
  //   unread: 0
  // }));
  // informationStore.setInformation({car: 0, system: 0})
  readAllInformation(mainSn.value).then((data: any) => {
    console.log(data)
	getDataList()
  }).catch((err:any) => {
    console.log(err)
  })
}

const actionConfirm = (index: any) => {
  console.log(index)
}


</script>

<style lang="scss" scoped>
.barFlex {
  margin-right: 32rpx;
  display: flex;
  align-items: center;

  .rightIcon {
    width: 32rpx;
    height: 32rpx;
  }

  .barText {
    color: var(--Text-tips, #909399);
    text-align: center;
    font-feature-settings: 'case' on;
    /* Regular/Caption2 */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption2, 22rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
    letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
  }
}

.content {
  height: 100vh;
  flex-direction: column;
  display: flex;

  .topBar {
    height: 88rpx;

    :deep .u-scroll-bar {
      background: #3c9cff !important;
    }
  }

  .listBox { //list 整体
    padding: 32rpx;

    .itemContent { //item单项
      display: flex;
      margin-bottom: 24rpx;
      width: 686rpx;
      padding: 32rpx;
      flex-direction: column;
      align-items: flex-start;
      gap: 32rpx;
      flex-shrink: 0;
      border-radius: 16rpx;
      background: var(--Background-white, #FFF);

      .titleBox {
        display: flex;
        align-items: center;
        width: 100%;

        .textTitle {
          flex: 1;
          color: var(--Text-main, #282A2E);
          font-feature-settings: 'case' on;
          /* Medium/Subheadline */
          font-family: "PingFang SC";
          font-size: 30rpx;
          font-style: normal;
          font-weight: 600;
          position: relative;
		  padding-left: 0rpx;
        }
		.activeText{
			padding-left: 30rpx;
		}

        .activeText::before {
          position: absolute;
          top: 15rpx;
          content: '';
          left: 10rpx;
          width: 10rpx;
          height: 10rpx;
          border-radius: 10rpx;
          background: red;
          margin-right: 10rpx;
        }

        .textSubtitle {
          color: var(--Text-main, #282A2E);
          font-feature-settings: 'case' on;
          /* Medium/Subheadline */
          font-family: "PingFang SC";
          font-size: 30rpx;
          font-style: normal;
          line-height: 40rpx; /* 133.333% */
          letter-spacing: -0.24px;
        }
      }

      .textContent {
        flex: 1;
        color: var(--Text-content, #53565C);
        /* Regular/Caption1 */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption1, 24rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
      }

      .clickBox {
        display: flex;
        width: 100%;
        align-items: center;

        .textDate {
          flex: 1;
          color: #000;
          /* Regular/Caption1 */
          font-family: var(--Font-Family, "PingFang SC");
          font-size: var(--Font-Size-Caption1, 24rpx);
          font-style: normal;
          font-weight: 400;
          line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
          letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .clickTips {
          color: var(--Text-tips, #909399);
          /* Regular/Caption1 */
          font-family: var(--Font-Family, "PingFang SC");
          font-size: var(--Font-Size-Caption1, 24rpx);
          font-style: normal;
          font-weight: 400;
          line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
          letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .arrowIcon {
          margin: 4rpx;
        }
      }
    }
  }

  //.swiperBox {
  //    flex: 1;
  //    .swiperItem {
  //
  //    }
  //}
}


</style>
