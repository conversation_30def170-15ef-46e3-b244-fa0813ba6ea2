<template>
  <picker-view :value="pickerValue" @change="pickerChange" class="picker-view" :indicator-style="indicatorStyle" @tap.stop="returnHandle">
    <picker-view-column>
      <view class="picker-item" v-for="(item, index) in years" :key="index">{{item}}年</view>
    </picker-view-column>
    <picker-view-column>
      <view class="picker-item" v-for="(item, index) in months" :key="index">{{ item }}月</view>
    </picker-view-column>
    <picker-view-column v-if="days.length > 0">
      <view class="picker-item" v-for="(item, index) in days" :key="index">{{ item }}日</view>
    </picker-view-column>
  </picker-view>
</template>
<script>
    import moment from "moment/moment";

    export default {
      name: 'termPicker',
      props: {
			  defaultDate: {
				  type: String,
				  default: moment().format('YYYY-MM-DD')
			  },
        minYear: {
          type: Number,
          default: 2000,
        },
			  timeLimit: {
				  type: Boolean,
				  default: false
			  }
      },
      data() {
          const date = new Date();
          const years = [];
          const year = date.getFullYear();
          const months = [];
          const month = date.getMonth() + 1;
          let days = []
          const day = date.getDate();
          for (let i = this.minYear; i <= year; i++) {
              years.push(i);
          }
          for (let i = 1; i <= 12; i++) {
              months.push(i);
          }
          for (let i = 1; i <= 31; i++) {
            days.push(i)
          }
          return {
              indicatorStyle: 'height: 40px;',
              year,
              month,
              day,
              years,
              months,
              days,
              pickerValue: [],
              resultDate: ''
          };
      },
      mounted() {
        this.setDate()
      },
      methods: {
        returnHandle(){},
        setDate() {
          if (this.defaultDate) {
            let date = this.defaultDate
            this.resultDate = this.defaultDate
            this.setPicker(date)
          } else {
            let month = this.month < 10 ? '0' + this.month : this.month
            let day = this.day < 10 ? '0' + this.day : this.day
            let nowTime = this.year + '-' + month + '-' + day
            this.resultDate = nowTime
            this.setPicker(nowTime)
          }
        },
        setPicker(date) {
          const splitVal = date.split('-')
          let year = this.years.indexOf(Number(splitVal[0]))
          let month = Number(splitVal[1]) - 1
          let day = Number(splitVal[2]) - 1
          this.pickerChange({
            detail: {
              value: [year, month, day]
            }
          })
        },
        getDateTime(date) {
          let year = this.years[date[0]]
          let month = this.months[date[1]]
          let day = this.days[date[2]]
          if (month < 10) {
            month = '0' + month
          }
          if (day < 10) {
            day = '0' + day
          }
          this.resultDate =  year + '-' + month + '-' + day
          this.$emit('onChange', this.resultDate)
        },
        pickerChange(e) {
          const currents = e.detail.value
          if (currents[1] + 1 === 2) {
              this.days = []
              if (
                  ((currents[0] + this.minYear) % 4 === 0 &&
                      (currents[0] + this.minYear) % 100 !== 0) ||
                  (currents[0] + this.minYear) % 400 === 0
              ) {
                  for (let i = 1; i < 30; i++) {
                      this.days.push(i)
                  }
              } else {
                  for (let i = 1; i < 29; i++) {
                      this.days.push(i)
                  }
              }
          } else if ([4, 6, 9, 11].some((item) => currents[1] + 1 === item)) {
              this.days = []
              for (let i = 1; i < 31; i++) {
                  this.days.push(i)
              }
          } else if (
              [1, 3, 5, 7, 8, 10, 12].some((item) => currents[1] + 1 === item)
          ) {
              this.days = []
              for (let i = 1; i < 32; i++) {
                  this.days.push(i)
              }
          }
          this.pickerValue = currents
          this.getDateTime(currents)
        },
      }
    }
</script>

<style lang="scss" scoped>
    .picker-view {
    	width: 750rpx;
    	height: 480rpx;
    	background-color: #FFFFFF;
    	.picker-item {
    		height: 40px;
    		display: flex;
    		align-items: center;
    		justify-content: center;
    		text-align: center;
    	}
    }
</style>