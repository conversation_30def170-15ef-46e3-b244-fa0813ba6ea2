<template>
	<view style="padding: 32rpx">
		<view class="whiteBox">
			<u-collapse>
				<u-collapse-item :title="item.head" v-for="(item, index) in itemList" :key="index">
					{{ item.body }}
				</u-collapse-item>
			</u-collapse>
		</view>
		<view class="whiteBox">
			<u-collapse>
				<u-collapse-item :title="item.head" v-for="(item, index) in itemListA" :key="index*Math.random()">
					{{ item.body }}
				</u-collapse-item>
			</u-collapse>
		</view>
	</view>
</template>

<script setup lang="ts">
const itemList: any = [
	{
		head: '登录异常',
		body: '请先退出登录，再重启一段网络，再次尝试登录',
		open: true,
		disabled: true
	}
];

const itemListA: any = [
	{
		head: '绑定车辆',
		body: '点击首页的增加设备，扫一扫均可增加设备',
		open: true,
		disabled: true
	},
	{
		head: '更换手机号',
		body: '登录app->底部菜单我的->账号管理->手机号',
		open: false
	},
	{
		head: '车辆二维码',
		body: '点击首页扫码，进入绑定流程',
		open: false
	},
	{
		head: '切换车辆',
		body: '骑行页面下拉可以切换拥有的车辆',
		open: false
	}
];
</script>
<style scoped lang="scss">
.whiteBox {
	background: #ffffff;
	padding: 32rpx;
	border-radius: 16rpx;
	margin-bottom: 32rpx;
}
</style>
