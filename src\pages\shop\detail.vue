<script lang="ts" setup>
import skuPopup from '@/components/vk-data-goods-sku-popup/vk-data-goods-sku-popup'
import { addCartShopAPI } from '@/services/cart'
import { getGoodsByIdAPI, getGoodsReviewAPI } from '@/services/goods'
import { onLoad } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'
import ServicePanel from './components/ServicePanel.vue'
import Comment from './components/comment.vue'
import Price from './components/Price.vue'
import { useOrderStore } from '@/stores'
import { calcAdd } from '@/composables/math'
import moment from 'moment/moment'

const orderStore = useOrderStore()
// 购物车数量
const cartNumber = ref(0)
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 接收页面参数
const query = defineProps<{
    id: string
}>()
// 获取商品详情信息
const goods: any = ref<any>()
const getGoodsByIdData = async () => {
    const res: any = await getGoodsByIdAPI(query.id)
    console.log(res)
    goods.value = res.result
    let listA = res.result.specs
    for (let i in listA) {
        const arr = listA[i].values
        for (let n in arr) {
            arr[n] = {
                name: arr[n],
                selected: false,
            }
        }
    }
    goods.value.specs = listA
    resetReview()
}
const skuRef: any = ref()

// 页面加载
onLoad(() => {})
onMounted(() => {
    // uni.setNavigationBarTitle({
    //     title: title,
    // })
    getGoodsByIdData()
})

// 轮播图变化时
const currentIndex = ref(0)
const onChange = (ev:any) => {
    currentIndex.value = ev.detail.current
}

// 点击图片时
const onTapImage = (url: string) => {
    // 大图预览
    uni.previewImage({
        current: url,
        urls: goods.value!.mainPictures,
    })
}

// uni-ui 弹出层组件 ref
const popup = ref<{
    open: (type?: any) => void
    close: () => void
}>()

// 弹出层条件渲染
const popupName = ref<'address' | 'service'>()
const openPopup = (name: any) => {
    // 修改弹出层名称
    popupName.value = name
    popup.value?.open()
}


// 商品购买数量
const bugcount = ref(1)

// 加入购物车事件
const onAddCart = async () => {
    // 判断是否有库存
    if (skuObj.value.inventory <= 0) {
        uni.showToast({ icon: 'none', title: '商品库存不足' })
        return
    }
    console.log( skuObj.value )
    if (skuObj.value.skuId) {
        const { skuId } = skuObj.value
        const { goodsId } = skuObj.value.skuInfo
        const res = await addCartShopAPI({ skuId, goodsId, quantity:bugcount.value } )
        if(res.msg=='success'){
            uni.showToast({ icon: 'none', title: '加入购物车成功!' })
        }
    } else {
        // 规格没有选择 提示用户
        uni.showToast({ icon: 'none', title: '请选择商品规格!' })
    }
}
// 立即购买
const onBuyNow = () => {
    let sku = JSON.parse(JSON.stringify(skuObj.value))
    // 判断是否有库存
    if (sku.inventory <= 0) {
        uni.showToast({ icon: 'none', title: '商品库存不足!' })
        return
    }
    sku.countNum = bugcount.value
    console.log(sku)

    orderStore.setOrderInfo(sku)
    uni.navigateTo({
        url: '/pages/shop/createOrder?type=now',
    })
}
const activeIndex = ref(0)
const setActiveIndex = (index: any) => {
    activeIndex.value = index
}
const normalSku: any = ref({})
const skuObj: any = ref({})
const isPreSale = ref(false)
const skuChange = (sku: any) => {
    console.log(sku,999)
    selectSku.value = sku.specsText
    if (sku.skuId) {
        normalSku.value = sku
    }
    skuObj.value = sku
    if (sku.skuInfo?.isPreSale) {
        isPreSale.value = sku.skuInfo.isPreSale
    }
}
const selectSku = ref('请选择SKU规格')

const totalInfo: any = ref({})
const resetReview = async () => {
    let res = await getGoodsReviewAPI({ goodsId: goods.value.id, page: 1, size: 1000 })
    if (res.msg === 'success') {
        let arr = res.result.rows
        let scoreA = 0
        for (let i in arr) {
            scoreA = calcAdd(scoreA, arr[i].goodsRating)
        }
        totalInfo.value.count = res.result.count
        if (res.result.count > 0) {
            totalInfo.value.comment = res.result.rows[0]
        } else {
            totalInfo.value.comment = null
        }
        if (scoreA == 0) {
            totalInfo.value.goodsRating = 0
        } else {
            totalInfo.value.goodsRating = (scoreA / res.result.count).toFixed(2)
        }
    }
}
/**
 * 将给定的日期对象格式化为人类可读的字符串形式
 * 此函数使用了 moment.js 库来处理日期的格式化
 *
 * @param {any} date - 日期对象或可被 moment 解析的日期字符串
 * @returns {string} 格式化后的日期字符串，格式为 'YYYY-MM-DD HH:mm:ss'
 */
const formatDate = (date:any) => {
    return moment(date).format('YYYY-MM-DD HH:mm:ss')
}
let systemInfo = uni.getSystemInfoSync();
const windowHeight = systemInfo.windowHeight;
</script>

<template>
    <view
        class="viewport"
        :style="{ width: '750rpx', height: windowHeight + 'px' }"
    >
        <!-- 基本信息 -->
        <view id="good" class="goods">
            <!-- 商品主图 -->
            <view class="preview">
                <swiper circular @change="onChange"  style="height: 750rpx">
                    <swiper-item v-for="item in goods?.mainPictures" :key="item" style="height: 750rpx">
                        <image :src="item" class="image" mode="aspectFill" @tap="onTapImage(item)" />
                    </swiper-item>
                </swiper>
                <view class="indicator">
                    <text class="current">{{ currentIndex + 1 }}</text>
                    <text class="split">/</text>
                    <text class="total">{{ goods?.mainPictures.length }}</text>
                </view>
            </view>

            <!-- 商品简介 -->
            <view class="meta">
                <view class="name ellipsis">{{ goods?.name }}</view>
                <Price :goods="goods" :skuObj="skuObj"></Price>
            </view>

            <!-- 操作面板 -->
            <view class="action">
                <view class="item">
                    <text class="label">优惠</text>
                    <text class="text ellipsis">12月好物放送, 领券购买直降120元</text>
                </view>
                <view class="item">
                    <text class="label">邮费</text>
                    <text
                        v-if="  skuObj?.skuInfo?.shipping.name == '包邮' || normalSku?.skuInfo?.shipping.name == '包邮'"
                        class="text ellipsis"
                    >
                        包邮
                    </text>
                </view>
                <view class="item arrow" @tap="openPopup('service')">
                    <text class="label">服务</text>
                    <view class="text ellipsis">
                        <text>· 无忧退货</text>
                        <text>· 快速退款</text>
                        <text>· 免费包邮</text>
                    </view>
                </view>
            </view>
            <view id="review" class="action">
                <view v-if="goods?.specs.length > 0" class="item" style="padding-right: 0">
                    <skuPopup
                        ref="skuRef"
                        :bugcount="bugcount"
                        :goods="goods"
                        @bugcountChange="bugcount = $event"
                        @change="skuChange"
                    />
                </view>
            </view>
            <view class="action">
                <view class="reviewBox" @click="openPopup('review')">
                    <view class="reviewTop">
                        <view class="reviewLabel">评价</view>
                        <view class="reviewScore">{{ totalInfo?.goodsRating || 0 }}</view>
                        <view class="reviewRate">
                            <uni-rate
                                :size="18"
                                :value="totalInfo?.goodsRating"
                                active-color="#1c432b"
                                allow-half readonly
                            />
                        </view>
                        <view class="reviewDesc">共{{ totalInfo?.count || 0 }}条评价</view>
                        <view class="reviewArrow">
                            <svg fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.5 21L16.5 12L7.5 3"
                                    stroke="#333333"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="3"
                                />
                            </svg>
                        </view>
                    </view>
                    <view v-if="totalInfo?.comment" class="reviewComment">
                        <view class="reviewInfo">
                            <view class="reviewName">
<!--                                {{ totalInfo.comment.fname || totalInfo?.comment.member.nickname }}-->
                            </view>
                            <view class="reviewTime">{{ formatDate(totalInfo?.comment.createTime) }}</view>
                        </view>
                        <view class="reviewContent">{{ totalInfo?.comment.content }}</view>
                        <view class="reviewImgList">
                            <view v-for="item in totalInfo?.comment.imgs" :key="item" class="reviewImg">
                                <image :src="item" mode="aspectFill" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 商品详情 -->
        <view id="detail" class="detail panel">
            <view class="title">
                <text :class="activeIndex == 0 ? '' : ''" @tap="setActiveIndex(0)">商品描述</text>
            </view>
            <view class="content">
                <template v-if="activeIndex == 0">
                    <view class="contentBox">
                        <mp-html :content="goods?.desc" />
                    </view>
                    <view class="properties">
                        <!-- 属性详情 -->
                        <view v-for="item in goods?.properties" :key="item.name" class="item">
                            <text class="label">{{ item.name }}</text>
                            <text class="value">{{ item.value }}</text>
                        </view>
                    </view>
                    <!-- 图片详情 -->
                    <view class="contentBox">
                        <mp-html :content="goods?.content" />
                    </view>
                </template>
                <template v-if="activeIndex == 1">
                    <!-- 评论详情 -->
                    <view class="contentBox"></view>
                </template>
            </view>
        </view>

        <!-- 同类推荐 -->
        <view class="similar panel" v-if="goods?.similarProducts">
            <view class="title">
                <text>同类推荐</text>
            </view>
            <view class="content">
                <navigator
                    v-for="item in goods?.similarProducts"
                    :key="item.id"
                    :url="`/pages/goods/goods?id=${item.id}`"
                    class="goods"
                    hover-class="none"
                >
                    <image :src="item.picture" class="image" mode="aspectFill"></image>
                    <view class="name ellipsis">{{ item.name }}</view>
                    <view class="price">
                        <text class="symbol">¥</text>
                        <text class="number">{{ item.price }}</text>
                    </view>
                </navigator>
            </view>
        </view>
        <view style="height: 160rpx"></view>
    </view>

    <!-- 用户操作 -->
    <view v-if="goods" class="toolbar">
        <view class="buttons">
            <view class="addCart" @tap="onAddCart">加入购物车</view>
            <view class="payment" @tap="onBuyNow">
                立即购买
            </view>
        </view>
    </view>
    <!-- uni-ui 弹出层 -->
    <uni-popup ref="popup" background-color="#fff" type="bottom">
        <ServicePanel v-if="popupName == 'service'" @close="popup?.close()" />
        <view v-if="popupName == 'review'" style="padding: 20rpx 36rpx">
            <view className="commentTitle">
                评价
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                    @click="popup?.close()"
                >
                    <path
                        d="M4 4L20 20"
                        stroke="#333333"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                    />
                    <path
                        d="M4 20L20 4"
                        stroke="#333333"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                    />
                </svg>
            </view>
            <scroll-view scroll-y style="height: 800rpx">
                <Comment :goods="goods" @close="popup?.close()"></Comment>
            </scroll-view>
        </view>
    </uni-popup>
</template>

<style lang="scss">
.commentTitle {
    color: var(--Normal-Black-333, #333);
    font-family: 'PingFang SC';
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 28px */
    display: flex;
    justify-content: space-between;
    margin-top: 56rpx;
    margin-bottom: 32rpx;

    svg {
        width: 24rpx;
        height: 24rpx;
    }
}

.reviewBox {
    .reviewTop {
        display: flex;
        align-items: center;
        height: 124rpx;

        .reviewLabel {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 28px */
            margin-right: 24rpx;
        }

        .reviewScore {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 28px */
            margin-right: 16rpx;
        }

        .reviewRate {
            flex: 1;
        }

        .reviewDesc {
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
            margin-right: 10rpx;
        }

        .reviewArrow {
            width: 24rpx;
            height: 24rpx;
            flex-shrink: 0;

            svg {
                width: 24rpx;
                height: 24rpx;
                margin-bottom: 10rpx;
            }
        }
    }

    .reviewComment {
        .reviewInfo {
            display: flex;
            justify-content: space-between;
            margin-bottom: 32rpx;

            .reviewName {
                color: var(--Brand-Green-Primary, #3C9CFF);
                font-family: 'PingFang SC';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 24px */
            }

            .reviewTime {
                color: var(--Normal-Gray-999, #999);
                font-family: 'PingFang SC';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 100%; /* 24px */
            }
        }

        .reviewContent {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 30rpx;
            margin-bottom: 20rpx;
        }

        .reviewImgList {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 40rpx;
            border-bottom: 1px solid #ededed;

            .reviewImg {
                width: 160rpx;
                height: 160rpx;
                flex-shrink: 0;
                align-self: stretch;
                border-radius: 4px;
                border: 2px solid var(--Border-Border-Primary, #ededed);

                image {
                    width: 160rpx;
                    height: 160rpx;
                }
            }
        }
    }
}

.navbarBox {
    width: 750rpx;
    padding: 0rpx 38rpx 20rpx;
    box-sizing: border-box;

    .tabBox {
        width: 100%;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .tab {
            color: var(--Normal-Black-666, #666);
            text-align: justify;
            font-family: 'PingFang HK';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 42px */
        }

        .tabActive {
            color: var(--Normal-Black-333, #333);
            text-align: justify;
            font-family: 'PingFang HK';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 42px */
            position: relative;
        }

        .tabActive::after {
            content: '';
            position: absolute;
            bottom: -13rpx;
            left: -10rpx;
            width: 80rpx;
            height: 2px;
            background-color: #3C9CFF;
        }
    }
}

page {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.viewport {
    background: #ededed;
    overflow-y: scroll;
}

.cart-badge {
    position: absolute;
    top: 1px; /* 根据需要调整位置 */
    right: 15px; /* 根据需要调整位置 */
    width: 20px; /* 圆点的宽度 */
    height: 20px; /* 圆点的高度 */
    background-color: red; /* 圆点的颜色 */
    color: white; /* 字体颜色 */
    border-radius: 50%; /* 圆形 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px; /* 字体大小 */
}

.panel {
    margin-top: 20rpx;
    background-color: #fff;

    .title {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 90rpx;
        line-height: 1;
        padding: 30rpx 0rpx 30rpx;
        position: relative;

        text {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 28px */
            margin-right: 24rpx;
        }

        .active {
            color: #3C9CFF;
        }

        navigator {
            font-size: 24rpx;
            color: #666;
        }
    }
}

.arrow {
    &::after {
        position: absolute;
        top: 50%;
        right: 30rpx;
        content: '\e6c2';
        color: #000;
        font-weight: 600;
        font-family: 'erabbit' !important;
        font-size: 32rpx;
        transform: translateY(-50%);
    }
}

/* 商品信息 */
.goods {
    background-color: #ededed;

    .preview {
        height: 750rpx;
        position: relative;

        .image {
            width: 750rpx;
            height: 750rpx;
        }

        .indicator {
            height: 40rpx;
            padding: 0 24rpx;
            line-height: 40rpx;
            border-radius: 30rpx;
            color: #fff;
            font-family: Arial, Helvetica, sans-serif;
            background-color: rgba(0, 0, 0, 0.3);
            position: absolute;
            bottom: 30rpx;
            right: 30rpx;

            .current {
                font-size: 26rpx;
            }

            .split {
                font-size: 24rpx;
                margin: 0 1rpx 0 2rpx;
            }

            .total {
                font-size: 24rpx;
            }
        }
    }

    .meta {
        position: relative;
        padding: 36rpx;
        background-color: #ffffff;
        margin-bottom: 24rpx;

        .priceBox {
            display: flex;
            align-items: flex-end;
            margin-top: 32px;
        }

        .price {
            color: var(--Brand-Green-Primary, #3C9CFF);
            font-family: 'PingFang HK';
            font-size: 40rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 40px */
            margin-right: 24rpx;
        }

        .oldPrice {
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang HK';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx;

            .number {
                text-decoration: line-through;
            }
        }

        .symbol {
            margin-right: 10rpx;
        }

        .name {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 32rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 32px */
            margin-bottom: 14px;
        }
    }

    .action {
        padding: 0 36rpx;
        background-color: #ffffff;
        margin-bottom: 24rpx;

        .item {
            min-height: 90rpx;
            padding-right: 60rpx;
            border-bottom: 1rpx solid #eaeaea;
            font-size: 26rpx;
            color: #333;
            position: relative;
            display: flex;
            align-items: center;

            &:last-child {
                border-bottom: 0 none;
            }
        }

        .label {
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
            margin-right: 32rpx;
        }

        .text {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
            text {
                color: var(--Brand-Green-Primary-65, rgba(35, 84, 54, 0.65));
                font-family: 'PingFang SC';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 100%; /* 24px */
                margin-right: 24rpx;
            }
        }
    }
}

/* 商品详情 */
.detail {
    padding: 0 36rpx;

    .content {
        .image {
            width: 100%;
        }
    }

    .properties {
        padding: 0 20rpx 0 0 ;
        margin-bottom: 30rpx;

        .item {
            display: flex;
            line-height: 2;
            padding: 20rpx 0;
            font-size: 26rpx;
            color: #333;
            border-bottom: 1rpx dashed #ccc;
        }

        .label {
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
            margin-right: 32rpx;
        }

        .value {
            flex: 1;
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
        }
    }

    .contentBox {
        width: 100%;
        padding-bottom: 36rpx;
    }
}

/* 同类推荐 */
.similar {
    padding: 0 36rpx;
    .content {
        padding: 0 20rpx 20rpx;
        background-color: #f4f4f4;
        display: flex;
        flex-wrap: wrap;

        .goods {
            width: 340rpx;
            padding: 24rpx 20rpx 20rpx;
            margin: 20rpx 7rpx;
            border-radius: 10rpx;
            background-color: #fff;
        }

        .image {
            width: 300rpx;
            height: 260rpx;
        }

        .name {
            height: 80rpx;
            margin: 10rpx 0;
            font-size: 26rpx;
            color: #262626;
        }

        .price {
            line-height: 1;
            font-size: 20rpx;
            color: #cf4444;
        }

        .number {
            font-size: 26rpx;
            margin-left: 2rpx;
        }
    }

    navigator {
        &:nth-child(even) {
            margin-right: 0;
        }
    }
}

/* 底部工具栏 */
.toolbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: #fff;
    height: 128rpx;
    padding: 20rpx 36rpx;
    border-top: 1rpx solid #eaeaea;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: content-box;

    .buttons {
        width: 100%;
        display: flex;

        .addCart {
            display: flex;
            padding: 17rpx 50rpx;
            justify-content: center;
            align-items: center;
            gap: 8rpx;
            width: 327rpx;
            height: 88rpx;
            align-self: stretch;
            border-radius: 16rpx;
            border: 2rpx solid var(--Brand-Green-Primary, #3C9CFF);
            color: var(--Brand-Green-Primary, #3C9CFF);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 24px */
            margin-right: 24rpx;
            cursor: pointer;
        }

        .payment {
            display: flex;
            padding: 17rpx 50rpx;
            justify-content: center;
            align-items: center;
            gap: 8rpx;
            width: 327rpx;
            height: 88rpx;
            align-self: stretch;
            border-radius: 16rpx;
            border: var(--stroke-weight-1, 1px) solid var(--Brand-Green-Primary, #3C9CFF);
            background: var(--Brand-Green-Primary, #3C9CFF);
            color: var(--Normal-White, #fff);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 24px */
            cursor: pointer;
        }
    }

    .icons {
        padding-right: 20rpx;
        display: flex;
        align-items: center;
        flex: 1;
        // 兼容 H5 端和 App 端的导航链接样式
        .navigator-wrap,
        .icons-button {
            flex: 1;
            text-align: center;
            line-height: 1.4;
            padding: 0;
            margin: 0;
            border-radius: 0;
            font-size: 20rpx;
            color: #333;
            background-color: #fff;

            &::after {
                border: none;
            }
        }

        text {
            display: block;
            font-size: 34rpx;
        }
    }
}
</style>
