import { http } from '@/utils/http'
const shopURL = 'https://shop.evl.usr.86.ltd/srv'
//获取分类列表
export const getHomeCategoryAPI = (params: any): any => {
  return http({
    method: 'GET',
    url: shopURL+'/v1/category',
    data: params,
  })
}
// 获取全部分类
export const getCategoryAllAPI = (): any => {
  return http({
    url: shopURL+'/v1/category-all',
  })
}
// 获取分类详情
export const getCategoryFilterAPI = (id: any): any => {
  return http({
    url: shopURL+`/v1/category/${id}`,
  })
}
