/* 轮播图 */
.carousel {
  width: 686rpx;
  margin: 32rpx;
  height: 264rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  background-color: #efefef;
  .indicator {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 20rpx;
    display: flex;
    justify-content: center;
    .dot {
      width: 30rpx;
      height: 6rpx;
      margin: 0 8rpx;
      border-radius: 6rpx;
      background-color: rgba(255, 255, 255, 0.4);
    }
    .active {
      background-color: #fff;
    }
  }
  .navigator,
  .image {
    width: 686rpx;
    height: 264rpx;
    border-radius: 16rpx;
    object-fit: cover;
  }
}
