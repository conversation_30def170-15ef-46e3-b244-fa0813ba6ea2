<script lang="ts" setup>
import {ref} from 'vue'
import {getCarInfo, getDeviceEnergy, getMyDevice, getRideRecord, getRideRecordToday} from "@/services/device";
import {onHide, onShow, onUnload} from "@dcloudio/uni-app";
import CarEmpty from "@/pages/car/carEmpty.vue";
import carRun from "./carRun.vue"
import carLocation from "./carLocation.vue"
import {useCarStore} from '@/stores'
import {gps2AMap} from "@/utils/location";
import moment from "moment/moment";

const carStore = useCarStore()
carStore.clearCarInfo()
const mainID = ref('')
const latitude: any = ref('39.909')
const longitude: any = ref('116.39742')
const circles: any = ref([
  {
    latitude: latitude.value,
    longitude: longitude.value,
  },
  {
    latitude: latitude.value,
    longitude: longitude.value,
  }
])
const carInfo: any = ref({})
const mainInfo = ref({})
const flag = ref(0)
const address = ref('暂无定位')
const deviceList = ref([])
const leftFlag = ref(false)
const timeText = ref('')
const timer: any = ref(null)
const consumeList = ref([])
const energyList = ref([])
// 线路列表
const toLine = async () => {
  const id = await getCarId()
  uni.navigateTo({
    url: '/pages/car/myLine?id=' + id
  })
}
// 消息通知
const toInfo = () => {
  uni.navigateTo({
    url: '/pages/index/information?sn='+carInfo.value.sn+'&carName='+carInfo.value?.carModel?.brand+''+carInfo.value?.carModel?.model+'&carAddress='+address.value+'&carID='+mainID.value
  })
}
// 显示车型切换
const showLeft = () => {
  leftFlag.value = true
}
// 车机切换车型
const changeDevice = (id: any) => {
  uni.setStorageSync('carId', id)
  if (id == '') {
    flag.value = 0
  }
  mainID.value = id
  getResetData()
  getTodayData()
}
// 定位器切换车型
const toCarDetail = (id: any) => {
  uni.setStorageSync('carId', id)
  if (id == '') {
    flag.value = 0
  }
  mainID.value = id
  address.value = ''
  consumeList.value = []
  energyList.value = []
  mainInfo.value = {}
  timeText.value = ''
  getResetData()
  getTodayData()
  leftFlag.value = false
}
// 车辆详情
const toSetting = async () => {
  const id = await getCarId()
  uni.navigateTo({
    url: '/pages/car/carDetail?id=' + id
  })
}
// 获取车辆ID
const getCarId = async () => {
  let id = uni.getStorageSync('carId') || ''
  if (id == '') {
    const deviceRes = await getMyDevice()
    if (deviceRes.msg == 'success') {
      let list = deviceRes.result.filter((item: any) => {
        return item.userDefault == true
      })
      if (list.length > 0) {
      } else {
        list = deviceRes.result
      }
      if(list.length > 0){
        id = list[0].id
      }
    }
  }
  return id
}
// 初始化数据
const getResetData = async (type = 0) => {
  const id = await getCarId()
  if (id == '') {
    // uni.navigateTo({
    //     url: '/pages/index/addDevice'
    // })
  } else {
    let res = await getCarInfo(id)
    if (res.msg == 'success') {
      carInfo.value = Object.assign(carInfo.value, res.result)
      let lat: any = ''
      let lng: any = ''
      if (res.result.lastPoint) {
        let result = gps2AMap(res.result.lastPoint.lng, res.result.lastPoint.lat)
        lat = result[1]
        lng = result[0]
        latitude.value = lat
        longitude.value = lng
        circles.value[0].latitude = lat
        circles.value[0].longitude = lng
        circles.value[1].latitude = lat
        circles.value[1].longitude = lng
        timeText.value = getText(res.result.lastPoint.ts * 1000)
      } else {
        timeText.value = '--'
      }
    }
    //#ifndef H5
    // 获取车辆经纬度地址
    await getAddress(latitude.value, longitude.value);
    //#endif
    if (res.result.type == 0) {
      flag.value = 1
      if (type == 0) {
        getTodayData()
      }
    } else {
      flag.value = 2
      if (type == 0) {
        getTodayData()
        getRideEnergy(id)
      }
    }
  }
}
// 获取车辆更新时间
const getText = (time: any) => {
  // 根据时间戳计算距离当前的时间差，显示分钟前，小时前，日期
  const now = new Date().getTime();
  const diff = now - time;
  if (diff < 60000) {
    // 小于1分钟，显示“刚刚”
    return '刚刚';
  } else if (diff < 3600000) {
    // 小于1小时，显示分钟数
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前';
  } else {
    // 大于24小时 显示年月日时分秒
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
  }
}
// 经纬度转地址
const getAddress = async (lat: number, lng: number) => {
  const apiKey = 'a3d415243ae2185eb0f16452497e3867'; // 替换为你的高德地图API Key
  // address.value = '地址获取中';
  const formatted_address = uni.getStorageSync(lat + '-' + lng)
  if (formatted_address) {
    address.value = formatted_address
    return
  }
  uni.request({
    url: `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${lng},${lat}&key=${apiKey}&radius=1000&extensions=all `,
    methods: "GET",//仅为示例，并非真实接口地址。
    success: (res: any) => {
      uni.setStorageSync(lat + '-' + lng, res.data.regeocode.formatted_address)
      address.value = res.data.regeocode.formatted_address;
    }
  });
};
// 获取今日汇总
const getTodayData = async () => {
  const id = await getCarId()
  let resToday = await getRideRecordToday(id)
  if (resToday.msg == 'success') {
    mainInfo.value = resToday.result
  }
}
// 获取我的车辆列表
const getMyCar = async () => {
  const deviceRes = await getMyDevice()
  if (deviceRes.msg == 'success') {
    deviceList.value = deviceRes.result
  }
}
// 格式化秒为时分秒
const formatSecondsToHHMMSS = (seconds: any) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(secs).padStart(2, '0');
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
// 电子围栏
const toFence = async () => {
  const id = await getCarId()
  uni.navigateTo({
    url: '/pages/car/Fence?id=' + id
  })
}
const getRideEnergy = async (id: any) => {
  const filter = {
    deviceId: id,
    page: 1,
    size: 10
  }
  let consume = await getRideRecord(filter)
  if (consume.msg == 'success') {
    consumeList.value = consume.result.rows.reverse()
  }
  let energy = await getDeviceEnergy(id)
  if (energy.msg == 'success') {
    //倒序
    energyList.value = energy.result.reverse()
  }
}

onShow(async () => {
  const id = await getCarId()
  if (id == '') {
    flag.value = 0
  }
  mainID.value = id
  await getMyCar()

  await getResetData()
  timer.value = setInterval(() => {
    getResetData(1)
  }, 10000)
})
onUnload(() => {
  clearInterval(timer.value)
  timer.value = null
})
onHide(() => {
  clearInterval(timer.value)
  timer.value = null
})
const numberFormat = (num: number) => {
  // 判断小数位多余三位
  if (num.toString().split('.')[1] && num.toString().split('.')[1].length > 2) {
    return num.toFixed(2)
  } else {
    return num
  }
}
</script>

<template>
	<car-empty v-if="flag==0"></car-empty>
	<!--  是不是车机版 都会生成新的 在定位版 新的不加载 并不会销毁旧的-->
	<car-run v-else-if="flag==2" :key="timeText" :address="address" :car-info="carInfo" :consumeList="consumeList"
		:device-list="deviceList" :energyList="energyList" :mainID="mainID" :mainInfo="mainInfo" :timeText="timeText"
		@change="changeDevice"  @update="getResetData"></car-run>
		<carLocation v-else :key="timeText" :address="address" :car-info="carInfo" :consumeList="consumeList"
		:device-list="deviceList" :energyList="energyList" :mainID="mainID" :mainInfo="mainInfo" :timeText="timeText"
		@change="changeDevice"></carLocation>
</template>

<style lang="scss" scoped>
	.textLeft {
		text-align: left;
	}

	.textCenter {
		text-align: center;
	}

	.textRight {
		text-align: right;
	}


	.detailBox {
		padding: 32rpx 32rpx 0;

		.mainDesc {
			color: #000;
			/* Regular/Footnote */
			font-family: var(--Font-Family, "PingFang SC");
			font-size: var(--Font-Size-Footnote, 26rpx);
			font-style: normal;
			font-weight: 400;
			line-height: var(--Line-Height-Footnote, 36rpx);
			/* 138.462% */
			letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
			background: #FFFFFF;
			padding: 0 32rpx 32rpx;
		}

		.detailTop {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #FFFFFF;
			padding: 32rpx;

			.label {
				color: var(--Text-content, #53565C);
				font-family: var(--Font-Family, "PingFang SC");
				font-size: var(--Font-Size-Footnote, 26rpx);
				font-style: normal;
				font-weight: 400;
				line-height: var(--Line-Height-Footnote, 36rpx);
				/* 138.462% */
				letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
			}

			.content {
				flex: 1;
				margin-right: 10rpx;
				color: var(--Text-tips, #909399);
				text-align: right;
				font-family: var(--Font-Family, "PingFang SC");
				font-size: var(--Font-Size-Caption1, 24rpx);
				font-style: normal;
				font-weight: 400;
				line-height: var(--Line-Height-Caption1, 32rpx);
				/* 133.333% */
				letter-spacing: var(--Letter-Spacing-Caption1, 0px);
			}
		}

		.detailMain {
			padding: 0 32rpx 32rpx;
			display: flex;
			background: #FFFFFF;

			.item {
				flex: 1;

				.itemTitle {
					color: #000;
					font-feature-settings: 'case' on;
					font-family: "PingFang SC";
					font-size: 34rpx;
					font-style: normal;
					font-weight: 500;
					line-height: 44rpx;
					letter-spacing: -0.408px;

				}
				
				.blue {
				  color: var(--Primary-text, #3C9CFF);
				}
				
				.red {
				  color: #FF7070;
				}
				
				.green {
				  color: #5AC725;
				}
				
				.hui {
				  color: #666666;
				}

				.itemDesc {
					color: var(--Text-content, #53565C);
					font-family: var(--Font-Family, "PingFang SC");
					font-size: var(--Font-Size-Footnote, 26rpx);
					font-style: normal;
					font-weight: 400;
					line-height: var(--Line-Height-Footnote, 36rpx);
					/* 138.462% */
					letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
					margin-top: 32rpx;
				}
			}
		}
	}

	.centerBox {
		padding: 32rpx 32rpx 0;
		display: flex;
		justify-content: space-between;

		.item {
			width: 328rpx;
			padding: 32rpx;
			border-radius: 16rpx;
			border-bottom: 0.5px solid var(--Border-4, #E6E6E8);
			background: var(--Background-white, #FFF);

			.itemTitle {
				color: #000;
				font-feature-settings: 'case' on;
				font-family: "PingFang SC";
				font-size: 34rpx;
				font-style: normal;
				font-weight: 500;
				line-height: 44rpx;
				letter-spacing: -0.408px;

			}

			.itemDesc {
				color: var(--Text-content, #53565C);
				font-family: var(--Font-Family, "PingFang SC");
				font-size: var(--Font-Size-Footnote, 26rpx);
				font-style: normal;
				font-weight: 400;
				line-height: var(--Line-Height-Footnote, 36rpx);
				/* 138.462% */
				letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
				margin-bottom: 32rpx;
			}
		}
	}

	.mainTop {
		flex-shrink: 0;
		background: url('../../static/imgs/car/bg.png') no-repeat;
		background-size: 100% 100%;
	}

	.topBox {
		display: flex;
		align-items: center;
		padding: 32rpx;

		.left {
			flex: 2;
			display: flex;
			align-items: center;
			position: relative;
			color: #333;
			font-family: "PingFang HK";
			font-size: 36rpx;
			font-style: normal;
			font-weight: 600;
			line-height: normal;

			.icon {
				width: 26rpx;
				height: 13rpx;
				flex-shrink: 0;
				margin-left: 20rpx;
			}

			.rotate {
				width: 26rpx;
				height: 13rpx;
				flex-shrink: 0;
				margin-left: 20rpx;
				transform: rotate(180deg);
			}

			.maskSpace {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 10;
				background: transparent;
			}

			.leftMask {
				position: absolute;
				top: 60rpx;
				width: 164px;
				background: #FFFFFF;
				border-radius: 16rpx;
				padding: 16rpx 32rpx;
				z-index: 12;

				:after {
					content: '';
					position: absolute;
					left: 20rpx;
					width: 0;
					height: 0;
					top: -5rpx;
					border-left: 10rpx solid transparent;
					border-right: 10rpx solid transparent;
					border-bottom: 10rpx solid #fff;
					/* 三角形的颜色 */
					transform: translateY(-50%);
				}

				.leftText {
					padding: 16rpx 0rpx;
					color: #4F4F4F;
					text-align: center;
					font-family: "PingFang HK";
					font-size: 24rpx;
					font-style: normal;
					font-weight: 500;
					line-height: 24rpx;
					/* 100% */
					width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.activeText {
					color: #4B85FD;
				}
			}
		}

		.right {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			flex: 1;

			.icon {
				width: 36rpx;
				height: 36rpx;
				flex-shrink: 0;
				margin-left: 24rpx;
			}
		}

	}

	.carBox {
		padding: 32rpx;
		height: 420rpx;
		position: relative;

		.topIcon {
			display: flex;
			justify-content: flex-start;

			.icon {
				width: 36rpx;
				height: 36rpx;
				flex-shrink: 0;
				margin-right: 24rpx;
			}
		}

		.carImg {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 564rpx;
			height: 446rpx;
			flex-shrink: 0;
			transform: translate(-50%, -50%);
		}
	}

	.mapBox {
		margin: 32rpx 32rpx 0;
		width: 686rpx;
		height: 166rpx;
		flex-shrink: 0;
		border-radius: 16rpx;
		background: url("../../static/imgs/car/mapbg.png") no-repeat;
		background-size: 100% 100%;
		overflow: hidden;
		display: flex;
		justify-content: space-between;

		.mapItem {
			width: 252rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
			margin-right: 8rpx;
		}

		.mapInfo {
			color: var(--font-u-main-color, #000);
			text-align: center;
			font-feature-settings: 'case' on;
			font-family: "PingFang SC";
			font-size: 15px;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
			/* 133.333% */
			letter-spacing: -0.24px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding: 32rpx;
			width: 430rpx;

			.title {
				color: var(--font-u-main-color, #000);
				text-align: left;
				font-feature-settings: 'case' on;
				font-family: "PingFang SC";
				font-size: 26rpx;
				font-style: normal;
				font-weight: 500;
				line-height: 40rpx;
				/* 133.333% */
				letter-spacing: -0.24px;
				margin-bottom: 26rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				height: 40rpx;
			}

			.desc {
				display: flex;
				align-items: center;

				.icon {
					width: 36rpx;
					height: 36rpx;
					flex-shrink: 0;
					margin-right: 10rpx;
				}

				color: var(--font-u-content-color, #606266);
				font-feature-settings: 'case' on;
				font-family: "PingFang SC";
				font-size: 22rpx;
				font-style: normal;
				font-weight: 500;
				line-height: 26rpx;
				/* 118.182% */
				letter-spacing: 0.066px;
			}
		}
	}
</style>
