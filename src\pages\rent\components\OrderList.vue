<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getOrderList} from "@/services/rent";

onMounted(() => {
    getMemberOrderData()
})
const props = defineProps({
    orderState: {
        type: Number,
        default: 0
    }
})
const params = ref({
    page: 1,
    pageSize: 100,
    status: props.orderState
})
const list = ref([])
const getMemberOrderData = async () => {
    list.value = []
    let res = await getOrderList(params.value)
    console.log(res.result.rows)
    list.value = res.result.rows
}
// tab列表
const tabTypes = [
    {name: 0, label: '待支付', disabled: false},
    {name: 1, label: '待核销', disabled: false},
    {name: 2, label: '使用中', disabled: false},
    {name: 3, label: '已完成', disabled: false},
    {name: 10, label: '已取消', disabled: false},
    {name: 11, label: '已关闭', disabled: false},
]


const formatPayState = (order: any) => {
    let payState = order.status
    const stateMap: any = {
        0: '待支付',
        1: '待核销',
        2: '使用中',
        3: '已完成',
        10: '已取消',
        11: '已关闭',
    }
    return stateMap[payState]
}
const toDetail = (id) => {
    uni.navigateTo({
        url: '/pages/rent/orderDetail?id='+id
    })
}
</script>

<template>
    <view class="mainBox">
        <mescroll-empty v-if="list.length==0" ></mescroll-empty>
        <view v-for="item in list" class="itemBox" @click="toDetail(item.id)">
            <view class="title">{{ item.store.name }}
                <view class="status" v-if="item.status ==0">{{ formatPayState(item) }}</view>
                <view class="status"  v-if="item.status ==1">{{ formatPayState(item) }}</view>
                <view class="status green"  v-if="item.status ==2">{{ formatPayState(item) }}</view>
                <view class="status blue"  v-if="item.status ==3">{{ formatPayState(item) }}</view>
                <view class="status"  v-if="item.status ==10">{{ formatPayState(item) }}</view>
                <view class="status"  v-if="item.status ==11">{{ formatPayState(item) }}</view>
            </view>
            <view class="name">{{ item.menu.name }}</view>
            <view class="desc">{{ item.menu.description }}</view>
            <view class="priceMain">
                <view class="price">
                    <view class="priceText">租金</view>
                    <view class="priceNum">
                        <view class="unit">￥</view>
                        {{ item.menu.price / 100 }}
                    </view>
                </view>
                <view class="price">
                    <view class="priceText">天数</view>
                    <view class="priceNum">{{ item.menu.days }}
                        <view class="unit">天</view>
                    </view>
                </view>
            </view>
        </view>
    </view>

</template>

<style lang="scss" scoped>
.mainBox {
    padding-top: 32rpx;
    background: #f5f5f5;
}

.itemBox {
    width: 686rpx;
    height: 270rpx;
    margin: 0 32rpx 32rpx;
    padding: 24rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    .title {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx; /* 129.412% */
        display: flex;
        justify-content: space-between;
        margin-bottom: 32rpx;

        .status {
            width: 100rpx;
            height: 40rpx;
            flex-shrink: 0;
            border-radius: 6rpx;
            background: rgba(255, 0, 0, 0.25);
            color: #F00;
            text-align: right;
            /* Regular/Footnote */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .blue{
            background: rgba(60, 156, 255, 0.25);
            color: var(--Primary-text, #3C9CFF);
        }
        .green{
            background: rgba(1, 167, 59, 0.25);
            color: #01A73B;
        }
    }

    .name {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 40rpx; /* 133.333% */
    }

    .desc {
        color: var(--Text-content, #53565C);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Footnote, 26rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
        margin-bottom: 30rpx;
    }
}

.priceMain {
    display: flex;

    .price {
        flex: 1;
        display: flex;
        align-items: flex-end;
    }

    .priceText {
        color: #C3C3C3;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 24rpx; /* 100% */
    }

    .priceNum {
        display: flex;
        align-items: flex-end;
        color: #F00;
        font-family: "PingFang SC";
        font-size: 40rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 35rpx; /* 100% */
        .unit {
            color: #F00;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx; /* 100% */
        }
    }
}
</style>
