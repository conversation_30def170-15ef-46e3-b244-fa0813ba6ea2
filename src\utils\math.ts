import Big from 'big.js';
// 加法
export const calcAdd = (a: number, b: number) => {
    // 确保 a 和 b 都是有效的数字
    if (typeof a === 'number' && !isNaN(a) && typeof b === 'number' && !isNaN(b)) {
        try {
            return new Big(a).plus(b).toNumber();
        } catch (error) {
            // 处理 Big 库可能抛出的异常
            console.error('Error in Big library:', error);
            return 0;
        }
    } else if (typeof a === 'number' && !isNaN(a)) {
        return a;
    } else if (typeof b === 'number' && !isNaN(b)) {
        return b;
    } else {
        return 0;
    }
};
// 减法
export const calcSub = (a: number, b: number) => {
    // 确保 a 和 b 都是有效的数字
    if (typeof a === 'number' && !isNaN(a) && typeof b === 'number' && !isNaN(b)) {
        try {
            return new Big(a).minus(b).toNumber();
        } catch (error) {
            // 处理 Big 库可能抛出的异常
            console.error('Error in Big library:', error);
            return 0;
        }
    } else if (typeof a === 'number' && !isNaN(a)) {
        return a;
    } else if (typeof b === 'number' && !isNaN(b)) {
        return b;
    } else {
        return 0;
    }
}
// 乘法
export const calcMul = (a: number, b: number) => {
    // 确保 a 和 b 都是有效的数字
    if (typeof a === 'number' && !isNaN(a) && typeof b === 'number' && !isNaN(b)) {
        try {
            return new Big(a).times(b).toNumber();
        } catch (error) {
            // 处理 Big 库可能抛出的异常
            console.error('Error in Big library:', error);
            return 0;
        }
    } else if (typeof a === 'number' && !isNaN(a)) {
        return a;
    } else if (typeof b === 'number' && !isNaN(b)) {
        return b;
    } else {
        return 0;
    }
};
//除法
export const calcDiv = (a: number, b: number) => {
    // 确保 a 和 b 都是有效的数字
    if (typeof a === 'number' && !isNaN(a) && typeof b === 'number' && !isNaN(b)) {
        try {
            return new Big(a).div(b).toNumber();
        } catch (error) {
            // 处理 Big 库可能抛出的异常
            console.error('Error in Big library:', error);
            return 0;
        }
    } else if (typeof a === 'number' && !isNaN(a)) {
        return a;
    } else if (typeof b === 'number' && !isNaN(b)) {
        return b;
    } else {
        return 0;
    }
};
