import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useCarStore = defineStore(
    'carInfo',
    () => {
        // 车型信息
        const carInfo = ref<any>({brand:'',model:'',img:'https://evl.oss-cn-beijing.aliyuncs.com/model/08-PRO-1.png'})
        // 修改车型信息
        const setCarInfo = (val: any) => {
            carInfo.value = val
        }

        // 清理车型信息
        const clearCarInfo = () => {
            carInfo.value = {}
        }

        // 记得 return
        return { carInfo, setCarInfo, clearCarInfo }
    },
    {
        // 配置持久化
        persist: {
            // 调整为兼容多端的API
            storage: {
                setItem(key, value) {
                    uni.setStorageSync(key, value)
                },
                getItem(key) {
                    return uni.getStorageSync(key)
                },
            },
        },
    },
)