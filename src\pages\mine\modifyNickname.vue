<template>
    <u-navbar :is-back="true" title="修改昵称">
        <template v-slot:right>
            <u-icon @click="saveName" name="email" size="32" style="margin-right: 32rpx"></u-icon>
        </template>
    </u-navbar>
    <view class="formBox">
        <u-input v-model="nickname" :border="false" maxlength="30" placeholder="请输入昵称" type="text"></u-input>
        <image src="../../static/imgs/mine/clear.png" class="deleteIcon" v-if="nickname.length>0" @tap="nickname = ''"></image>
    </view>
    <view class="length">{{ 30 - nickname.length }}</view>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {modifyUser} from '@/services/user'
import {useMemberStore} from "@/stores";
const memberStore = useMemberStore()
const nickname = ref('')
const props = defineProps({
    nickname:{
        type:String,
        default:''
    }
})

// 页面加载
onMounted(() => {
    nickname.value = props.nickname
})
const saveName = async () => {
    console.log(nickname.value)
    let res = await modifyUser({nickname: nickname.value})
    if(res.msg=='success'){
        const obj = Object.assign(memberStore.userInfo,{nickname:nickname})
        memberStore.setUserInfo(obj)
        uni.navigateBack({delta:1})
    }
}
</script>

<style lang="scss" scoped>
.rightIcon {
    width: 48rpx;
    height: 48rpx;
    margin: 0 32rpx;
}

.formBox {
    display: flex;
    margin: 32rpx;
    height: 112rpx;
    padding: 0px 24rpx;
    gap: 4px;
    flex-shrink: 0;
    border-radius: 16rpx;
    border: 1px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white, #FFF);
    justify-content: space-between;
    align-items: center;
    .deleteIcon{
        width: 32rpx;
        height: 32rpx;
    }
}

.length {
    color: var(--Text-tips, #909399);
    /* Regular/Caption1 */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption1, 24rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
    letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    text-align: right;
    padding: 0 40rpx;
    margin-top: 16rpx;
}
</style>
