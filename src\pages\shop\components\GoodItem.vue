<template>
  <view class="goodItem">
    <view class="image">
      <image :src="orderGood.picture" alt="" class="imgItem" />
    </view>
    <view class="info">
      <view class="infoBox">
        <view class="name ellipsis-2">
          {{ orderGood.name }}
        </view>
        <view class="count">
          <view v-for="info in orderGood.specs" :key="info.name" class="spec">
            {{ info.name }}: {{ info.value }}
          </view>
        </view>
      </view>
      <view class="priceBox">
        <view style="display: flex; justify-content: flex-end; width: 100%">
            <text class="price">单价：￥{{ orderGood.curPrice / 100 }}</text>
        </view>
        <view class="count">x{{ orderGood.quantity }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  skuId: {
    type: String,
    required: true,
  },
  orderGood: {
    type: Object,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.goodItem {
  display: flex;
  background: #ffffff;
  padding: 24rpx 0 24rpx;
}

.image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 6rpx;

  image {
    width: 150rpx;
    height: 150rpx;
  }
}

.info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  margin-left: 24rpx;
  .infoBox{
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .name {
      color: var(--Normal-Black-333, #333);
      font-family: "PingFang HK";
      font-size: 21rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 150%; /* 31.5px */
    }

    .ellipsis-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 控制显示的行数 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .spec {
      color: var(--Normal-Black-666, #666);
      font-family: "PingFang HK";
      font-size: 21rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 100%; /* 21px */
      margin-bottom: 5rpx;
    }
  }
  .priceBox{
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    .price {
      color: var(--Normal-Gray-999, #999);
      font-family: "PingFang HK";
      font-size: 21rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 150%; /* 31.5px */

      text-align: right;
      margin-bottom: 10rpx;
    }

    .count {
      color: var(--Normal-Gray-999, #999);
      font-family: "PingFang HK";
      font-size: 21rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 150%; /* 31.5px */
      text-align: right;
    }
  }


}

</style>
