<script setup lang="ts">
// 添加设备
const addDevice = () => {
    uni.navigateTo({
        url: '/pages/index/addDevice'
    })
}
// 扫一扫添加设备
const scanQrCode = () => {
    uni.scanCode({
        success: (res:any) => {
            if(res.errMsg== "scanCode:ok"){
                uni.navigateTo({
                    url:'/pages/addDevice/snResult?type=scan&sn='+res.result
                })
            }
        }
    })
}
</script>

<template>
    <view class="emptyBox" >
        <L-top-height></L-top-height>
        <view class="emptyTopBox">
            <view class="left">
              <image class="icon" src="../../static/imgs/car/icon.svg"></image>
            </view>
            <view class="right">
                <image class="icon" src="../../static/imgs/car/info.svg"></image>
                <image class="icon" src="../../static/imgs/car/setting.svg" @tap="toSetting"></image>
            </view>
        </view>
        <view class="flexBox">
            <view class="titleBox">Cola10</view>
            <view class="imgBox" >
                <image src="../../static/imgs/car/carHappy.png"></image>
            </view>
            <view class="infoBox">
                <view class="info">
                    <view class="infoTitle">Cola 10</view>
                    <view class="infoDesc">美学轻骑 乐享智趣</view>
                </view>
            </view>
        </view>
        <view  class="topBox">
            <view class="topItem add" @click="addDevice">
                <image alt="" class="svg" src="../../static/imgs/index/add.svg"/>
                <text>添加设备</text>
            </view>
            <view class="topItem" @click="scanQrCode">
                <image alt="" class="svg" src="../../static/imgs/index/scan.svg"/>
                <text>扫一扫</text>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.emptyBox{
    width: 100%;
    min-height: 95vh;
    background: url("../../static/imgs/car/bgMain.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .topBox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;

        .topItem {
            width: 327rpx;
            height: 148rpx;
            flex-shrink: 0;
            align-self: stretch;
            border-radius: 24rpx;
            box-shadow: 0px 8rpx 8rpx 0px rgba(0, 0, 0, 0.02);
            display: flex;
            align-items: center;
            justify-content: center;
            background: #FFFFFF;
            color: #000;
            text-align: center;
            font-feature-settings: 'case' on;
            /* Medium/Body */
            font-family: "PingFang SC";
            font-size: 34rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 44rpx; /* 129.412% */
            letter-spacing: -0.408px;
            border: 2rpx solid var(--Border-4, #E6E6E8);

            .svg {
                width: 48rpx;
                height: 48rpx;
                margin-right: 24rpx;
            }
        }

        .add {
            width: 331rpx;
            height: 152rpx;
            border: 0px solid transparent;
            background: url("../../static/imgs/index/bg.png") no-repeat;
            background-size: 100% 100%;

        }
    }
    .flexBox{
        flex: 1;
        width: 100%;
        position: relative;
        .titleBox{
            width: 100%;
            position: absolute;
            color: #525D71;
            text-align: center;
            font-feature-settings: 'case' on;
            font-family: "Zen Dots";
            font-size: 112rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 56px */
            letter-spacing: -0.408px;
            top: 202rpx;
        }
        .imgBox{
            height: 422rpx;
            margin: 240rpx 74rpx 0;
        }
        .infoBox{
            width: 100%;
            padding: 0 72rpx;
            .info{
                width: 100%;
                height: 400rpx;
                background: url("../../static/imgs/car/bgFour.png") no-repeat;
                background-size: 100% 100%;
                .infoTitle{
                    color: var(--Text-white, #FFF);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 56rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 68rpx; /* 121.429% */
                    letter-spacing: 0.364px;
                    margin-bottom: 16rpx;
                    padding-top: 60rpx;
                }
                .infoDesc{
                    color: var(--Text-white, #FFF);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 34rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 44rpx; /* 129.412% */
                    letter-spacing: -0.408px;
                }
            }
        }
    }
}
.emptyTopBox {
    display: flex;
    align-items: center;
    padding: 32rpx;

    .left {
        flex: 2;
        display: flex;
        align-items: center;
        position: relative;

        .icon {
            width: 68rpx;
            height: 52rpx;
            flex-shrink: 0;
            position: absolute;
            top: -30rpx;
        }
    }

    .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;

        .icon {
            width: 36rpx;
            height: 36rpx;
            flex-shrink: 0;
            margin-left: 24rpx;
        }
    }

}
</style>