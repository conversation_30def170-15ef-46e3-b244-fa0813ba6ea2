// 封装倒计时逻辑函数
import { computed, onUnmounted, ref } from 'vue'
// 秒转日时分秒
export const useCountDown = () => {
  // 1. 响应式的数据
  let timer:any = null
  const time = ref(0)
  // 格式化时间 为 xx日xx时xx分xx秒
  const formatDate = (seconds:any) => {
    if(seconds<=0){
      return '已到期'
    }
    // 计算天数
    const days = Math.floor(seconds / (24 * 60 * 60));
    // 剩余秒数
    let remainingSeconds = seconds % (24 * 60 * 60);
    // 计算小时数
    const hours = Math.floor(remainingSeconds / (60 * 60));
    // 再次计算剩余秒数
    remainingSeconds %= (60 * 60);
    // 计算分钟数
    const minutes = Math.floor(remainingSeconds / 60);
    // 最后计算剩余秒数
    const secs = remainingSeconds % 60;
    if(days==0&&hours==0&&minutes==0){
      return  `${secs}秒`
    }
    if(days==0&&hours==0){
      return  `${minutes}分${secs}秒`
    }
    if(days==0){
      return  `${hours}时${minutes}分${secs}秒`
    }
    return `${days}天${hours}时${minutes}分${secs}秒`
  }

  const formatTime = computed(() => formatDate(time.value))
  // 2. 开启倒计时的函数
  const start = (currentTime:any) => {
    // 开始倒计时的逻辑
    // 核心逻辑的编写：每隔1s就减一
    time.value = currentTime
    timer = setInterval(() => {
      time.value--
    }, 1000)
  }
  // 组件销毁时清除定时器
  onUnmounted(() => {
    timer && clearInterval(timer)
  })
  return {
    formatTime,
    start
  }
}
