<template>
    <view v-if="showCode==false">
        <view class="top">紧急联系人手机号</view>
        <view class="topPhone">
            请输入紧急联系人手机号并验证
        </view>
        <view class="formBox">
            <u-input v-model="phone" :border="false" maxlength="11" placeholder="请输入手机号" type="number"></u-input>
            <image src="../../static/imgs/mine/clear.png" class="deleteIcon" v-if="phone.length>0" @tap="phone = ''"></image>
        </view>
        <view class="btnBox">
            <u-button  type="primary" @click="showCode = true" :disabled="status" @tap="getCode">获取验证码</u-button>
        </view>
    </view>
    <view v-else>
        <view class="top">验证</view>
        <view class="topPhone">
            已向您尾号
            <text>{{ phoneNewLastFour }}</text>
            的手机发送验证码
        </view>
        <view class="codeBox">
            <u-message-input maxlength="6" v-model="code" focus></u-message-input>
        </view>
        <view class="topPhone">
            获取不到验证码？
            <text @click="getCodeAgain">重新获取</text>
        </view>
        <view class="btnBox">
            <u-button  type="primary" @tap="confirm">确认</u-button>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue'
import {getCodeAPI} from "@/services/login";
import {getDeviceTel} from "@/services/device";
const showCode = ref(false)
const phone = ref('')
const code = ref('')
const props = defineProps({
    id:{
        type:String,
        default:''
    }
})
// 获取页面初始列表
const confirm = async () => {
    let res = await getDeviceTel({
        deviceId:props.id,
        tel: phone.value,
        code: code.value
    })
    if (res.msg=='success'){
        uni.navigateBack()
    }
    console.log(phone.value,code.value,res)

}
const phoneNewLastFour = computed(() => {
    return phone.value.slice(-4)
})
const status = computed(() => {
    return !(phone.value)
})
const getCode =async () => {
        let res = await getCodeAPI({tel: phone.value})
        if (res.msg == 'success') {
            uni.showToast({icon: 'none', title: '验证码已发送到:' + phone.value})
            showCode.value = true
        }
}
</script>

<style lang="scss" scoped>
.top {
    color: var(--Text-main, #282A2E);
    /* Medium/Title1 */
    font-family: "PingFang SC";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 64rpx; /* 121.429% */
    letter-spacing: 0.364px;
    padding: 48rpx 32rpx 0;
}
.topPhone{
    padding: 14rpx 32rpx 0;
    color: var(--Text-main, #282A2E);
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption1, 24rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
    letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    text{
        color: var(--Primary-text, #3C9CFF);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption1, 24rpx);
        font-style: normal;
        font-weight: 600;
        line-height: var(--Line-Height-Caption1, 32rpx);
        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    }
}

.formBox {
    display: flex;
    margin: 32rpx;
    height: 112rpx;
    padding: 0px 24rpx;
    gap: 4px;
    flex-shrink: 0;
    border-radius: 16rpx;
    border: 1px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white, #FFF);
    justify-content: space-between;
    align-items: center;
    .deleteIcon{
        width: 32rpx;
        height: 32rpx;
    }
}
.codeBox{
    padding: 58rpx 32rpx 32rpx;
    :deep(.u-char-flex){
        justify-content: flex-start;
    }
}
.btnBox{
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
}
</style>
