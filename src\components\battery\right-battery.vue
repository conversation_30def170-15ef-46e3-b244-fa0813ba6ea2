<template>
  <view class="battery">
    <view :style="{width: battery+'%',background:bgColor}" class="active">
      <image v-show="battery<=80" class="batteryIcon" :src="battery<50?'../../static/imgs/car/icon-light-w.svg':'../../static/imgs/car/icon-light.svg'"></image>
    </view>
    <view :style="{color: battery <= 99 ? '#282A2E' : '#FFF'}" class="batteryNum">{{ battery }}
      <text :style="{color: battery <= 99 ? '#282A2E' : '#FFF'}">%</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {computed} from "vue";

const props = defineProps({
  battery: {
    type: Number,
    default: () => ({})
  }
})
const bgColor = computed(() => {
  if(props.battery<30){
    return '#FF7070'
  }else if (props.battery >=30 && props.battery <50){
    return '#E5A23C'
  }else{
    return '#34A853'
  }
})
</script>


<style scoped lang="scss">
.battery {
  position: relative;
  width: 100%;
  height: 80rpx;
  flex-shrink: 0;
  background: #EFEFEF;
  border-radius: 16rpx;
  overflow: hidden;

  .active {
    height: 80rpx;
    flex-shrink: 0;
    background: #34A853;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .batteryIcon {
      width: 16rpx;
      height: 28rpx;
      flex-shrink: 0;
        margin-right: 10rpx;
    }
  }
  .batteryNum {
    position: absolute;
    right:24rpx;
    bottom: 12rpx;
    color: #282A2E;
    font-family: "DIN Alternate";
    font-size: 56rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 100%; /* 28px */
    letter-spacing: 0.28px;
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;

    text {
      color: #282A2E;
      text-align: right;
      font-family: "DIN Alternate";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
  }
}
</style>
