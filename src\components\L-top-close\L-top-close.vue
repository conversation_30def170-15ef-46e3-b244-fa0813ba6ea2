<script lang="ts" setup>
const back = () => {
    uni.navigateBack({delta: 1})
}
</script>

<template>
    <view class="closeBar" @tap="back">
        <image alt="close" class="closeBox" src="./close.svg"></image>
    </view>
</template>

<style lang="scss" scoped>
.closeBar {
    display: flex;
    width: 750rpx;
    height: 88rpx;
    padding: 0px 48rpx;
    align-items: center;
    flex-shrink: 0;
    background: #f5f5f5;

    .closeBox {
        width: 32rpx;
        height: 32rpx;
    }
}
</style>