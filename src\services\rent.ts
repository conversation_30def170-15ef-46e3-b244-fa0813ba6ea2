import { http } from '@/utils/http'
const baseURL = 'https://shop.evl.usr.86.ltd/srv'
// 获取轮播图
export const getNearStoreAPI = (data:any): any => {
    return http({
        url: baseURL+'/rent/me/store',
        method: 'GET',
        data
    })
}
export const getNearStoreDetailAPI = (storeId:any): any => {
    return http({
        url:baseURL+`/rent/me/store/${storeId}`,
        method: 'GET'
    })
}
export const getNearStoreComboAPI = (storeId:any): any => {
    return http({
        url:baseURL+`/rent/me/storeMenu/${storeId}`,
        method: 'GET'
    })
}

export const createOrderAPI = (data:any): any => {
    return http({
        url:baseURL+`/rent/me/order`,
        method: 'POST',
        data
    })
}
export const buyOrderAPI =(storeId:any): any => {
    return http({
        url:baseURL+`/rent/me/order/pay/${storeId}`,
        method: 'PUT',
    })
}
export const cancelOrderAPI =(orderId:any): any => {
    return http({
        url:baseURL+`/rent/me/order/cancel/${orderId}`,
        method: 'PUT',
    })
}
export const receiveCarAPI =(orderId:any,data:any): any => {
    return http({
        url:baseURL+`/rent/me/order/receive/${orderId}`,
        method: 'PUT',
        data
    })
}
export const backCarAPI =(orderId:any,data:any): any => {
    return http({
        url:baseURL+`/rent/me/order/return/${orderId}`,
        method: 'PUT',
        data
    })
}
export const getOrderList =(data:any): any => {
    return http({
        url:baseURL+`/rent/me/orders`,
        method: 'GET',
        data
    })
}
export const getOrderDetail =(orderId:any): any => {
    return http({
        url:baseURL+`/rent/me/order/${orderId}`,
        method: 'GET',
    })
}


export const getRealInfoAPI = (): any => {
    return http({
        url:baseURL+`/rent/me/memberInfo`,
        method: 'GET'
    })
}

export const saveRealInfoAPI = (data:any): any => {
    return http({
        url:baseURL+`/rent/me/memberInfo`,
        method: 'POST',
        data
    })
}
export const savePutRealInfoAPI = (data:any): any => {
    return http({
        url:baseURL+`/rent/me/memberInfo`,
        method: 'PUT',
        data
    })
}