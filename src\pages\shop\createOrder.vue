<!--suppress TypeScriptCheckImport -->
<script setup lang="ts">
import { getGoodDetailAPI, getShippingDetailAPI, createOrderAPI } from '@/services/order'
import { deleteMemberCartAPI, getCartListAPI } from '@/services/cart'

import {onLoad, onShow} from '@dcloudio/uni-app'
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { getAddressListAPI } from '@/services/address'
import {useAddressStore, useOrderStore} from '@/stores'
const { t } = useI18n()
const orderStore = useOrderStore()
// 翻译头部
// 获取订单信息
const query: any = ref({})
onLoad((options) => {
    query.value = options
})
const goods: any = ref([])
const orderInfo: any = ref({})
onShow(async () => {
    await getAddressList()
    if (query.value.type == 'now') {
        // 直接购买逻辑
        const mainInfo: any = JSON.parse(JSON.stringify(orderStore.orderInfo))
        console.log(orderStore.value, 7777)
        const res: any = await getGoodDetailAPI({ id: mainInfo.skuInfo.goodsId })
        mainInfo.goodInfo = res.result
        // 预售商品不计算折扣
        if (mainInfo.skuInfo.isPreSale == false) {
            // 普通商品
            // 单价
            mainInfo.showPrice = mainInfo.skuInfo.price / 100
            // 总价
            // mainInfo.showTotalPrice = (mainInfo.skuInfo.price / 100) * mainInfo.countNum
            mainInfo.showTotalPrice = Math.round(mainInfo.skuInfo.price * mainInfo.countNum) / 100
            // 实付
            mainInfo.showpayPrice =
                Math.round(
                    (mainInfo.skuInfo.price / 100) * mainInfo.countNum * (100 - mainInfo.goodInfo.discount),
                ) / 100

            // 总金额
            mainInfo.showpayPriceNext =
                Math.round(
                    (mainInfo.skuInfo.price / 100) * mainInfo.countNum * (100 - mainInfo.goodInfo.discount),
                ) / 100
        } else {
            // 预售商品

            // 单价
            mainInfo.showPrice = mainInfo.skuInfo.prePrice / 100
            // 总价
            mainInfo.showTotalPrice = (mainInfo.skuInfo.prePrice / 100) * mainInfo.countNum
            // 实付
            mainInfo.showpayPrice = (mainInfo.skuInfo.prePrice / 100) * mainInfo.countNum
            // 总金额
            mainInfo.showpayPriceNext = (mainInfo.skuInfo.price / 100) * mainInfo.countNum
        }
        goods.value = [mainInfo]
        console.log(goods.value)
    } else if (query.value.type == 'cart') {
        // 购物车逻辑
        const res: any = await getCartListAPI()
        let list = res.result.rows
        let selCart =  JSON.parse(uni.getStorageSync('selCart'))
        // 筛选list跟sellcart  id一致 的加入新数组
        list = selCart.filter((item: any) => {
            return list.some((item2: any) => {
                return item.skuId == item2.skuId
            })
        })

        console.log(selCart,list,'test')
        for (const i in list) {
            const mainInfo: any = JSON.parse(JSON.stringify(list[i]))
            mainInfo.skuInfo = mainInfo.sku
            mainInfo.countNum = mainInfo.quantity
            const resGood: any = await getGoodDetailAPI({ id: mainInfo.skuInfo.goodsId })
            mainInfo.goodInfo = resGood.result
            const resShipping: any = await getShippingDetailAPI(mainInfo.skuInfo.shippingId)
            mainInfo.skuInfo.shipping = resShipping.result
            // 单价
            mainInfo.showPrice = mainInfo.skuInfo.price / 100
            // 总价
            mainInfo.showTotalPrice = (mainInfo.skuInfo.price / 100) * mainInfo.countNum
            // 实付
            mainInfo.showpayPrice =
                ((mainInfo.skuInfo.price / 100) * mainInfo.countNum * (100 - mainInfo.goodInfo.discount)) /
                100
            // 总金额
            mainInfo.showpayPriceNext =
                ((mainInfo.skuInfo.price / 100) * mainInfo.countNum * (100 - mainInfo.goodInfo.discount)) /
                100
            list[i] = mainInfo
            console.log(list)
        }
        goods.value = list
    }
    orderInfo.value = {
        count: goods.value.reduce((sum: any, item: any) => add(sum, item.countNum), 0),
        fee: Math.max(...goods.value.map((item: any) => item.skuInfo.shipping.amount)),
        showPayPrice: goods.value.reduce((sum: any, item: any) => add(sum, item.showpayPrice), 0),
        showPayPriceNext: goods.value.reduce(
            (sum: any, item: any) => add(sum, item.showpayPriceNext),
            0,
        ),
    }
    orderInfo.value.total = add(orderInfo.value.fee / 100, orderInfo.value.showPayPrice)
    console.log(orderInfo.value.fee / 100, orderInfo.value.showPayPriceNext)
    orderInfo.value.fullTotal = add(orderInfo.value.fee / 100, orderInfo.value.showPayPriceNext)
    console.log(orderInfo.value)
})
const add = (a, b) => {
    let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b))
    return Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b
}
const addressIList = ref([])
const selecteAddress = ref<any>()
const getAddressList = async () => {

    const addressStore = useAddressStore()
    if(addressStore.selectedAddress){
        selecteAddress.value = addressStore.selectedAddress
        return
    }

    const res: any = await getAddressListAPI({ page: 1, size: 100 })
    addressIList.value = res.result.rows
    let item = addressIList.value.find((item: any) => item.isDefault == true)
    if (item) {
        selecteAddress.value = item
    } else {
        item = addressIList.value[0]
        selecteAddress.value = item
    }
}
// 提交订单
const onOrderSubmit = async () => {
    // 没有收货地址提醒
    if (!selecteAddress.value?.id) {
        return uni.showToast({ icon: 'none', title: '请选择收货地址' })
    }
    const list = JSON.parse(JSON.stringify(goods.value))
    const info: any = {
        skus: [],
        addressId: selecteAddress.value.id,
        frontAmount: Math.round(orderInfo.value.fullTotal * 100),
        payType: 1,
    }
    for (const i in list) {
        console.log(list[i])
        info.skus.push({ skuId: list[i].skuId, quantity: list[i].countNum })
    }
    const res: any = await createOrderAPI(info)
    if (res.msg == 'success') {
        const orderId = res.result.id
        for (const i in list) {
            console.log(list[i])
            if(list[i].id){
                await deleteMemberCartAPI(list[i].id)
            }
        }
        // 直接购买
        orderStore.setOrderInfo({})
        uni.redirectTo({
            url: '/pages/shop/pay?id=' + orderId,
        })
    }
}
const toPage = (url: string) => {
    uni.navigateTo({
        url,
    })
}
</script>

<template>
    <scroll-view enable-back-to-top scroll-y class="viewport">
        <!-- 收货地址 -->
        <navigator
            v-if="selecteAddress"
            class="shipment"
            hover-class="none"
            url="/pages/shop/address?from=order"
        >
            <view style="display: flex;align-items: center;width: 100%">
                <view class="infoBox">
                    <view class="address"> {{ selecteAddress.fullLocation }} {{ selecteAddress.address }} </view>
                    <view class="user"> {{ selecteAddress?.receiver }} {{ selecteAddress?.contact }} </view>
                </view>
                <text class="icon icon-right"></text>
            </view>
        </navigator>
        <navigator
            v-else
            class="shipment"
            hover-class="none"
            url="/pages/shop/address?from=order"
        >
            <view style="display: flex;align-items: center;width: 100%">
                <view class="address"> 请选择收货地址 </view>
                <text class="icon icon-right"></text>
            </view>

        </navigator>
        <!-- 商品信息 -->

        <view class="goods" v-if="goods.length>0">
            <view
                v-for="item in goods"
                :key="item.skuId"
                class="item"
                hover-class="none"
                @click="toPage(`/pages/goods/goods?id=${item.id}`)"
            >
                <image :src="item?.goodInfo?.picture" alt="" class="picture"  />
                <view class="meta">
                    <view class="name ellipsis"> {{ item.goodInfo?.name }} </view>
                    <view class="attrs">{{ item.specsText }}</view>
                    <view class="prices">
                        <view class="pay-price symbol">{{ item.showPrice }}</view>
                    </view>
                    <view class="count">x{{ item.countNum }}</view>
                </view>
            </view>
        </view>
        <view class="settlementPay">
            <text class="textPay">支付方式 </text>
            <text class="numberPay">在线支付</text>
        </view>
        <!-- 支付金额 -->
        <view class="settlement">
            <view class="title">订单明细</view>
            <view class="item">
                <text class="text">商品小计 </text>
                <text class="number symbolNormal">{{ orderInfo.total }}</text>
            </view>
            <view class="item">
                <text class="text">配送费 </text>
                <text class="number symbolNormal">{{ orderInfo.fee / 100 }}</text>
            </view>
            <view class="item">
                <text class="text">优惠券</text>
                <text class="number ">暂无可用
                    <svg class="symbolArrow" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M7.5 21L16.5 12L7.5 3" stroke="#333333" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg></text>

            </view>
            <view class="coupon"></view>
            <view class="item mb-0">
                <text class="text">实付: </text>
                <text class="number symbolNormal">{{ orderInfo.total }}
                </text>
            </view>
        </view>
    </scroll-view>

    <!-- 吸底工具栏 -->
    <view class="toolbar" >
        <view class="total-pay ">
            实付
            <text class="number symbol">{{ orderInfo.total }}</text>
        </view>
        <view class="button" :class="{ disabled: !selecteAddress?.id }" @tap="onOrderSubmit">
            创建订单
        </view>
    </view>
</template>

<style lang="scss" scoped>
page {
    display: flex;
    flex-direction: column;
    background-color: #f4f4f4;
}

.symbol::before {
    content: '¥';
    color: var(--Brand-Green-Primary, #3C9CFF);
    font-family: "PingFang HK";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 32px */
    margin-right: 8rpx;
}
.symbol {
    margin-top: 20rpx;
}
.symbolNormal::before {
    content: '¥';
    color: var(--Brand-Green-Primary, #3C9CFF);
    font-family: "PingFang HK";
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 32px */
}
.symbolNormal {
    font-size: 24rpx;
    margin-top: 20rpx;
}



.shipment {
    height: 160rpx;
    border-radius: 12px;
    padding: 24rpx;
    border: 1px solid var(--Border-Border-Primary, #EDEDED);
    background: var(--Normal-White, #FFF);
    margin: 24rpx 18rpx;
    display: flex;
    align-items: center;
    .infoBox{
        width: 660rpx;
        .user {
            color: var(--Normal-Black-666, #666);
            font-family: "PingFang HK";
            font-size: 20rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 20px */
            margin-top: 20rpx;
        }

        .address {
            color: var(--Normal-Black-333, #333);
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 36px */
            letter-spacing: 1.2px;
        }
    }
    .icon {
        font-size: 36rpx;
        color: #333;
    }

}

.goods {
    margin: 20rpx;
    padding: 0 20rpx;
    border-radius: 10rpx;
    background-color: #fff;

    .item {
        width:600rpx;
        display: flex;
        padding: 30rpx 0;
        border-top: 1rpx solid #eee;

        &:first-child {
            border-top: none;
        }

        .picture {
            width: 128rpx;
            height: 128rpx;
            border-radius: 4px;
            margin-right: 24rpx;
        }

        .meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .name {
            width: 100%;
            color: var(--Normal-Black-333, #333);
            font-family: "PingFang SC";
            font-size: 28rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 28px */
            overflow: hidden;
        }

        .attrs {
            color: var(--Normal-Black-666, #666);
            font-family: "PingFang SC";
            font-size: 22rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 22px */
            margin-top: 16rpx;
        }

        .prices {
            display: flex;
            align-items: baseline;
            margin-top: 6rpx;
            font-size: 28rpx;

            .pay-price {
                color: var(--Brand-Green-Primary, #3C9CFF);
                font-family: "PingFang HK";
                font-size: 32rpx;
                font-style: normal;
                font-weight: 600;
                line-height: 100%; /* 32px */
            }

            .price {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
            }
        }

        .count {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 26rpx;
            color: #444;
        }
    }
}

.related {
    margin: 20rpx;
    padding: 0 20rpx;
    border-radius: 10rpx;
    background-color: #fff;

    .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 80rpx;
        font-size: 26rpx;
        color: #333;
    }

    .input {
        flex: 1;
        text-align: right;
        margin: 20rpx 0;
        padding-right: 20rpx;
        font-size: 26rpx;
        color: #999;
    }

    .item .text {
        width: 125rpx;
    }

    .picker {
        color: #666;
    }

    .picker::after {
        content: '\e6c2';
    }
}
.settlementPay{
    margin: 20rpx;
    display: flex;
    padding: 28rpx 24rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 34rpx;
    border-radius: 12rpx;
    border: 1px solid var(--Border-Border-Primary, #EDEDED);
    background: var(--Normal-White, #FFF);

    .textPay{
        color: var(--Normal-Black-333, #333);
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 28px */
    }
    .numberPay{
        color: var(--Normal-Black-666, #666);
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 24px */
    }
}

/* 结算清单 */
.settlement {
    margin: 20rpx 20rpx 148rpx;
    min-height: 350rpx;
    flex-shrink: 0;
    border-radius: 12px;
    border: 1px solid var(--Border-Border-Primary, #EDEDED);
    background: var(--Normal-White, #FFF);
    padding: 28rpx 24rpx;
    .title{
        color: var(--Normal-Black-333, #333);
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 28px */
        margin-bottom: 34rpx;
    }
    .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32rpx;
        .text{
            color: var(--Normal-Black-666, #666);
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
        }
        .number{
            color: var(--Normal-Black-333, #333);
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 24px */
            display: flex;
            align-items: center;
            .symbolArrow {
                width: 24rpx;
                height: 24rpx;
            }
        }

    }
    .mb-0{
        margin-bottom: 0;
    }
    .coupon{
        width: 100%;
        height: 2rpx;
        background: var(--Border-Border-Primary, #EDEDED);
        margin-bottom: 32rpx;
    }

    .danger {
        color: #cf4444;
    }
}

/* 吸底工具栏 */
.toolbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: #fff;
    height: 128rpx;
    padding: 20rpx 36rpx;
    border-top: 1rpx solid #eaeaea;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .total-pay {
        color: var(--Brand-Green-Primary, #3C9CFF);
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 32px */

        .decimal {
            font-size: 75%;
        }
    }

    .button {
        width: 327rpx;
        height: 88rpx;
        display: flex;
        padding: 17rpx 50rpx;
        justify-content: center;
        align-items: center;
        gap: 8rpx;
        align-self: stretch;
        border-radius: 16rpx;
        border: var(--stroke-weight-1, 1px) solid var(--Brand-Green-Primary, #3C9CFF);
        background: var(--Brand-Green-Primary, #3C9CFF);
        color: var(--Normal-White, #FFF);
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 24px */
    }

    .disabled {
        opacity: 0.6;
    }
}
</style>
