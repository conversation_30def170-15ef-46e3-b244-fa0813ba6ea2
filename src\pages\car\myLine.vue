<template>
    <u-navbar :is-back="true" title="我的线路">
        <template v-slot:right>
            <image class="rightIcon" src="../../static/imgs/car/date.svg" @tap="showDate"></image>
        </template>
    </u-navbar>
    <u-notice-bar :list="runLine" mode="horizontal" type="warning"></u-notice-bar>
    <view class="main">
        <view class="box borderBottomRadius borderTopRadius">
            <view class="line" @tap="listToLine()">
                <view class="lineTitle">{{ dateInterval() }}</view>
                <view class="lineDesc">查看汇总</view>
                <L-arrow></L-arrow>
            </view>
            <view class="lineInfo">
                <view class="lineItem">
                    <view class="textTitle">{{ numberFormat(calcDiv(carInfo.totalMeter, 1000)) }}</view>
                    <view class="textDesc">总里程</view>
                </view>
                <view class="lineItem border">
                    <view class="textTitle">{{ carInfo.totalNum }}</view>
                    <view class="textDesc">线路</view>
                </view>
                <view class="lineItem">
                    <view class="textTitle">{{ formatSecondsToHHMMSS(carInfo.totalTime) }}</view>
                    <view class="textDesc">时间</view>
                </view>
            </view>
        </view>
    </view>
    <view class="listBox">
        <mescroll-empty v-if="dataList.length == 0"></mescroll-empty>
        <view v-for="item in dataList" class="listItem">
            <view class="listDate">
                {{ item.date }}
            </view>
            <view v-for="info in item.list" class="item" @tap="toLine([info])">
                <view class="topBox">
                    <view class="topImg">
                        <image class="imgItem" src="../../static/imgs/car/map.png"></image>
                    </view>
                    <view class="topInfo">
                        <view class="right">
                            <view style="display: flex;align-items: center">
                                <view class="redDot"></view>
                                <view class="startAddress">{{ info.fromAddress }}</view>
                            </view>
                            <view class="space"></view>
                            <view style="display: flex;align-items: center">
                                <view class="blueDot"></view>
                                <view class="endAddress">{{ info.toAddress }}</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="lineMain"></view>
                <view class="iconBox">
                    <view class="iconItem">
                        <image class="icon" src="../../static/imgs/car/line.svg"></image>
                        <view class="iconText">{{ info.distance }}km</view>
                    </view>
                    <view class="iconItem">
                        <image class="icon" src="../../static/imgs/car/time.svg"></image>
                        <view class="iconText">{{ info.timeText }}</view>
                    </view>
                    <view class="iconItem">
                        <image class="icon" src="../../static/imgs/car/speed.svg"></image>
                        <view class="iconText">{{ info.speed }}km/h</view>
                    </view>
                    <view class="iconDesc">查看线路</view>
                    <L-arrow></L-arrow>
                </view>
            </view>
        </view>
    </view>
    <u-popup v-model="show" border-radius="48" mode="bottom">
        <view class="maskBox">
            <view class="maskTitle">选择时间跨度</view>
            <view class="maskMain">
                <view class="maskDesc">
                    快捷选择
                </view>
                <view class="listBox">
                    <view :class="confirmType==0?'listItem active':'listItem'" @click="clickQuick(0)">全部</view>
                    <view :class="confirmType==1?'listItem active':'listItem'" @click="clickQuick(1)">当日</view>
                    <view :class="confirmType==2?'listItem active':'listItem'" @click="clickQuick(2)">3天</view>
                    <view :class="confirmType==3?'listItem active':'listItem'" @click="clickQuick(3)">7天</view>
                    <view :class="confirmType==4?'listItem active':'listItem'" @click="clickQuick(4)">30天</view>
                </view>
                <view class="maskDesc">
                    选择时间
                </view>
                <view class="timeBox">
                    <view :class="{'checkedBox':checkedBegin}" class="uncheckBox" @click="checkedDate(true)">
                        {{ defaultDate[0] }}
                    </view>
                    <view class="center"></view>
                    <view :class="{'checkedBox':!checkedBegin}" class="uncheckBox" @click="checkedDate(false)">
                        {{ defaultDate[1] }}
                    </view>
                </view>
            </view>
            <date-picker v-if="checkedBegin"
                         :default-date=defaultDate[0]
                         @onChange="onChange">
            </date-picker>
            <date-picker v-else
                         :default-date=defaultDate[1]
                         @onChange="onChange">
            </date-picker>
            <view class="btnBox">
                <u-button plain type="primary" @click="show=false">取消</u-button>
                <u-button type="primary" @click="searchLine">点击查询</u-button>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import {ref, watch} from 'vue'
import moment from "moment";
import {onShow} from "@dcloudio/uni-app";
import {getCarInfo, getRideRecord} from "@/services/device";
import {v4 as uuidv4} from 'uuid';
import {calcDiv, calcMul} from "@/utils/math";
import {gps2AMap} from "@/utils/location";
// 右上角按钮点击事件
const show = ref(false)
const checkedBegin = ref(true)
const defaultDate = ref(['2000-01-01', moment().format('YYYY-MM-DD')])
const getAll = ref(true)
const deviceInfo = ref({})
const showDate = () => {
    show.value = true
}

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})
/**
 * 显示日期区间
 */
const dateInterval = () => {
    if (getAll.value) {
        return '全部记录'
    }
    if (defaultDate.value[0] == defaultDate.value[1]) {
        return defaultDate.value[0].split('-').join('.')
    }
    return defaultDate.value[0].split('-').join('.') + ' - ' + defaultDate.value[1].split('-').join('.')
}
const confirmType: any = ref(0)
const clickQuick = (type: number) => {
    switch (type) {
        case 0://全部
            defaultDate.value = ['2000-01-01', moment().format('YYYY-MM-DD')]
            checkedBegin.value = true
            break
        case 1://当日
            defaultDate.value = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 2://3天
            defaultDate.value = [moment().subtract(3, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 3://7天
            defaultDate.value = [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 4://30天
            defaultDate.value = [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
    }
}
watch(() => defaultDate.value, () => {
    let list = [
        ['2000-01-01', moment().format('YYYY-MM-DD')],
        [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(3, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    ]
    let num = 0
    for (let i = 0; i < list.length; i++) {
        if (list[i][0] === defaultDate.value[0] && list[i][1] === defaultDate.value[1]) {
            confirmType.value = i
            num++
        }
    }
    if (num == 0) {
        confirmType.value = 5
    }
}, {
    deep: true
})
// 打开日期选择框
const checkedDate = (isBegin: boolean) => {
    console.log('checkedDate', isBegin)
    checkedBegin.value = isBegin
}
// 点击确定按钮，返回当前选择的值
const onChange = (date: any) => {
    defaultDate.value[checkedBegin.value ? 0 : 1] = date
}
const toLine = (lines: any) => {
    const uuid = lines.length === 1 ? lines[0].id : uuidv4()
    uni.setStorageSync(uuid, JSON.stringify(lines))
    uni.navigateTo({
        url: '/pages/line/line?uuid=' + uuid + '&deviceName=' + getDeviceName()
    })
}
const listToLine = () => {
    const list: any = []
    dataList.value.forEach((item: any) => {
        list.push(...item.list)
    })
    toLine(list)
}

function getDeviceName() {
    if (!deviceInfo.value.name || deviceInfo.value.name === 'null') {
        return deviceInfo.value.carModel.brand + deviceInfo.value.carModel.model
    } else {
        deviceInfo.value.name
    }
}

const searchLine = () => {
    let startTime = new Date(defaultDate.value[0]).getTime()
    let endTime = new Date(defaultDate.value[1]).getTime()
    if (startTime > endTime) {//如果开始日期大于结束日期,则将结束日期设置为开始日期
        filter.begin = defaultDate.value[1]
        filter.end = defaultDate.value[0]
    }
    filter.begin = defaultDate.value[0]
    filter.end = defaultDate.value[1]
    filter.deviceId = props.id
    show.value = false
    //获取数据
    getResetData()
}
const carInfo = ref({
    totalNum: 0,
    totalMeter: 0,
    totalTime: 0
})
onShow(async () => {
    show.value = false
    filter.deviceId = props.id
    let res = await getCarInfo(props.id)
    if (res.msg == 'success') {
        deviceInfo.value = res.result
    }
    getResetData()
})
const filter = {
    deviceId: props.id,
    begin: defaultDate.value[0],
    end: defaultDate.value[1],
    page: 1,
    size: 10000 //todo 服务器没有汇总数据所以一次拉取10000条，后续接口修改在优化这里
}
const dataList = ref([])

/**
 * 获取筛选参数
 * todo 当前全部逻辑为2000-01-01 - now 所以不需要该方法，后面修在处理
 */
function getFilterValue() {
    if (getAll.value) {
        return {
            deviceId: filter.deviceId,
            page: filter.page,
            size: filter.size,
        }
    } else {
        return filter
    }
}

const runLine: any = ref([])
const runInfo = ref({})
const getResetData = async () => {
    console.log('getFilterValue', JSON.stringify(filter))
    let res = await getRideRecord(filter)
    if (res.msg == "success") {
        let list: any = res.result.rows
        //     根据createTime ,moment格式化YYYY-MM-DD ,作为主键拆分二维数组[{date，list}]
        let dateList: any = []
        let totalNum = 0
        let totalMeter = 0
        let totalTime = 0
        const arr = list.filter((i: any) => i.end == null)
        if (arr.length == 1) {
            runInfo.value = arr[0]
            runLine.value = ['当前有骑行未结束']
        }
        list = list.filter((i: any) => i.end != null)
        for (let item of list) {
            let date = moment(item.createTime).format('YYYY-MM-DD')
            let index = dateList.findIndex((i: any) => i.date === date)
            item.fromAddress = await getAddress(item.fromLat, item.fromLng)
            item.toAddress = await getAddress(item.toLat, item.toLng)
            // 将距离转换为公里
            item.distance = numberFormat(calcDiv(item.meter, 1000));
            item.timeText = formatSecondsToHHMMSS(item.time)
            let speed = calcDiv(item.meter, item.time)
            item.speed = calcMul(speed, 3.6).toFixed(2)
            // 根据distance和time  计算时速
            if (index === -1) {
                dateList.push({
                    date: date,
                    list: [item]
                })
            } else {
                dateList[index].list.push(item)
            }
            totalNum++
            totalMeter += item.meter
            totalTime += item.time
        }
        carInfo.value = {
            totalNum: Math.round(totalNum),
            totalMeter: Math.round(totalMeter),
            totalTime: Math.round(totalTime)
        }
        dataList.value = dateList
    }
}
const formatSecondsToHHMMSS = (seconds: any) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(secs).padStart(2, '0');
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
const getAddress = async (lat: number, lng: number) => {
    let result = gps2AMap(lng, lat)
    const apiKey = 'a3d415243ae2185eb0f16452497e3867'; // 替换为你的高德地图API Key
    const address = uni.getStorageSync(lat + '-' + lng)
    if (address) {
        return address
    }
    if (lat && lng) {
        return new Promise((resolve, reject) => {
            uni.request({
                url: `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${result[0]},${result[1]}&key=${apiKey}&radius=1000&extensions=all `,
                methods: "GET",//仅为示例，并非真实接口地址。
                success: (res: any) => {
                    uni.setStorageSync(lat + '-' + lng, res.data.regeocode.formatted_address)
                    resolve(res.data.regeocode.formatted_address)
                }
            });
        })
    } else {
        return '骑行未结束！'
    }
};
const numberFormat = (num: number) => {
    // 判断小数位多余三位
    if (num.toString().split('.')[1] && num.toString().split('.')[1].length > 2) {
        return num.toFixed(2)
    } else {
        return num
    }
}
</script>

<style lang="scss" scoped>
.rightIcon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 32rpx;
}

.maskBox {
    background: #FFFFFF;

    .maskTitle {
        margin-top: 48rpx;
        color: #4F4F4F;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 48rpx; /* 150% */
    }

    .maskMain {
        margin: 32rpx;
        padding: 24rpx;
        border-radius: 6px;
        background: #F7F7F7;

        .maskDesc {
            color: #969696;
            font-family: "PingFang SC";
            font-size: 28rpx;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 24rpx;
        }

        .listBox {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            padding: 0;

            .listItem {
                flex: 1;
                display: flex;
                height: 64rpx;
                justify-content: center;
                align-items: center;
                border-radius: 8rpx;
                border: 1px solid #F2F2F2;
                background: #FFF;
                color: #4F4F4F;
                text-align: center;
                font-family: "PingFang SC";
                font-size: 28rpx;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-bottom: 24rpx;
                margin-right: 10rpx;
            }

            .active {
                border: 1px solid #4B85FD;
            }
        }

        .timeBox {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .uncheckBox {
                flex: 1;
                height: 64rpx;
                flex-shrink: 0;
                border-radius: 8rpx;
                border: 2rpx solid #F2F2F2;
                background: #FFF;
                color: #969696;
                font-family: "PingFang SC";
                font-size: 28rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 128.571% */
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .center {
                width: 24rpx;
                height: 4rpx;
                flex-shrink: 0;
                background: #C3C3C3;
                margin: 0 12rpx;
            }

            .checkedBox {
                border: 2rpx solid #4B85FD;
                color: #282828;
                font-weight: 500;
            }
        }
    }

    .btnBox {
        display: flex;
        margin: 36rpx;

        .u-btn {
            flex: 1;
            margin: 0 32rpx;
        }
    }
}

.main {
    padding: 32rpx;
    font-family: 'PingFang SC';

    .box {
        padding: 26rpx 22rpx;
        background: #FFFFFF;
    }

    .line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .lineTitle {
            color: var(--Text-main, #282A2E);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Callout, 32rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Callout, 42rpx);
            letter-spacing: var(--Letter-Spacing-Callout, -0.32px);
        }

        .lineDesc {
            flex: 1;
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 12px;
            text-align: right;
            margin-right: 10rpx;
        }
    }

    .lineInfo {
        display: flex;
        width: 638rpx;
        padding: 28rpx;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        border-radius: 16rpx;
        background: #F7F7F7;

        .border {
            border-left: 1px solid #C3C3C3;
            border-right: 1px solid #C3C3C3;
            flex: 1;
        }

        .lineItem {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .textTitle {
                color: #242424;
                text-align: center;
                font-family: D-DIN-PRO;
                font-size: 28rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 28rpx; /* 100% */
                margin-bottom: 10rpx;
            }

            .textDesc {
                color: #C3C3C3;
                text-align: center;
                font-family: "PingFang SC";
                font-size: 20rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 20rpx; /* 100% */
            }
        }
    }
}

.listBox {
    padding: 0 32rpx 32rpx;

    .listDate {
        color: var(--Text-main, #282A2E);
        /* Medium/Callout */
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 42rpx; /* 131.25% */
        letter-spacing: -0.32px;
        margin-bottom: 24rpx;
    }

    .item {
        padding: 24rpx;
        flex-shrink: 0;
        margin-bottom: 32rpx;
        border-radius: 16rpx;
        border: 0.5px solid #EBEBEB;
        background: #FFF;
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);

        .topBox {
            display: flex;

            .topImg {
                width: 112rpx;
                height: 112rpx;
                border-radius: 12rpx;
                flex-shrink: 0;

                .imgItem {
                    width: 112rpx;
                    height: 112rpx;
                }
            }

            .topInfo {
                height: 100%;
                display: flex;
                margin-top: 10rpx;


                .right {
                    flex: 1;
                    height: 98rpx;
                    display: flex;
                    flex-direction: column;
                    .redDot{
                        background: #E45656;
                        width: 15rpx;
                        height: 15rpx;
                        border-radius: 15rpx;
                        margin: 0 5rpx;
                    }
                    .blueDot{
                        background: #3C9CFF;
                        width: 15rpx;
                        height: 15rpx;
                        border-radius: 15rpx;
                        margin: 0 5rpx;
                    }

                    .startAddress {
                        width: 460rpx;
                        color: #969696;
                        font-family: "PingFang SC";
                        font-size: 24rpx;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 32rpx; /* 133.333% */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .space {
                        flex: 1;
                        background: #ededed;
                        width: 1px;
                        margin: 0 12rpx;
                    }

                    .endAddress {
                        width: 460rpx;
                        color: #969696;
                        font-family: "PingFang SC";
                        font-size: 24rpx;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 32rpx; /* 133.333% */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

            }
        }

        .lineMain {
            width: 100%;
            height: 1px;
            background: #E6E6E8;
            margin: 20rpx 0;
        }

        .iconBox {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .iconItem {
                flex: 1;
                display: flex;
                align-items: center;

                .icon {
                    width: 26rpx;
                    height: 26rpx;
                    margin-right: 10rpx;
                }

                .iconText {
                    color: var(--Text-tips, #909399);
                    font-family: var(--Font-Family, "PingFang SC");
                    font-size: var(--Font-Size-Footnote, 26rpx);
                    font-style: normal;
                    font-weight: 400;
                    line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
                    letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
                }

            }

            .iconDesc {
                color: var(--Text-tips, #909399);
                text-align: right;
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Caption1, 24rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
                letter-spacing: var(--Letter-Spacing-Caption1, 0px);
            }
        }
    }
}
</style>
