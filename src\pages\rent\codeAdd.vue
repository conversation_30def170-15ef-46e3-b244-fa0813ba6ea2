<template>
    <view v-if="mainInfo" class="mainBox">
        <view class="top">
            <image :src="mainInfo.menu.modelImg" mode="aspectFit"></image>
        </view>
        <view class="formBox">
            <view class="label">套餐名称：</view>
            <view class="content">{{ mainInfo.menu.name }}</view>
        </view>
        <view class="formBox">
            <view class="label">门店名称：</view>
            <view class="content">{{ mainInfo.store.address }}{{ mainInfo.store.name }}</view>
        </view>
        <view class="formBox">
            <view class="label">车型：</view>
            <view class="content">{{ mainInfo.menu.model }}</view>
        </view>
        <view class="formBox">
            <view class="label">设备SN：</view>
            <view class="content">{{ sn }}</view>
        </view>
    </view>
    <view class="orderBox">
        <view class="orderMain">
            <view class="rightBoxMain">
                <u-button type="primary" @click="confirm">确认信息</u-button>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {getOrderDetail, receiveCarAPI} from "@/services/rent";

const props = defineProps({
    sn: {
        type: String,
        default: ''
    },
    order: {
        type: String,
        default: ''
    }
})
onMounted(() => {
    getMemberOrderData()
})
const mainInfo: any = ref(null)
const getMemberOrderData = async () => {
    let res = await getOrderDetail(props.order)
    console.log(res)
    mainInfo.value = res.result
}
const confirm = async () => {
    let res = await receiveCarAPI(props.order, {
        deviceSn: props.sn,
        snapshot: mainInfo.value.menu.modelImg
    })
    console.log(res,'car')
    uni.navigateTo({
        url: '/pages/rent/codeResult'
    })
}
</script>

<style lang="scss" scoped>
.top {
    margin: 32rpx;
    padding: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 16rpx;
    border-bottom: 0.5px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white-black, #FFF);

    image {
        width: 512rpx;
        height: 292rpx;
        flex-shrink: 0;
    }
}

.formBox {
    display: flex;
    margin: 32rpx;
    padding: 24rpx;
    background: #ffffff;
    justify-content: space-between;
    border-radius: 16rpx;
    border-bottom: 0.5px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white-black, #FFF);

    .label {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 34rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }

    .content {
        color: var(--Text-content, #53565C);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Footnote, 26rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
        letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
    }
}

.orderMain {
    position: fixed;
    bottom: 0;
    width: 750rpx;
    height: 130rpx;
    flex-shrink: 0;
    background: #FFF;
    box-shadow: 0px 4rpx 4rpx 0px rgba(0, 0, 0, 0.25);
    display: flex;

    .rightBoxMain {
        width: 100%;
        padding: 24rpx 32rpx;
        flex-shrink: 0;
    }

}
</style>
