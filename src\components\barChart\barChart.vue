<template>
    <view class="content">
        <l-echart ref="barChart"></l-echart>
    </view>
</template>

<script>
import * as echarts from 'echarts/core';
import {BarChart} from 'echarts/charts';
import {GridComponent, TooltipComponent} from 'echarts/components';
import {CanvasRenderer} from 'echarts/renderers';
import moment from "moment";
// 注册必须的组件
echarts.use([
    TooltipComponent,
    GridComponent,
    BarChart,
    CanvasRenderer
]);
export default {
    data() {
        return {}
    },
    // 接收参数
    props: {
        energyList: {
            type: Array,
            default: () => ([])
        }
    },
    // 动态处理consumeList,deep
    watch: {
        energyList: {
            handler(newVal) {
                this.loadBarData(newVal)
            },
        }
    },
    mounted() {
        //加载柱状图数据--示例1
        this.loadBarData(this.energyList)
    },
    methods: {
        //加载柱状图数据,这里可以去请求服务器获取数据
        loadBarData(list) {
            console.log(list, 99999)
            // list  提取consume 组成新数组
            let y = list.map(item => item.energy)
            let x = list.map(item => moment(item.time * 1000).format('HH'))
            if(list.length==0){
                y=[70,70,70,70,70,70,70,70,70,70]
                x=[0,0,0,0,0,0,0,0,0,0]
            }

            //这里请求服务器拿到数据
            let myData = {
                x,
                y
            }
            let colors = []
            for (let i in y) {
                if (y[i] > 60) {
                    colors.push('#398ADE')
                } else {
                    colors.push('#94C9FF')
                }
            }
            //这里option配置参考文档：https://echarts.apache.org/zh/option.html
            this.$refs.barChart.init(echarts, chart => {
                chart.setOption({
                    //设置曲线的颜色
                    grid: {
                        // left: '1%',
                        // right: '1%',
                        // bottom: '1%',
                        // containLabel: true
                        top: 10,
                        bottom: 10,
                        left: 0,
                        right: 0
                    },
                    yAxis: [{
                        type: 'value',
                        axisLabel: {
                            show: true, // 隐藏y轴文字
                        },
                        // y轴分割线设置为虚线
                        splitLine: {
                            show: true,
                        },
                        min: 10,
                        max: 100
                    }],
                    xAxis: [{
                        type: 'category',
                        axisLabel: {
                            show: false // 隐藏x轴文字
                        },
                        data: myData.x,
                        axisTick: {
                            show: false,
                        },
                    }],
                    series: [{
                        name: '数量',
                        type: 'bar',
                        data: myData.y.map((value, index) => ({
                            value: value,
                            itemStyle: {
                                color: colors[index % colors.length] // 使用不同的颜色
                            }
                        })),
                        barWidth: '10'
                    }]
                });

            });
        },
    },
}
</script>

<style lang="scss" scoped>
.content {
    width: 284rpx;
    height: 210rpx;
}
</style>