import { http } from '@/utils/http'
const shopURL = 'https://shop.evl.usr.86.ltd/srv'
export const getOrderAPI = (id: any) => {
  return http({
    method: 'GET',
    url: shopURL+`/v1/me/order/${id}`
  })
}

export const getPayLinkAPI = (orderId: any) => {
  return http({
    url: shopURL+`/v1/me/alipay/m/${orderId}`
  })
}
export const getWalletAPI = (orderId:any) => {
  return http({
    method: 'POST',
    url: shopURL+`/v1/me/wallet/pay/${orderId}`
  })
}


export const getMoneyOutAPi = (walletId:any,params:any) => {
  return http({
    method: 'GET',
    url: shopURL+`/v1/me/transaction/${walletId}`,
    data:params,
  })
}
export const getUserMoneyInfoAPI = () => {
  return http({
    method: 'GET',
    url: shopURL+`/v1/me/wallet`
  })
}



