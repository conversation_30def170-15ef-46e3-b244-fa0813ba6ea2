import moment from "moment/moment";

const systemKeys = [
    "launchFlag",
    "member",
    "carInfo",
    "information",
    'carStatus',
    'order',
    'address',
]

export const clearData = () => {
    //获取storageInfo中的keys
    const cacheMap = []
    for (let i = 0; i < systemKeys.length; i++){
        const value = uni.getStorageSync(systemKeys[i])
        cacheMap.push({
            keys: systemKeys[i],
            value: value
        })
    }
    uni.clearStorageSync()
    for (let i = 0; i < cacheMap.length; i++){
        uni.setStorageSync(cacheMap[i].keys, cacheMap[i].value)
    }
    console.log("clearStorage finish")
}

export const formatData = (kbSize:number) => {
   //判断kbSize大小，格式化数据并添加单位
    if (kbSize < 1024) {
        return kbSize + " KB"
    } else if (kbSize >= 1024 && kbSize < 1024 * 1024) {
        return (kbSize / 1024).toFixed(2) + " MB"
    } else {
        return (kbSize / (1024 * 1024)).toFixed(2) + "GB"
    }
}