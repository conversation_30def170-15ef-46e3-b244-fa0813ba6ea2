<template>
    <view class=" mb-32">
        <view class="topMain" @click="toMyAccount">
            <image alt="" class="topBg" src="../../static/imgs/mine/bg.png" srcset=""/>
            <L-top-height></L-top-height>
            <view class="topBox">
                <u-avatar :src="userInfo.avatar" class="avatar" size="144"></u-avatar>
                <view class="infoBox">
                    <view class="name">{{ userInfo.nickname }}</view>
                    <view class="phone">{{ userInfo.tel }}</view>
                </view>
            </view>
        </view>
        <view class="bannerBox">
            <u-swiper :list="list" height="264" img-mode="scaleToFill" name="img" @click="bannerTap"></u-swiper>
        </view>
        <view class="orderBox">
            <view class="orderTop" @click="toOrder(9999)">
                <view class="orderTitle">我的订单</view>
                <view class="orderDesc">全部</view>
                <L-arrow></L-arrow>
            </view>
            <view class="orderBottom">
                <view class="orderItem" @click="toOrder(1)">
                    <image class="icon" src="../../static/imgs/mine/order/pay.svg"></image>
                    <view class="orderText">待付款</view>
                </view>
                <view class="orderItem" @click="toOrder(4)">
                    <image class="icon" src="../../static/imgs/mine/order/pay.svg"></image>
                    <view class="orderText">待发货</view>
                </view>
                <view class="orderItem" @click="toOrder(5)">
                    <image class="icon" src="../../static/imgs/mine/order/pay.svg"></image>
                    <view class="orderText">待收货</view>
                </view>
                <view class="orderItem" @click="toOrder(10)">
                    <image class="icon" src="../../static/imgs/mine/order/pay.svg"></image>
                    <view class="orderText">退款 / 售后</view>
                </view>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem  borderTopRadius border" @click="toPage('/pages/shop/wallet')">钱包管理
                <view class="num">余额：¥{{ walletInfo.balance / 100 }}</view>
                <L-arrow></L-arrow>
            </view>
            <view class="listItem   borderBottomRadius" @click="toPage('/pages/shop/address')">地址管理
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem  borderTopRadius " @click="toPage('/pages/rent/order')">租车订单
                <L-arrow></L-arrow>
            </view>
            <view class="listItem   borderBottomRadius" @click="toPage('/pages/rent/list')">我的租车
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem  borderTopRadius borderBottomRadius" @click="toPage('/pages/rent/real')">实名认证
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem  borderTopRadius borderBottomRadius" @click="toPage('/pages/mine/carManager')">车辆管理
                <view class="num">{{ deviceList.length }}</view>
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem  borderTopRadius borderBottomRadius"
                  @click="toPage('/pages/index/notificationSettings')">消息设置
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem border  borderTopRadius " @click="toMyAccount">账号管理
                <L-arrow></L-arrow>
            </view>
            <view class="listItem border" @click="toPage('/pages/mine/privacySetting')">隐私设置
                <L-arrow></L-arrow>
            </view>
            <view class="listItem borderBottomRadius" @click="cleanCache">清理缓存
                <view class="cache">{{ cacheNum }}</view>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem border borderTopRadius" @click="showSend">客服邮箱
                <L-arrow></L-arrow>
            </view>
            <view class="listItem border " @click="toPage('/pages/mine/helpCenter')">帮助中心
                <L-arrow></L-arrow>
            </view>
            <view class="listItem borderBottomRadius" @click="toPage('/pages/mine/about')">关于
                <L-arrow></L-arrow>
            </view>
        </view>
    </view>
    <u-modal v-model="send" :mask-close-able="true" :show-cancel-button="true" content="即将向客服发送电子邮件"
             title="客服邮箱"
             @confirm="sendEmail"></u-modal>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getUserInfo} from "@/services/user";
import {onShow} from "@dcloudio/uni-app";
import {getBanner} from "@/services/banner";
import {clearData, formatData} from "@/utils/storage";
import {getMyDevice} from "@/services/device";
import {useMemberStore} from "@/stores";
import {getUserMoneyInfoAPI} from "@/services/pay";

const memberStore = useMemberStore()
const userInfo: any = ref({})
const deviceList: any = ref([])
const resetData = async () => {
    const res = await getUserInfo()
    if (res.msg == 'success') {
        userInfo.value = res.result
        memberStore.setUserInfo(res.result)
    }
    const bannerRes = await getBanner({
        page: 1,
        size: 10,
        position: 1
    })
    if (bannerRes.msg == 'success') {
        list.value = bannerRes.result.rows
    }
    const deviceRes = await getMyDevice()
    if (deviceRes.msg == 'success') {
        deviceList.value = deviceRes.result
    }
    getStorageSize()
}
const getStorageSize = () => {
    uni.getStorageInfo({
        success: function (res) {
            console.log('currentSize', res.currentSize);
            //把值减去5K（系统设置），小于0也显示0
            //判断size 是否小于0 小于0则赋值0
            const size = (res.currentSize < 5) ? 0 : res.currentSize - 5;
            //把kb数据转化为 MB GB等数据格式
            cacheNum.value = formatData(size)
        }
    })
}

onShow(() => {
    resetData()
    resetWallet()
})
const send = ref(false)
const showSend = () => {
    send.value = true
}
//  发送邮件
const sendEmail = () => {
    // 定义邮件的各个部分
    const to = '<EMAIL>'; // 收件人邮箱地址
    const subject = '邮件主题'; // 邮件主题
    const body = '这是邮件正文内容。'; // 邮件正文
    // 构建邮件 URL
    const mailtoUrl = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    // 使用 plus.runtime.openURL 唤起邮件客户端
    plus.runtime.openURL(mailtoUrl, function (error) {
        console.error('无法打开邮件客户端: ' + error.message);
    });
    send.value = false
}
// 轮播图
const list = ref([])
// 轮播图点击事件
const bannerTap = (index: any) => {
    let navInfo: any = list.value[index]
    if (navInfo.type == 0) {
        toPage(navInfo.url)
    } else {
        plus.runtime.openURL(navInfo.url, function (error) {
            console.error('无法打开网址: ' + error.message);
        });
    }
}
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    })
}
const cleanCache = () => {
    //清理Line开头的骑行数据
    uni.showLoading({
        title: '清理中...'
    })
    clearData();
    getStorageSize()
    uni.hideLoading()
}
const cacheNum = ref('0.00M')
const toMyAccount = () => {
    uni.navigateTo({
        url: '/pages/mine/myAccount'
    })
}
const walletInfo: any = ref({})
const resetWallet = async () => {
    const res = await getUserMoneyInfoAPI()
    walletInfo.value = res.result
}
const toOrder = (status: any) => {
    uni.navigateTo({
        url: '/pages/shop/order?status=' + status
    })
}
</script>

<style lang="scss" scoped>
.orderBox {
    margin: 32rpx;
    display: flex;
    width: 686rpx;
    padding: 32rpx;
    flex-direction: column;
    align-items: center;
    gap: 14px;
    border-radius: 8px;
    border-bottom: 0.5px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white-black, #FFF);

    .orderTop {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .orderTitle {
            color: #000;
            font-feature-settings: 'case' on;
            /* Regular/Body */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Body, 34rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
            letter-spacing: var(--Letter-Spacing-Body, -0.41px);
        }

        .orderDesc {
            flex: 1;
            color: var(--Text-content, #53565C);
            /* Regular/Footnote */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
            text-align: right;
            margin-right: 8rpx;
        }
    }

    .orderBottom {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .orderItem {
            width: 130rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .icon {
                width: 32px;
                height: 32px;
                margin-bottom: 16rpx;
            }

            .orderText {
                color: #000;
                text-align: center;
                /* Regular/Caption1 */
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Caption1, 24rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
                letter-spacing: var(--Letter-Spacing-Caption1, 0px);
            }
        }

    }
}

.topMain {
    width: 100%;
    background: #f5f5f5;
    position: relative;
    overflow: hidden;
    height: 300rpx;

    .topBg {
        width: 100%;
        position: absolute;
        left: 0;
        right: 0;
        height: 300rpx;
        object-fit: cover;
        z-index: 0;
    }

    .avatar {
        margin-left: 32rpx;
    }
}

.topBox {
    display: flex;
    position: absolute;
    top: 108rpx;
    left: 0;
    right: 0;
    padding: 20rpx;
    justify-content: space-between;
    align-items: center;

    .infoBox {
        flex: 1;
        margin-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .name {
            color: var(--Text-main, #282A2E);

            /* Medium/Headline */
            font-family: "PingFang SC";
            font-size: 34rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 44rpx; /* 129.412% */
            letter-spacing: -0.408px;
            text-align: left;
            margin-bottom: 16rpx;
        }

        .phone {
            color: var(--Text-content, #53565C);
            /* Regular/Footnote */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
            text-align: left;
        }
    }
}

.bannerBox {
    margin: 32rpx;
}

.listBox {
    margin: 32rpx;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .num {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

        .cache {
            color: var(--Text-content, #53565C);

            /* Regular/Footnote */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }
    }

    .border {
        border-bottom: 1px solid #ededed;
    }
}

.mb-32 {
    padding-bottom: 32rpx;
}
</style>
