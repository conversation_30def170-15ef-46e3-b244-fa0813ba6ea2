<script lang="ts" setup>
import {onShow} from "@dcloudio/uni-app";
import {computed, ref, watch} from 'vue'
import {calcAdd, calcDiv} from "@/utils/math";
import lineChart from '../../components/lineChart/lineChart.vue'
import chart from '../../components/barChart/barChart.vue'
import LeftBattery from "@/components/battery/left-battery.vue";
import moment from "moment/moment";

const leftFlag = ref(false)
const startup = ref(false)
const props = defineProps({
  carInfo: {
    type: Object,
    default: () => ({})
  },
  mainInfo: {
    type: Object,
    default: () => ({})
  },
  deviceList: {
    type: Array,
    default: () => ([])
  },
  address: {
    type: String,
    default: ''
  },
  consumeList: {
    type: Array,
    default: () => ([])
  },
  energyList: {
    type: Array,
    default: () => ([])
  },
  timeText: {
    type: String,
    default: ''
  },
  mainID: {
    type: String,
    default: ''
  },
})
// 使用 getter 函数精准监听
watch(
  () => props.carInfo.extInfo.lock,
  (newVal, oldVal) => {
    if (newVal == 0) {
      startup.value = false
    }
    // 你的业务逻辑
  }, {deep: true}
)
const mainCarInfo = ref(props.carInfo)
const emit = defineEmits(['change'])
const blueStatus = ref(1)
onShow(async () => {
  console.log(props.carInfo, 'info')
})
const carClose = () => {
  // todo  处理关机
  startup.value = false
}
// 显示切换车型
const showLeft = () => {
  leftFlag.value = !leftFlag.value
}
// 切换车辆
const emitCarDetail = (id: any) => {
  emit('change', id)
  leftFlag.value = false
}
const toFence = () => {
  uni.navigateTo({
    url: '/pages/car/Fence?id=2'
  })
}
// 车辆寻路
const toMapFence = () => {
  if (plus.os.name == "Android") {
    uni.navigateTo({
      url: '/pages/car/carFence?deviceId=' + props.carInfo.id
    })
  } else {
    uni.navigateTo({
      url: '/pages/car/carFenceIOS?deviceId=' + props.carInfo.id
    })
  }
}
// 车辆详情
const toSetting = async () => {
  uni.navigateTo({
    url: '/pages/car/carDetail?id=' + props.carInfo.id
  })
}
// 格式化时间
const formatSecondsToHHMMSS = (seconds: any) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(secs).padStart(2, '0');
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
// 线路汇总
const toLine = async () => {
  uni.navigateTo({
    url: '/pages/car/myLine?id=' + props.carInfo.id
  })
}
// 充电
const toBarChart = () => {
  uni.navigateTo({
    url: '/pages/car/barChart?id=' + props.carInfo.id
  })
}
// 电池能耗
const toLineChart = () => {
  uni.navigateTo({
    url: '/pages/car/lineChart?id=' + props.carInfo.id
  })
}
// 汇总数据
const consume = computed(() => {
  let total = 0
  props.consumeList.forEach((item: any) => {
    total = item.consume
  })
  //返回 props.consumeList 最后一个数组对象的consume
  return total
})
const energyTime = computed(() => {
  //获取props.energyList数组对象中最后一个元素的time值
  let list: any = props.energyList
  if (list.length == 0) {
    return '-- 小时前'
  }
  let time = list[list.length - 1].time * 1000
  // 根据时间戳计算距离当前的时间差，显示分钟前，小时前，日期
  const now = new Date().getTime();
  const diff = now - time;
  if (diff < 60000) {
    // 小于1分钟，显示“刚刚”
    return '刚刚';
  } else if (diff < 3600000) {
    // 小于1小时，显示分钟数
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前';
  } else {
    // 大于24小时 显示年月日时分秒
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
  }
})
// 消息通知
const toInfo = () => {
  uni.navigateTo({
    url: '/pages/index/information?sn=' + props.carInfo.sn + '&carName=' + props.carInfo.carModel?.brand + '' + props.carInfo.carModel?.model + '&carAddress=' + props.address + '&carID=' + props.mainID
  })
}
const numberFormat = (num: number) => {
  // 判断小数位多余三位
  if (num.toString().split('.')[1] && num.toString().split('.')[1].length > 2) {
    return num.toFixed(2)
  } else {
    return num
  }
}
const fixBattery = (num: any, voltage: any) => {
  // 转换为数值类型
  const numValue = Number(num);
  const voltageValue = Number(voltage);

  // 处理无效输入
  if (isNaN(numValue) || isNaN(voltageValue)) {
    return 0; // 或抛出错误
  }

  // 处理除零情况
  if (voltageValue === 0) {
    return 0; // 或根据业务逻辑调整
  }

  // 计算百分比
  if (numValue >= voltageValue) {
    return 100;
  } else {
    const percentage = (numValue / voltageValue) * 100;
    return Math.floor(percentage); // 或改用 Math.round
  }
};
const timeFlag = (ts:any) => {
  ts=Number(ts)
  let flag = false
    // 判断ts 跟当前时间相差三十分钟就返回true
    if (ts > (new Date().getTime() - 1800000)) {
      flag = true
    } else {
      flag = false
    }
    console.log(moment(ts).format('YYYY-MM-DD HH:mm:ss'))
  let time = new Date().getTime() - 1800000
    console.log(time,moment(time).format('YYYY-MM-DD HH:mm:ss') , 8522244)
    return flag
}
</script>

<template>
  <view class="main">
    <view class="mainTop">
      <L-top-height></L-top-height>
      <view class="topBox">
        <view class="left">
          <view style="display: flex;align-items: center" @click.prevent="showLeft">
            <text>{{ carInfo?.carModel?.brand }} {{
                carInfo?.carModel?.model
              }}
            </text>
            <image v-if="leftFlag" class="rotate" src="../../static/imgs/car/arrowBottom.svg"></image>
            <image v-else class="icon" src="../../static/imgs/car/arrowBottom.svg"></image>
          </view>
          <view v-if="leftFlag" class="maskSpace" @click="leftFlag=false"></view>
          <view v-if="leftFlag" class="leftMask">
            <view v-for="(item,index) in deviceList"
                  :key="item.id" :class="mainID==item.id?'leftText activeText':'leftText'"
                  @click.stop="emitCarDetail(item.id)">
              {{ item.carModel.brand }}{{ item.carModel.model }}
            </view>
          </view>
        </view>
        <!--            todo  备用    {{item.type==1?'车机':'定位器'}}-->
        <view class="right">
          <image class="icon" src="../../static/imgs/car/info.svg" @tap="toInfo"></image>
          <image class="icon" src="../../static/imgs/car/setting.svg" @tap="toSetting"></image>
        </view>
      </view>
      <view class="carBox">
        <view class="topIcon">
          <image v-if="carInfo.extInfo.signal==1" alt="" class="icon"
                 src="../../static/imgs/index/locationChannelA.svg"/>
          <image v-if="carInfo.extInfo.signal==2" alt="" class="icon"
                 src="../../static/imgs/index/locationChannelB.svg"/>
          <image v-if="carInfo.extInfo.signal==3" alt="" class="icon"
                 src="../../static/imgs/index/locationChannelC.svg"/>
          <image v-if="carInfo.extInfo.signalGps==1" alt="" class="icon"
                 src="../../static/imgs/index/channelA.svg"/>
          <image v-if="carInfo.extInfo.signalGps==2" alt="" class="icon"
                 src="../../static/imgs/index/channelB.svg"/>
          <image v-if="carInfo.extInfo.signalGps==3" alt="" class="icon"
                 src="../../static/imgs/index/channelC.svg"/>
        </view>
        <view class="modelText">
        </view>
        <view class="carMain">
          <view class="carFlex">
            <view class="carDistance">
              <view class="title">电池电压</view>
              <view class="desc">{{ (carInfo.dataPoint.battery / 1000).toFixed(0) }}
                <text>v</text>
              </view>
            </view>
            <view class="carBattery">
              <view class="title">剩余电量</view>
              <left-battery v-if="carInfo.dataPoint.battery>0"
                            :battery="fixBattery(carInfo.dataPoint.battery, carInfo.voltage)"></left-battery>
              <left-battery v-else :battery="0"></left-battery>
            </view>
          </view>
        </view>
        <image :src="carInfo?.carModel?.img" class="carImg" mode="aspectFit"></image>
      </view>

    </view>
  </view>
  <view class="mainPadding" style="padding-bottom: 0">
    <view class="mainFlex">
      <view class="item one">
        <view class="itemTop">
          <view class="title">报警状态</view>
          <view class="desc"></view>
        </view>
        <view v-if="mainCarInfo.dataPoint.alarm ==0" class="hui">无</view>
        <view v-else-if="mainCarInfo.dataPoint.alarm ==1" class="red">车辆震动</view>
        <view v-else-if="mainCarInfo.dataPoint.alarm ==2" class="red">车辆断电</view>
        <view v-else class="hui">
          _
        </view>
      </view>
      <view class="item one">
        <view class="itemTop">
          <view class="title">车辆状态</view>
          <view class="desc"></view>
        </view>
        <view v-if="mainCarInfo.dataPoint.acc ==1&&timeFlag(mainCarInfo.dataPoint.ts)" class="green">行驶中</view>
        <view v-else-if="mainCarInfo.dataPoint.acc ==0" class="blue">驻车中</view>
        <view v-else class="hui">
          _
        </view>
      </view>
      <view class="item">
        <view class="itemTop" @tap="toFence">
          <view class="title">车辆定位</view>
          <view class="desc"></view>
          <L-arrow></L-arrow>
        </view>
        <image class="map" src="../../static/imgs/car/mapCircle.png" @tap="toMapFence"></image>
        <view class="address">{{ address }}</view>
        <view class="time">{{ timeText }}</view>
      </view>
      <view class="item" @tap="toLine">
        <view class="itemTop" @tap="toLine">
          <view class="title">今日骑行</view>
          <L-arrow></L-arrow>
        </view>
        <view class="distance">{{ numberFormat(calcDiv(mainInfo.meter, 1000)) }} km</view>
        <view class="unit" style="text-indent: 3rpx">{{ formatSecondsToHHMMSS(mainInfo.time) }}</view>
        <image v-if="mainInfo.meter >0" class="map" src="../../static/imgs/car/mapLine.png"></image>
        <image v-else class="map" src="../../static/imgs/car/space.png"></image>
      </view>
    </view>
  </view>
  <view class="detailBox" @tap="toLine">
    <view class="mainDetail">
      <view class="detailTop borderTopRadius">
        <view class="label">我的骑行记录</view>
        <view class="content">查看线路</view>
        <L-arrow></L-arrow>
      </view>
      <view class="detailMain borderBottomRadius">
        <view class="item">
          <view class="itemTitle textLeft">{{
              numberFormat(calcAdd(carInfo.totalMeter, mainInfo.meter) / 1000)
            }}km
          </view>
          <view class="itemDesc textLeft">总里程</view>
        </view>
        <view class="item">
          <view class="itemTitle textCenter">
            {{ formatSecondsToHHMMSS(calcAdd(carInfo.totalTime, mainInfo.time)) }}
          </view>
          <view class="itemDesc textCenter">总时长</view>
        </view>
        <view class="item">
          <view class="itemTitle textRight">{{ calcAdd(carInfo.totalNum, mainInfo.count) }}</view>
          <view class="itemDesc textRight">总次数</view>
        </view>
      </view>
    </view>
  </view>
  <view class="mainPadding pb-125" style="padding-bottom: 0">
    <view class="mainFlex">
      <view class="item" @tap="toLineChart">
        <view class="itemTop">
          <view class="title">电池能耗</view>
          <L-arrow></L-arrow>
        </view>
        <view class="distance">{{ consume || '暂无' }}</view>
        <view class="unit">Wh/KM</view>
        <lineChart :consumeList="consumeList"></lineChart>
      </view>
      <view class="item" @tap="toBarChart">
        <view class="itemTop">
          <view class="title">上次充电</view>
          <L-arrow></L-arrow>
        </view>
        <view v-if="carInfo.extInfo.battery>0" class="rate">{{ carInfo.extInfo.battery }}%</view>
        <view v-else class="rate">--%</view>
        <view class="unit">{{ energyTime }}</view>
        <chart :energyList="energyList"></chart>
      </view>
    </view>
  </view>
  <L-speed v-if="startup" :carInfo="mainCarInfo"></L-speed>
</template>

<style lang="scss" scoped>
.startupContent {
  display: flex;
  width: 100%;
  height: 144rpx;
  border-radius: 16rpx;
  align-items: center;
  background: linear-gradient(270deg, #000 0%, #666 100%);

  .startupText {
    margin-left: 24rpx;
    margin-right: 32rpx;
    text-align: center;
    color: var(--Text-white, #FFF);
    font-feature-settings: 'case' on;
    /* Medium/Subheadline */
    font-family: "PingFang SC";
    font-size: 30rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 40rpx; /* 133.333% */
    letter-spacing: -0.24px;
  }

  .startupBgIcon {
    height: 100%;
    flex: 1;
  }
}

.mainPadding {
  padding: 0 32rpx 32rpx;
}

.mainTop {
  padding-bottom: 4rpx;
  flex-shrink: 0;
  background: url('../../static/imgs/car/bg.png') no-repeat;
  background-size: 100% 100%;
}

.textLeft {
  text-align: left;
}

.textCenter {
  text-align: center;
}

.textRight {
  text-align: right;
}

.topBox {
  //position: sticky;
  //top: 0;
  //z-index: 1000;
  display: flex;
  align-items: center;
  padding: 32rpx;
  width: 100%;

  .left {
    flex: 2;
    display: flex;
    align-items: center;
    position: relative;
    color: #333;
    font-family: "PingFang HK";
    font-size: 36rpx;
    font-style: normal;
    font-weight: 600;
    line-height: normal;

    .icon {
      width: 26rpx;
      height: 13rpx;
      flex-shrink: 0;
      margin-left: 20rpx;
    }

    .rotate {
      width: 26rpx;
      height: 13rpx;
      flex-shrink: 0;
      margin-left: 20rpx;
      transform: rotate(180deg);
    }

    .maskSpace {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 10;
      background: transparent;
    }

    .leftMask {
      position: absolute;
      top: 60rpx;
      width: 164px;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 16rpx 32rpx;
      z-index: 12;

      :after {
        content: '';
        position: absolute;
        left: 20rpx;
        width: 0;
        height: 0;
        top: -5rpx;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-bottom: 10rpx solid #fff; /* 三角形的颜色 */
        transform: translateY(-50%);
      }

      .leftText {
        padding: 16rpx 0rpx;
        color: #4F4F4F;
        text-align: center;
        font-family: "PingFang HK";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 24rpx; /* 100% */
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .activeText {
        color: #4B85FD;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;

    .icon {
      width: 36rpx;
      height: 36rpx;
      flex-shrink: 0;
      margin-left: 24rpx;
    }
  }
}

.carBox {
  padding: 32rpx;
  width: 100%;
  position: relative;

  .modelText {
    margin-top: 82rpx;
    margin-bottom: 120rpx;
    width: 686rpx;
    height: 88rpx;
    text-align: center;
    color: #525D71;
    text-align: center;
    font-feature-settings: 'case' on;
    font-family: "Zen Dots";
    font-size: 70rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 44px */
    letter-spacing: -0.28px;
  }

  .carMain {
    height: 300rpx;
    background: #FFFFFF;
    padding: 24rpx 40rpx 24rpx;
    display: flex;
    align-items: flex-end;
    border-radius: 24rpx;

    .carFlex {
      width: 100%;
      height: 150rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .carDistance {
        .title {
          color: var(--Text-tips, #909399);
          font-feature-settings: 'case' on;
          font-family: var(--Font-Family, "PingFang SC");
          font-size: var(--Font-Size-Caption2, 22rpx);
          font-style: normal;
          font-weight: 400;
          line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
          letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
          margin-bottom: 10rpx;
        }

        .desc {
          color: #000;
          font-family: "DIN Alternate";
          font-size: 72rpx;
          font-style: normal;
          font-weight: 700;
          line-height: 100%; /* 36px */
          letter-spacing: 0.36px;

          text {
            color: #000;
            font-family: "DIN Alternate";
            font-size: 46rpx;
            font-style: normal;
            font-weight: 700;
            line-height: 130%; /* 23.4px */
            letter-spacing: 0.18px;
		display: inline-block;
			padding-bottom: 2px!important;
          }
        }

      }

      .carBattery {
        .title {
          color: var(--Text-tips, #909399);
          font-feature-settings: 'case' on;
          font-family: var(--Font-Family, "PingFang SC");
          font-size: var(--Font-Size-Caption2, 22rpx);
          font-style: normal;
          font-weight: 400;
          line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
          letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
          margin-bottom: 10rpx;
        }
      }
    }
  }

  .carItem {

  }

  .topIcon {
    display: flex;
    justify-content: flex-start;

    .icon {
      width: 36rpx;
      height: 36rpx;
      flex-shrink: 0;
      margin-right: 24rpx;
    }

    .mainStatus {
      display: flex;
      flex: 1;
      justify-content: flex-end;
    }

    .status {
      height: 48rpx;
      padding: 6rpx 16rpx;
      align-items: center;
      flex-shrink: 0;
      border-radius: 8rpx;
      font-family: var(--Font-Family, "PingFang SC");
      font-size: var(--Font-Size-Caption1, 24rpx);
      font-style: normal;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .stop {
      border: 1px solid rgba(127, 223, 128, 0.20);
      background: #F1FBF3;
      color: #41D143;
    }

    .run {
      border: 1px solid rgba(42, 118, 240, 0.20);
      background: #DEF1FC;
      color: #2A76F0;
    }

    .fail {
      border: 1px solid var(--Border-4, #E6E6E8);
      background: var(--Border-3, #D9DBDE);
      color: var(--Text-content, #53565C);
    }

    .blue {
      border: 1px solid rgba(255, 167, 57, 0.20);
      background: #FFF2DE;
      color: #FFA739;
      margin-right: 24rpx;
    }

    .blueStop {
      border: 1px solid var(--Border-4, #E6E6E8);
      background: var(--Border-3, #D9DBDE);
      color: var(--Text-content, #53565C);
      margin-right: 24rpx;

    }

    .voltage {
      color: #1AB9BF;
      border: 1px solid rgba(26, 185, 191, 0.20);
      background: rgba(26, 185, 191, 0.10);
      margin-right: 24rpx;
    }


    .blueIcon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }

  .carImg {
    position: absolute;
    top: 80rpx;
    left: 61rpx;
    width: 564rpx;
    height: 446rpx;
    margin: 0 61rpx;
  }
}

.btnBox {
  padding: 0 32rpx 32rpx;
  display: flex;
  justify-content: space-around;

  .btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .btnItem {
      display: flex;
      width: 112rpx;
      height: 112rpx;
      border-radius: 56rpx;
      background: #3c9cff;
      justify-content: center;
      align-items: center;

      .btnIcon {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .black {
      background: var(--Background-black, #000);
    }

    .white {
      background: var(--Background-white-black, #FFF);
    }

    .btnText {
      width: 100%;
      color: var(--Text-main, #282A2E);
      text-align: center;
      /* Medium/Caption1 */
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx; /* 133.333% */
      margin-top: 12rpx;
    }
  }
}

.mainFlex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .item {
    display: flex;
    width: 330rpx;
    height: 446rpx;
    padding: 32rpx 24rpx;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
    border-radius: 24rpx;
    background: #FFF;
    box-shadow: 0px 2px 24px 0px rgba(0, 0, 0, 0.04);
    margin-bottom: 24rpx;

    .itemTop {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .title {
        color: var(--Text-main, #282A2E);
        /* Regular/Footnote */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Footnote, 26rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
        letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
      }

      .desc {
        color: var(--Text-tips, #909399);
        text-align: right;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption1, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Caption1, 16px); /* 133.333% */
        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        flex: 1;
        text-align: right;
        margin-right: 10rpx;
      }
    }

    .map {
      width: 284rpx;
      height: 210rpx;
    }

    .address {
      color: var(--Text-tips, #909399);
      margin-bottom: 8rpx;
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx; /* 133.333% */
      min-height: 64rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 16rpx;
    }

    .time {
      color: var(--Text-main, #282A2E);
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx; /* 133.333% */
    }

    .distance {
      color: #000;
      margin-bottom: 8rpx;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx; /* 127.273% */
      letter-spacing: 0.35px;
      margin-top: 16rpx;
    }

    .unit {
      color: var(--Text-tips, #909399);
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx; /* 133.333% */
      margin-bottom: 8rpx;
    }

    .rate {
      margin-bottom: 8rpx;
      color: var(--Primary-text, #3C9CFF);
      margin-top: 16rpx;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx; /* 127.273% */
      letter-spacing: 0.35px;
    }

  }

  .one {
    height: 180rpx !important;

    .blue {
      color: var(--Primary-text, #3C9CFF);
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
    }

    .red {
      color: #FF7070;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
    }

    .green {
      color: #5AC725;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
    }

    .hui {
      color: #666666;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
    }

    .descText {
      color: var(--Text-tips, #909399);
      /* Medium/Caption1 */
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx; /* 133.333% */
      margin-top: 16rpx;
    }
  }
}

.pb-125 {
  padding-bottom: 125rpx;
}

.detailBox {
  padding: 0 32rpx 32rpx;
  border-radius: 24rpx;

  .mainDetail {
    box-shadow: 0px 2px 24px 0px rgba(0, 0, 0, 0.04);
  }

  .mainDesc {
    color: #000;
    /* Regular/Footnote */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Footnote, 26rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
    letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
    background: #FFFFFF;
    padding: 32rpx;
  }

  .detailTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #FFFFFF;
    padding: 32rpx;

    .label {
      color: var(--Text-content, #53565C);
      font-family: var(--Font-Family, "PingFang SC");
      font-size: var(--Font-Size-Footnote, 26rpx);
      font-style: normal;
      font-weight: 400;
      line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
      letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
    }

    .content {
      flex: 1;
      margin-right: 10rpx;
      color: var(--Text-tips, #909399);
      text-align: right;
      font-family: var(--Font-Family, "PingFang SC");
      font-size: var(--Font-Size-Caption1, 24rpx);
      font-style: normal;
      font-weight: 400;
      line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
      letter-spacing: var(--Letter-Spacing-Caption1, 0px);
    }
  }

  .detailMain {
    padding: 0 32rpx 0;
    display: flex;
    background: #FFFFFF;

    .item {
      flex: 1;

      .itemTitle {
        color: #000;
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx;
        letter-spacing: -0.408px;

      }

      .itemDesc {
        color: var(--Text-content, #53565C);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Footnote, 26rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
        letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        margin-bottom: 32rpx;
        margin-top: 32rpx;
      }
    }
  }
}


</style>
