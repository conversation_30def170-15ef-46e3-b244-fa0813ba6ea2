<template>
    <view style="background: #f5f5f5;min-height: 95vh;padding: 36rpx;box-sizing: border-box;">
        <view class="goodInfo borderTopRadius">
            <view class="goodBox">
                <view style="display: flex; justify-content: space-between">
                    <view class="goodImg">
                        <image :src="goodInfo.picture" alt="" class="imgItem"/>
                    </view>
                    <view class="flex-1">
                        <view class="goodName">{{ goodInfo.name }}</view>
                        <view class="goodSku">
                            <view v-for="info in goodInfo.specs" class="spec">{{ info.name }}: {{ info.value }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="goodInfo">
            <view class="formBox">
                <textarea id="" v-model="refundForm.content" :readonly="goodInfo.goodsReviewId != null" name=""
                          placeholder="添加图文评价，帮助更多想买的人～"></textarea>
                <htz-image-upload
                    v-if="goodInfo.goodsReviewId == null"
                    v-model="refundForm.imgs"
                    :max="5"
                    action="https://assets.cutold.com/v1/imgs/upload"
                    name="photos"
                    @uploadSuccess="handleSuccess"
                ></htz-image-upload>
            </view>
        </view>
        <view class="rateBox">
            <view class="rateMain">
                <view class="rateLeft">物流评价</view>
            </view>
            <view class="rateInfo">
                <view class="rateLeft">
                    <uni-rate
                        :readonly="goodInfo.goodsReviewId != null"
                        :size="18"
                        :value="refundForm.goodsRating"
                        active-color="#1c432b"
                        allow-half
                    />
                </view>
            </view>
        </view>
        <view class="rateBox">
            <view class="rateMain">
                <view class="rateLeft">物流评价</view>
            </view>
            <view class="rateInfo">
                <view class="rateLeft">
                    <uni-rate
                        :readonly="goodInfo.goodsReviewId != null"
                        :size="18"
                        :value="refundForm.trackRating"
                        active-color="#1c432b"
                        allow-half
                    />
                </view>
            </view>
        </view>
        <view class="rateBox borderBottomRadius">
            <view class="rateMain">
                <view class="rateRight">服务评价</view>
            </view>
            <view class="rateInfo">
                <view class="rateRight">
                    <uni-rate
                        :readonly="goodInfo.goodsReviewId != null"
                        :size="18"
                        :value="refundForm.serviceRating"
                        active-color="#1c432b"
                        allow-half
                    />
                </view>
            </view>
        </view>
        <view v-if="goodInfo.goodsReviewId == null" class="btnBox">
            <view class="btnItem">
                <view class="btn" @click="submitFormRefund">提交</view>
            </view>
        </view>
    </view>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {getOrderReviewAPI, getOrderReviewDetailAPI} from '@/services/order'

onMounted(() => {
    resetData()
})

let info = uni.getStorageSync('reviewGood')
info = JSON.parse(info)
const goodInfo = ref({})
let refundForm = reactive({
    content: '',
    imgs: [],
    trackRating: 4,
    serviceRating: 3,
    goodsRating: 5,
    orderGoodsId: '',
})
const resetData = async () => {
    if (info.goodsReviewId) {
        let infoObj = await getOrderReviewDetailAPI(info.goodsReviewId)
        let data = infoObj.result
        console.log(data)
        let {content, imgs, trackRating, serviceRating, goodsRating} = data
        for (let i in imgs) {
            imgs[i] = {url: imgs[i], status: 'done'}
        }
        form.imgList = imgs
        refundForm.content = content
        refundForm.imgs = imgs
        refundForm.trackRating = trackRating
        refundForm.serviceRating = serviceRating
        refundForm.goodsRating = goodsRating
    } else {
        goodInfo.value = info
    }
}
goodInfo.value = info
let form = reactive({
    imgList: [],
})
const handleSuccess = (res) => {
    refundForm.imgs.push(res)
}


const submitFormRefund = async () => {
    refundForm.orderGoodsId = goodInfo.value.id
    let res = await getOrderReviewAPI(refundForm)
    if (res.msg == 'success') {
        uni.navigateBack({delta: 1})
    }
}
</script>

<style lang="scss" scoped>
.btnBox {
    padding-top: 24rpx;
    background: #FFFFFF;
    box-sizing: border-box;

    .btnItem {
        padding: 24rpx;
        width: 100%;
        border-top: 1px solid #ededed;
        display: flex;
        justify-content: flex-end;
        box-sizing: border-box;
    }

    .btn {
        display: flex;
        width: 176rpx;
        height: 72rpx;
        justify-content: center;
        align-items: center;
        gap: 16rpx;
        flex-shrink: 0;
        border-radius: 16rpx;
        background: var(--Brand-Green-Primary, #3C9CFF);
        color: var(--Normal-White, #FFF);
        text-align: center;
        font-family: "PingFang HK";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 28px */
    }
}

.rateBox {
    width: 100%;
    background: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80rpx;
    padding: 24rpx;

    .rateMain {
        height: 100%;
        display: flex;
        align-items: center;
        border-radius: 8rpx 8rpx 0px 0px;
        color: #333;
        font-family: 'PingFang HK';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 21px */
        .rateLeft {
            flex: 1;
        }
        .rateRight {
            flex: 1;
        }
    }

    .rateInfo {
        display: flex;
        align-items: center;
        .rateLeft {
            flex: 1;
        }
        .rateRight {
            flex: 1;
        }
    }
}

.goodInfo {
    background: #FFFFFF;
    padding: 36rpx 24rpx 0;
}

.goodTitle {
    width: 100%;
    background: #fff;
    padding: 24rpx 24rpx 0;
    box-sizing: border-box;

    .goodMain {
        height: 21rpx;
        display: flex;
        align-items: center;
        border-radius: 8rpx 8rpx 0px 0rpx;
        color: #333;
        font-family: 'PingFang HK';
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 21px */
        padding-bottom: 28rpx;
        border-bottom: 1rpx dotted #666;
    }
}

.goodBox {
    border-radius: 0px 0px 8rpx 8rpx;
    background: #fff;
    display: flex;
    flex-direction: column;

    .goodImg {
        width: 80rpx;
        height: 80rpx;

        .imgItem {
            width: 80rpx;
            height: 80rpx;
        }
    }

    .goodName {
        color: var(--Normal-Black-333, #333);
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 24px */
        margin-bottom: 10rpx;
    }

    .goodSku {
        color: var(--Normal-Black-666, #666);
        font-family: "PingFang SC";
        font-size: 22rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 22px */
    }

    .flex-1 {
        flex: 1;
        margin-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10rpx 0;
    }

    .goodPrice {
        color: #333;
        font-family: 'PingFang HK';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 100%; /* 20px */
        display: flex;
        justify-content: flex-end;
        margin-top: 24rpx;
    }
}

.formBox textarea {
    background: #fff;
    padding: 24rpx;
    font-size: 28rpx;
}

.formBox textarea {
    display: flex;
    height: 128rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 24rpx;
    flex-shrink: 0;
    border-radius: 8rpx;
    background: var(--Backgroung-Background-Primary, #F9F9F9);
    color: var(--Normal-Gray-999, #999);
    font-family: "PingFang HK";
    font-size: 20rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 30px */
}
</style>
