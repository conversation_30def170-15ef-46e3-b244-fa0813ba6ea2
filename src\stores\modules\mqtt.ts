import { defineStore } from 'pinia'
import { ref } from 'vue'
import mqtt from "mqtt";

export const useMqttStore = defineStore('mqttStore', () => {
    const client = ref<any>()
    const options:any = ref({})
    const disconnect = () => {
        if (client.value) {
            client.value.end();
            console.log('MQTT 已断开');
        }
    }
    const subscribe = (subscribeUrl:any) => {
        if(!client.value){
            return
        }
        client.value.subscribe(subscribeUrl, function(err:any) {
            if (!err) {
                uni.showToast({
                    title:"订阅成功",
                    duration:2000,
                    icon:"none"
                })
            }
        })
    }
    const unSubscribe = (subscribeUrl:any) => {
        if(!client.value){
            return
        }
        client.value.unsubscribe(subscribeUrl, function(err:any) {
            if (!err) {
                uni.showToast({
                    title:"订阅成功",
                    duration:2000,
                    icon:"none"
                })
            }
        })
    }
    const connect = () => {
        options.value = {
            clientId:"H5_test"+Date.now(),
            connectTimeout: 600000,
            clean: true,
			username:'client',
			password:'client123'
        }
        // 此处的端口为mqtt.js中的http端口，否则H5和手机连接不上mqtt服务器
        client.value = mqtt.connect('ws://mqtt.evl.usr.86.ltd:8083/mqtt',options.value)
        client.value.on('connect', function(res:any) {
            uni.showToast({
                title:"连接成功",
                duration:2000,
                icon:"none"
            })
        }).on('message', function(topic:any, message:any) {
            console.log(JSON.parse(message.toString()))
        }).on('reconnect', function() {
            console.log("重连")
        }).on('error', function(error:any) {
            console.log("error",error)
        })
    }

    return {
        client,
        options,
        connect,
        disconnect,
        subscribe,
        unSubscribe
    }
})


