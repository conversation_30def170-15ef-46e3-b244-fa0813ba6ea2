<script lang="ts" setup>
import {onMounted} from 'vue'

onMounted(
    () => {
    }
)
const scanCode = () => {
    uni.makePhoneCall({
        phoneNumber: '************'
    })
}
</script>

<template>
    <view class="">
        <view class="carBox">
            <view class="carMain">
                <view class="title">月租轻松骑</view>
                <view class="desc">雅迪G5</view>
                <view class="timeAll">
                    <view class="timeDesc">租车剩余时长</view>
                    <view class="time">26天15小时</view>
                </view>
                <view class="btnBox">
                    <view class="btn">我要续租</view>
                    <view class="btn">我要还车</view>
                </view>
            </view>
            <view class="carImg">
                <image src="./imgs/car.png"></image>
            </view>

        </view>
    </view>
    <view class="orderBox">
        <view class="tip">温馨提示：此数值与实际数值有偏差，请以最终订单信息为准有问题，请咨询客户热线</view>
        <view class="orderMain">
            <view class="rightBoxMain">
                <u-button type="primary" @click="scanCode">客服热线</u-button>
            </view>
        </view>
    </view>
</template>

<style lang="scss">
.carBox {
    width: 686rpx;
    height: 236rpx;
    flex-shrink: 0;
    padding: 24rpx;
    margin: 32rpx;
    background: url('./imgs/bg.png')lightgray 50% / cover no-repeat;
    display: flex;
    justify-content: space-between;
    border-radius: 16rpx;
    .carMain{
width: 380rpx;
        .title{
            color: var(--Text-main, #282A2E);
            font-feature-settings: 'case' on;

            /* Medium/Subheadline */
            font-family: "PingFang SC";
            font-size: 30rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 40rpx; /* 133.333% */
            letter-spacing: -0.24px;
            margin-bottom: 10rpx;
        }
        .desc{
            color: #53565C;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height:  24rpx; /* 100% */
            margin-bottom: 32rpx;
        }
        .timeAll{
            display: flex;
            align-items: flex-end;
            .timeDesc   {
                color: #53565C;
                font-family: "PingFang SC";
                font-size:  24rpx;
                font-style: normal;
                font-weight: 400;
                line-height:  24rpx; /* 100% */
                margin-right: 20rpx;
            }
            .time{
                color: #F00;
                font-family: "PingFang SC";
                font-size: 40rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 24rpx; /* 60% */
                .unit{
                    color: #F00;
                    font-family: "PingFang SC";
                    font-size: 24rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 24rpx;
                }
            }
        }
        .btnBox{
            display: flex;
            justify-content: space-between;
            margin-top: 24rpx;
            .btn{
                width: 180rpx;
                height: 40rpx;
                flex-shrink: 0;
                border-radius: 20rpx;
                background: #3C9CFF;
                color: #FFF;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
    .carImg{
        width: 260rpx;
        display: flex;
        justify-content: flex-end;
        image{
            width: 240rpx;
            height: 180rpx;
        }
    }
}
.tip{
    width: 686rpx;
    margin: 32rpx 32rpx 0;
    padding: 24rpx;
    color: var(--Text-tips, #909399);
    text-align: center;
    font-feature-settings: 'case' on;
    /* Regular/Caption2 */
    font-family: var(--Font-Family, "PingFang SC");
    font-size: var(--Font-Size-Caption2, 22rpx);
    font-style: normal;
    font-weight: 400;
    line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
    letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
}
.orderBox{
    position: fixed;
    bottom: 0;
    box-shadow: 0px 4rpx 4rpx 0px rgba(0, 0, 0, 0.25);
}
.orderMain {
    background: #FFF;
    width: 750rpx;
    height: 130rpx;
    flex-shrink: 0;

    display: flex;

    .leftBox {
        flex: 1;
        padding: 0 32rpx;

        .price {
            color: #F00;
            font-family: "PingFang SC";
            font-size: 40rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx;
            margin-bottom: 20rpx;
            margin-top: 40rpx;

            .unit {
                color: #F00;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 24rpx; /* 100% */
            }
        }

        .priceDesc {
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 100% */
        }
    }


    .rightBoxMain {
        width: 100%;
        padding: 24rpx 32rpx;
        flex-shrink: 0;
    }

}
</style>
