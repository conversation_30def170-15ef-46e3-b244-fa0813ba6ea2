<script lang="ts" setup>
import {onMounted, onUnmounted, ref} from 'vue';
import {onShow} from "@dcloudio/uni-app";
const countdown = ref(20);

let intervalId: number | null = null;

onShow(() => {
    countdown.value = 20
    intervalId = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            uni.showToast({
                title: '未搜索到设备',
                icon: 'none'
            })
            clearInterval(intervalId!);
            uni.navigateTo({
                url: '/pages/addDevice/blueTooth',
            })
        }
    }, 1000);
});

onUnmounted(() => {
    if (intervalId !== null) {
        clearInterval(intervalId);
    }
});
</script>

<template>
    <view class="main">
        <view class="top">
            正在搜索附近设备({{countdown}}s)
        </view>
        <view class="imgBox">
            <image class="loadingImg" src="../../static/imgs/addDevice/loading.png"></image>
        </view>
        <view class="bottom"></view>
    </view>

</template>

<style lang="scss" scoped>
.main {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .top {
        padding: 32rpx;
        color: var(--Text-main, #282A2E);
        /* Medium/Title1 */
        font-family: "PingFang SC";
        font-size: 56rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 68rpx; /* 121.429% */
        letter-spacing: 0.364px;
    }

    .imgBox {
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .loadingImg {
            display: block;
            width: 144rpx;
            height: 144rpx;
            animation: rotate360 500ms linear infinite; /* 2秒完成一次旋转，线性过渡，无限循环 */
        }

    }
}


</style>