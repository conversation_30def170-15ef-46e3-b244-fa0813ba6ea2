<template>
    <view class="mainBox">
        <view class="imgBox">
            <image class="logo" src="../../static/imgs/mine/logo.png"></image>
            <view class="appName">骑行助手</view>
            <view class="appDesc">v{{ versionText }}</view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="checkUpdate">检查更新
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius " @click="toPage('/pages/mit/mit')">软件许可和服务协议
                <L-arrow></L-arrow>
            </view>
            <view class="listItem borderBottomRadius" @click="toPage('/pages/mit/policy')">隐私政策
                <L-arrow></L-arrow>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getVersion} from "@/services/version";
const versionText = ref("")
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    });
};


const updateApp = (versionData: any) => {
  if (versionData.isWgt == 'true') {
        uni.showLoading({
            title: '正在下载',
            mask: true,
        })
        uni.downloadFile({
            url: versionData.url,
            success: downloadResult => {
                if (downloadResult.statusCode === 200) {
                    uni.showLoading({
                        title: '安装中..',
                        mask: true,
                    })
                    plus.runtime.install(
                        downloadResult.tempFilePath, {
                            force: true,
                        },
                        function () {
                            console.log('install success...')
                            uni.hideLoading()
                            plus.runtime.restart()
                        },
                        function (e) {
                            console.log(e)
                            uni.hideLoading()
                            uni.showToast({
                                title: e.message || '安装失败',
                                duration: 1500,
                                icon: 'none'
                            })
                        },
                    )
                } else {
                    uni.hideLoading()
                    uni.showToast({
                        title: '下载失败',
                        duration: 1500,
                        icon: 'none'
                    })
                }
            },
            fail(e) {
                uni.hideLoading()
                uni.showToast({
                    title: e.message || '下载失败',
                    duration: 1500,
                    icon: 'none'
                })
            },
        })
        return;
    }
    const systemInfo = uni.getSystemInfoSync()
    const downloadUrl =
        systemInfo.platform == 'android' ?
            versionData.url :
            versionData.url2
    plus.runtime.openURL(downloadUrl)
}
const checkUpdate = () => {
    plus.runtime.getProperty(plus.runtime.appid, async widgetInfo => {
        let res = await getVersion()
        let version = widgetInfo.versionCode
        if (res.msg == 'success') {
            let versionInfo = res.result
			console.log(versionInfo.v > version,versionInfo.v , version)
			if(versionInfo.v > version){
				if (versionInfo.isWgt === 'true') {
				    updateApp(versionInfo)
				} else {
          uni.showModal({
				        title: '新版本发布',
				        content: '检查到有新版本,需要更新吗？',
				        confirmText: '立即更新',
				        showCancel: false,
				        success: res => {
				            if (res.confirm) {
				                updateApp(versionInfo)
				            }
				        },
				    })
				}
			}else{
                uni.showToast({
                    title: '当前为最新版本',
                    duration: 1500,
                    icon: 'none'
                })
            }
        }
    })
}
plus.runtime.getProperty(plus.runtime.appid, async widgetInfo => {
    versionText.value = widgetInfo.version
})
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.imgBox {
    padding: 76rpx 278rpx 24rpx;

    .logo {
        width: 194rpx;
        height: 194rpx;
        flex-shrink: 0;
        margin-bottom: 22rpx;
    }

    .appName {
        color: var(--Text-main, #282A2E);
        text-align: center;
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: -0.24px;
        margin-bottom: 14rpx;
    }

    .appDesc {
        color: var(--Text-content, #53565C);
        text-align: center;
        font-family: "PingFang HK";
        font-size: 16.455px;
        font-style: normal;
        font-weight: 400;
        line-height: 24.682px;
    }
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }
}
</style>
