<script lang="ts" setup>
import LddSwiper from '@/components/ldd-swiper/ldd-swiper.vue'
import {ref,onMounted} from 'vue'
// 定义 props 接收数据
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    }
})
const clickItem = (id: number) => {
    uni.navigateTo({
        url: '/pages/shop/sort?category='+id
    })
}
const swiperList: any = ref([])
onMounted(()=>{
    if(props.list){
        swiperList.value = props.list
    }
})
</script>

<template>
    <view class="category">
        <LddSwiper :swiperList="swiperList" @clickItem="clickItem"></LddSwiper>
    </view>
</template>

<style lang="scss">
.category{
    margin-top: 20rpx;
}
</style>
