<script lang="ts" setup>
import {ref,onMounted} from 'vue'
const activeIndex = ref(0)

// 当 swiper 下标发生变化时触发
const onChange = (ev: any) => {
    activeIndex.value = ev.detail.current
}
const props = defineProps({
    list: {
        type: Array,
        default: () => [ ]
    }
})
const bannerList:any = ref([])
onMounted(()=>{
    if(props.list){
        bannerList.value = props.list
    }
})
</script>

<template>
    <view class="carousel">
        <swiper :autoplay="false" :circular="true" :interval="3000" @change="onChange" style="height: 280rpx;">
            <swiper-item v-for="item in bannerList" :key="item.id" style="height: 264rpx;">
                <navigator :url="item.hrefUrl" class="navigator" hover-class="none">
                    <image :src="item.imgUrl" class="image"></image>
                </navigator>
            </swiper-item>
        </swiper>
        <!-- 指示点 -->
        <view class="indicator" v-if="false">
            <text
                v-for="(item, index) in bannerList"
                :key="item.id"
                :class="{ active: index === activeIndex }"
                class="dot"
            ></text>
        </view>
    </view>
</template>

<style lang="scss">
@use '../styles/XtxSwiper';
</style>
