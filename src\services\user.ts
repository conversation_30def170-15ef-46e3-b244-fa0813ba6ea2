import { http } from '@/utils/http'
// 获取用户信息
export const getUserInfo = (): any => {
    return http({
        url: '/api/v1/me/user',
        method: 'GET',
    })
}
// 修改用户信息
export const modifyUser = (params: any): any => {
    return http({
        url: '/api/v1/me/user',
        method: 'PUT',
        data: params,
    })
}
// 修改手机号
export const modifyPhone = (params: any): any => {
    return http({
        url: '/api/v1/me/changeTel',
        method: 'PUT',
        data: params,
    })
}

