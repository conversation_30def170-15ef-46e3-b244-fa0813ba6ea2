<script setup>
import { onMounted, ref } from 'vue'
import { getGoodsReviewAPI } from '@/services/goods'
import moment from 'moment'
import {calcAdd} from '@/composables/math'

const list = ref([])
// 接受父组件参数
const props = defineProps({
  goods: Object,
})
let commentInfo = ref({})
onMounted(() => {
  resetData()
})
const resetData = async () => {
  console.log(props)
  let res = await getGoodsReviewAPI({ goodsId: props.goods.id, page: 1, size: 1000 })
  if (res.msg === 'success') {
    let arr = res.result.rows
    let scoreA = 0
    for (let i in arr) {
      scoreA = calcAdd(scoreA, arr[i].goodsRating)
    }
    console.log(scoreA)
    list.value = res.result.rows
    commentInfo.value.count = res.result.count
    if (scoreA == 0) {
      commentInfo.value.goodsRating = 0
    } else {
      commentInfo.value.goodsRating = (scoreA / res.result.count).toFixed(2)
    }
    console.log(commentInfo.value)
  }
  console.log(res, 'comment')
}
const formatDate = (date) => {
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}
</script>
<template>
  <view>
    <view className="commentHeader">
      <view className="commentScore">
        {{ commentInfo?.goodsRating || 0 }}
      </view>
      <view className="commentScoreBox" style="flex-direction: column; align-items: flex-start">
        <!-- 设置颜色 -->
        <uni-rate :size="18" :value="commentInfo.goodsRating" active-color="#1c432b" allow-half readonly />
        <view className="commentDesc">共{{ commentInfo?.count || 0 }} 条评价</view>
      </view>
    </view>
    <view v-for="(item, index) in list" :key="index" class="reviewComment">
      <view class="reviewInfo">
        <view class="reviewName">{{ item.fname || item.member.nickname }}</view>
        <view class="reviewTime">{{ formatDate(item.createTime) }}</view>
      </view>
      <view class="reviewContent">{{ item.content }}</view>
      <view class="reviewImgList">
        <view v-for="item in item.imgs" :key="item" class="reviewImg">
          <image :src="item" mode="aspectFill" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>


.commentHeader {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.commentScore {
  color: var(--Normal-Black-333, #333);
  font-family: "PingFang HK";
  font-size: 56rpx;
  font-style: normal;
  font-weight: 600;
  line-height: 100%; /* 56px */
  margin-right: 18rpx;
}

.score {
  width: 100%;
  margin-top: 24rpx;
}

.commentScoreBox {
  width: 100%;
  display: flex;
  align-items: center;
}

.commentDesc {
  color: var(--Normal-Gray-999, #999);
  font-family: "PingFang SC";
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 100%; /* 24px */
}








.reviewComment {
  .reviewInfo {
    padding-top: 40rpx;
    display: flex;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .reviewName {
      color: var(--Brand-Green-Primary, #3C9CFF);
      font-family: 'PingFang SC';
      font-size: 24rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 24px */
    }

    .reviewTime {
      color: var(--Normal-Gray-999, #999);
      font-family: 'PingFang SC';
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 100%; /* 24px */
    }
  }

  .reviewContent {
    color: var(--Normal-Black-333, #333);
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 30rpx;
    margin-bottom: 20rpx;
  }

  .reviewImgList {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 40rpx;
    border-bottom: 1px solid #ededed;

    .reviewImg {
      width: 160rpx;
      height: 160rpx;
      flex-shrink: 0;
      align-self: stretch;
      border-radius: 4px;
      border: 2px solid var(--Border-Border-Primary, #ededed);

      image {
        width: 160rpx;
        height: 160rpx;
      }
    }
  }
}
</style>
