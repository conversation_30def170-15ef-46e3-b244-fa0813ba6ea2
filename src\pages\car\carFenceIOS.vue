<template>
    <view :style="{ width: '750rpx', height: windowHeight + 'px' }">
        <map :circles="circles" :latitude="centerInfo.latitude" :longitude="centerInfo.longitude" :markers="markers"
             :style="{ width: '750rpx', height: windowHeight + 'px' }">
            <cover-view :class="[isAndroid?'iconMain ios':'iconMain ios']">
                <cover-view class="flexBox">
                    <cover-view class="iconBox">
                        <cover-image class="icon" mode="widthFix"
                                     src="../../static/imgs/car/motor_location.png" @tap="centerCar"></cover-image>
                    </cover-view>
                    <cover-view class="iconBox">
                        <cover-image class="icon" mode="widthFix" src="../../static/imgs/car/refresh.png"
                                     @tap="resetData"></cover-image>
                    </cover-view>
                    <cover-view class="flex-1"></cover-view>
                    <cover-view class="iconNav">
                        <cover-image class="icon" mode="widthFix" src="../../static/imgs/car/nav.png"
                                     @tap="navLine"></cover-image>
                    </cover-view>
                </cover-view>
                <cover-view class="whiteBox">
                    <cover-view class="title">{{ carInfo.carModel.brand }} {{
                            carInfo.carModel.model
                        }}
                    </cover-view>
                    <cover-view class="time">上报时间：{{ formatSecondsToHHMMSS(carInfo.lastPoint.ts * 1000) }}
                    </cover-view>
                    <cover-view class="border"></cover-view>
                    <cover-view class="location">
                        <cover-view class="locationTitle"> 车辆位置：
                        </cover-view>
                        <cover-view class="text">{{ address }}
                        </cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
    </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {onLoad} from "@dcloudio/uni-app";
import {getCarInfo} from "@/services/device";
import {gps2AMap} from "@/utils/location";
import moment from "moment";

const props = defineProps({
    deviceId: {
        type: Number || String,
        default: 0
    }
})
let windowHeight = uni.getSystemInfoSync().windowHeight;

const carInfo: any = ref({})
const centerInfo: any = ref({})
const circleInfo: any = ref({})
const carLocation: any = ref({})
const circles: any = ref([])
const markers: any = ref([])
const markerList: any = ref([{}])

const formatSecondsToHHMMSS = (time: any) => {
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
onLoad(() => {
    resetData()
})
const resetData = async () => {
    let res = await getCarInfo(props.deviceId)
    if (res.msg == 'success') {
        carInfo.value = res.result
        if (res.result.lastPoint) {
            let result = gps2AMap(res.result.lastPoint.lng, res.result.lastPoint.lat)
            let lat = result[1]
            let lng = result[0]
            carLocation.value = {
                longitude: lng,
                latitude: lat
            }
            centerInfo.value = {
                longitude: lng,
                latitude: lat
            }
            await getAddress(lat, lng);
            circles.value = [{
                latitude: lat,
                longitude: lng,
                fillColor: '#4B85FD1A',
                radius: 80,
                strokeWidth: 2,
                color: '#4B85FD26'
            }]
            markerList.value[0] = {
                latitude: lat,
                longitude: lng,
                id: "car",
                iconPath: '../../static/imgs/car/location-car.png',
                width: 30,
                height: 30
            }
            markers.value = JSON.parse(JSON.stringify(markerList.value))
        }
    }
}
const centerCar = () => {
    carLocation.value.longitude = carLocation.value.longitude + 0.00000000000001
    centerInfo.value = carLocation.value
}
const address = ref('')
const getAddress = async (lat: number, lng: number) => {
    const apiKey = 'a3d415243ae2185eb0f16452497e3867'; // 替换为你的高德地图API Key
    address.value = '地址获取中';
    const formatted_address = uni.getStorageSync(lat + '-' + lng)
    if (formatted_address) {
        address.value = formatted_address
        return
    }
    uni.request({
        url: `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${lng},${lat}&key=${apiKey}&radius=1000&extensions=all `,
        methods: "GET",//仅为示例，并非真实接口地址。
        success: (res: any) => {
            uni.setStorageSync(lat + '-' + lng, res.data.regeocode.formatted_address)
            address.value = res.data.regeocode.formatted_address;
        },
        fail: (err: any) => {
            console.log(err)
        }
    });
};
const navLine = () => {
    let latitude = centerInfo.value.latitude;
    let longitude = centerInfo.value.longitude;
    let name = address.value
    let url = "";
    if (plus.os.name == "Android") {//判断是安卓端
        plus.nativeUI.actionSheet({//选择菜单
            title: "选择地图应用",
            cancel: "取消",
            buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
        }, function (e) {
            switch (e.index) {
                case 1:
                    url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                    break;
                case 2:
                    url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&coord_type=gcj02&src=andr.baidu.openAPIdemo`;
                    break;
                case 3:
                    url = `androidamap://viewMap?sourceApplication=appname&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                    break;
                default:
                    break;
            }
            if (url != "") {
                url = encodeURI(url);
                //plus.runtime.openURL(url,function(e){})调起手机APP应用
                plus.runtime.openURL(url, function (e) {
                    plus.nativeUI.alert("本机未安装指定的地图应用");
                });
            }
        })
    } else {
        plus.nativeUI.actionSheet({
            title: "选择地图应用",
            cancel: "取消",
            buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
        }, function (e) {
            switch (e.index) {
                case 1:
                    url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                    break;
                case 2:
                    url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&content=${name}&src=ios.baidu.openAPIdemo&coord_type=gcj02`;
                    break;
                case 3:
                    url = `iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                    break;
                default:
                    break;
            }
            if (url != "") {
                url = encodeURI(url);
                plus.runtime.openURL(url, function (e) {
                    plus.nativeUI.alert("本机未安装指定的地图应用");
                });
            }
        })
    }

}
const systemInfo = uni.getSystemInfoSync()
const isAndroid = systemInfo.platform == 'android'
</script>

<style lang="scss" scoped>
.mapBox {
    width: 100%;
    height: 100vh;
}

// css 区分安卓ios
.iconMain {
    position: absolute;
    bottom: 32rpx;
    left: 24rpx;
    width: 702rpx;
    height: 404rpx;
    background: transparent;
    z-index: 99999;

    .flexBox {
        width: 702rpx;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        padding-bottom: 32rpx;
        padding-top: 20rpx;

        .iconBox {
            display: inline-block;
            width:64rpx;
            height: 64rpx;
            margin-right: 23rpx;
            border-radius: 16rpx;
            background: #ffffff;
            box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);

            .icon {
                width: 100%;
                height: 100%;
            }
        }

        .iconNav {
            display: inline-block;
            width:64rpx;
            height: 64rpx;
            flex-shrink: 0;
            margin-right: 0rpx;
            border-radius: 16rpx;
            background: #ffffff;
            box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);

            .icon {
                width: 100%;
                height: 100%;
            }
        }
    }

    .flex-1 {
        flex: 1;
    }

    .whiteBox {
        width: 702rpx;
        //height: 310rpx;
        background: #ffffff;
        border-radius: 16rpx;
        box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);

        .title {
            margin: 32rpx;
            color: #242424;
            font-family: "SF Pro Text";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 700;
            line-height: 24rpx;
            height: 30rpx;
        }

        .time {
            margin: 0 32rpx 32rpx;
            color: #969696;
            font-family: "SF Pro Text";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx; /* 100% */
            height: 30rpx;
        }

        .border {
            width: 702rpx;
            height: 1px;
            flex-shrink: 0;
            border-radius: 8rpx;
            background: #F2F2F2;
        }

        .location {
            margin: 32rpx;
            height: 80rpx;
            position: relative;

            .locationTitle {
                position: absolute;
                top: 0;
                width: 120rpx;
                display: inline-block;
                color: #969696;
                font-family: "SF Pro";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 36rpx; /* 150% */
            }

            .text {
                margin-left: 120rpx;
                display: inline-block;
                width: 520rpx;
                white-space: wrap;
                color: #242424;
                font-family: "SF Pro";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 36rpx; /* 150% */
            }
        }
    }
}

.android {
    bottom: 200rpx;
}

.ios {
    bottom: 64rpx;
}
</style>
