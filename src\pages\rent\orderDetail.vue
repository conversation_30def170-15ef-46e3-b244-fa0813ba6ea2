<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {backCarAPI, buyOrderAPI, cancelOrderAPI, getOrderDetail} from "@/services/rent";
import moment from 'moment'

onMounted(() => {
    getMemberOrderData()
})
const props = defineProps({
    id: {
        type: Number,
        default: 0
    }
})
const mainInfo: any = ref({})
const statusClass = ref('')
const getMemberOrderData = async () => {
    let res = await getOrderDetail(props.id)
    console.log(res)
    mainInfo.value = res.result
    statusClass.value = getClass()
}
// 监听mainInfo.value.status,切换class
const getClass = () => {
    switch (mainInfo.value.status) {
        case 2:
            return 'green';
        case 3:
            return 'blue';
        default:
            return '';
    }
}
// tab列表
const tabTypes = [
    {name: 0, label: '待支付', disabled: false},
    {name: 1, label: '待核销', disabled: false},
    {name: 2, label: '使用中', disabled: false},
    {name: 3, label: '已完成', disabled: false},
    {name: 10, label: '已取消', disabled: false},
    {name: 11, label: '已关闭', disabled: false},
]


const formatPayState = (order: any) => {
    let payState = order.status
    const stateMap: any = {
        0: '待支付',
        1: '待核销',
        2: '使用中',
        3: '已完成',
        10: '已取消',
        11: '已关闭',
    }
    return stateMap[payState]
}
const submitPay = async () => {
    let resPay = await buyOrderAPI(props.id)
    console.log(resPay)
    if (resPay.msg == 'success') {
        getMemberOrderData()
    }
}
const cancelPay = async () => {
    let resPay = await cancelOrderAPI(props.id)
    if (resPay.msg == 'success') {
        getMemberOrderData()
    }
}
//  还车
const toLine = () => {
    uni.navigateTo({
        url: '/pages/rent/line?id=' + props.id
    })
}
const formatTime = (time: any) => {
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
const scanCode = () => {
    uni.scanCode({
        success: (res: any) => {
            if (res.errMsg == "scanCode:ok") {
                let str = res.result
                console.log(str)
                uni.navigateTo({
                    url: '/pages/rent/codeAdd?sn=' + str + '&order=' + props.id
                })
            }
        }
    })
}
const backCar =async  () => {
   let res = await backCarAPI(props.id,{
        "deviceSn": mainInfo.value.deviceSn,
        "snapshot2": mainInfo.value.snapshot,
   })
    if (res.msg == '订单状态已改变') {
        uni.showToast({
            title: '车辆已退还',
            icon: 'none'
        })
    }
}

</script>

<template>
    <view class="bgBox">
        <view :class="['status', statusClass]">{{ formatPayState(mainInfo) }}</view>
        <view class="statusDesc">租车订单</view>
        <view class="itemBox">
            <view class="title">{{ mainInfo.store.name }}
                <image  @click="toLine" v-if="mainInfo.status ==2" class="iconImg" mode="aspectFit" src="./imgs/nav.png"></image>
            </view>
            <view class="name">{{ mainInfo.menu.name }}</view>
            <view class="desc">{{ mainInfo.menu.description }}</view>
            <view class="priceMain">
                <view class="price">
                    <view class="priceText">租金</view>
                    <view class="priceNum">
                        <view class="unit">￥</view>
                        {{ mainInfo.menu.price / 100 }}
                    </view>
                </view>
                <view class="price">
                    <view class="priceText">天数</view>
                    <view class="priceNum">{{ mainInfo.menu.days }}
                        <view class="unit">天</view>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <view class="mainBox">
        <view class="mainTitle">订单信息</view>
        <view class="infoBox">
            <view class="label">订单编号</view>
            <view class="content">{{ mainInfo.id }}</view>
        </view>
        <view class="infoBox">
            <view class="label">下单时间</view>
            <view class="content">{{ formatTime(mainInfo.createdAt) }}</view>
        </view>
        <view class="infoBox">
            <view class="label">支付方式</view>
            <view class="content">在线支付</view>
        </view>
        <view class="infoBox">
            <view class="label">支付时间</view>
            <view v-if="mainInfo.payAt" class="content">{{ formatTime(mainInfo.payAt) }}</view>
        </view>
        <view class="infoBox">
            <view class="label">交易流水号</view>
            <view class="content">{{ mainInfo.id }}</view>
        </view>
        <view class="infoBox">
            <view class="label">实付金额</view>
            <view class="content red">￥{{ mainInfo.total / 100 }}</view>
        </view>
    </view>
    <view v-if="mainInfo.status==0" class="orderBox">
        <view class="orderMain">
            <view class="rightBox">
                <u-button type="primary" @click="cancelPay">取消支付</u-button>
            </view>
            <view class="rightBox">
                <u-button type="primary" @click="submitPay">提交支付</u-button>
            </view>
        </view>
    </view>
    <view v-if="mainInfo.status==1" class="orderBox">
        <view class="orderMain">
            <view class="rightBoxMain">
                <u-button type="primary" @click="scanCode">扫码核销</u-button>
            </view>
        </view>
    </view>
    <view v-if="mainInfo.status==2" class="orderBox">
        <view class="orderMain">
            <view class="rightBoxMain">
                <u-button type="primary" @click="backCar">退还车辆</u-button>
            </view>
        </view>
    </view>
    <view v-if="mainInfo.status==3" class="orderBox">
        <view class="orderMain">
            <view class="rightBoxMain">
                <u-button type="primary" @click="scanCode">扫码核销</u-button>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped>
.mainBox {
    background: #f5f5f5;
}

.bgBox {
    width: 750rpx;
    height: 272rpx;
    flex-shrink: 0;
    border-radius: 0px 0px 30rpx 30rpx;
    background: #3C9CFF;
    padding-top: 30rpx;
    margin-bottom: 174rpx;

    .status {
        color: #FFF;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 40rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 32rpx; /* 80% */
    }

    .statusDesc {
        color: #FFF;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 32rpx; /* 133.333% */
        margin-top: 16rpx;
    }
}

.mainBox {
    margin: 32rpx;
    padding: 24rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.05);

    .mainTitle {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 30rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 40rpx;
        margin-bottom: 28rpx;
    }

    .infoBox {
        display: flex;
        justify-content: space-between;
        margin-bottom: 22rpx;

        .label {
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption1, 24rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
            letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .content {
            color: var(--Text-content, #53565C);
            text-align: right;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption1, 24rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
            letter-spacing: var(--Letter-Spacing-Caption1, 0px);
        }

        .red {
            color: #FF0004;
            text-align: right;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 32rpx; /* 133.333% */
        }
    }
}

.itemBox {
    position: absolute;
    margin: 32rpx;
    padding: 24rpx;
    width: 686rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    .title {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 44rpx; /* 129.412% */
        display: flex;
        justify-content: space-between;
        margin-bottom: 32rpx;

        .iconImg {
            width: 40rpx;
            height: 40rpx;
        }
    }

    .name {
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: "PingFang SC";
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 40rpx; /* 133.333% */
    }

    .desc {
        color: var(--Text-content, #53565C);
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Footnote, 26rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
        margin-bottom: 30rpx;
    }
}

.priceMain {
    display: flex;

    .price {
        flex: 1;
        display: flex;
        align-items: flex-end;
    }

    .priceText {
        color: #C3C3C3;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 24rpx; /* 100% */
    }

    .priceNum {
        display: flex;
        align-items: flex-end;
        color: #F00;
        font-family: "PingFang SC";
        font-size: 40rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 35rpx; /* 100% */
        .unit {
            color: #F00;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx; /* 100% */
        }
    }
}

.orderMain {
    position: fixed;
    bottom: 0;
    width: 750rpx;
    height: 130rpx;
    flex-shrink: 0;
    background: #FFF;
    box-shadow: 0px 4rpx 4rpx 0px rgba(0, 0, 0, 0.25);
    display: flex;

    .leftBox {
        flex: 1;
        padding: 0 32rpx;

        .price {
            color: #F00;
            font-family: "PingFang SC";
            font-size: 40rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 24rpx;
            margin-bottom: 20rpx;
            margin-top: 40rpx;

            .unit {
                color: #F00;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 24rpx; /* 100% */
            }
        }

        .priceDesc {
            color: #C3C3C3;
            font-family: "PingFang SC";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 24rpx; /* 100% */
        }
    }

    .rightBox {
        width: 378rpx;
        padding: 24rpx 32rpx;
        flex-shrink: 0;
    }

    .rightBoxMain {
        width: 100%;
        padding: 24rpx 32rpx;
        flex-shrink: 0;
    }

}
</style>
