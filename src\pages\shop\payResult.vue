<script lang="ts" setup>
import {getOrderAPI} from '@/services/pay'
import {onMounted, ref} from 'vue'
import { onLoad } from '@dcloudio/uni-app'
const payMoney = ref(0)
const add = (a:any, b:any) => {
    let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b));
    const sum = Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b;
    return sum.toFixed(2)
}
const query:any = ref({})
onLoad((options)=>{
    query.value = options
})
const orderInfo: any = ref({})
const getOrderInfo = async () => {
    let order: any = query.value.out_trade_no
    const flag = query.value.out_trade_no.indexOf('-')
    if (flag > -1) {
        order = order.split('-')[0]
    }
    const res: any = await getOrderAPI(order)
    orderInfo.value = res.result
    if (res.result.isPreSale) {
        // 根据定金是否支付判断时定金还是尾款支付
        if(res.result.status == 4){
            payMoney.value = add(res.result.preTotalMoney / 100, res.result.postFee / 100)

        }else{
            payMoney.value = add(res.result.nextTotalMoney / 100, 0)

        }
    } else {
        payMoney.value = add(res.result.payMoney / 100, res.result.postFee / 100)
    }
}
onMounted(() => {
    if (query.value.method == "alipay.trade.page.pay.return") {
        getOrderInfo()
    }
})
const toOrder = () => {
    uni.redirectTo({
        url: '/pages/shop/order',
    })
}
const back = () => {
    uni.navigateBack({delta: 1})
}
</script>


<template>
    <view class="xtx-pay-page">
        <view class="container">
            <view class="payTop">
                <view class="topInfo">
                    <image src="./imgs/back.png" @click="back"></image>
                </view>
            </view>
            <!-- 支付结果 -->
            <view class="pay-result">
                <view class="resultBox">
                    <svg class="resultIcon" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none">
                        <circle cx="28" cy="28" r="28" fill="#3C9CFF"/>
                        <path d="M16 27.5L26 37.5L43.5 20" stroke="#EDEDED" stroke-width="3" stroke-linecap="round"/>
                    </svg>
                    <view class="resultText">
                        已支付
                    </view>
                </view>
                <view class="orderNo">
                    订单编号：{{query.out_trade_no}}
                </view>
            </view>
        </view>
        <view class="btn" @tap="toOrder">查看订单</view>
    </view>
</template>

<style lang="scss" scoped>
.payTop{
    width: 750rpx;
    height: 382rpx;
    flex-shrink: 0;
    background: url('imgs/bg.png') lightgray 50% / cover no-repeat;
    box-shadow: 0px 8px 8px 0px rgba(0, 0, 0, 0.03);
    .topInfo {
        padding: 0 26rpx;
        padding-top: 96rpx;
        display: flex;
        width: 100%;

        image {
            width: 56rpx;
            height: 56rpx;
            flex-shrink: 0;
        }
    }
}
.pay-result {
    margin-top: 60rpx;
    .resultBox{
        display: flex;
        justify-content: center;
        align-items: center;
        .resultIcon{
            width: 56rpx;
            height: 56rpx;
            flex-shrink: 0;
            margin-right: 14rpx;
        }
        .resultText{
            color: var(--Normal-Black-333, #333);
            font-family: "PingFang HK";
            font-size: 40rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 40px */
        }
    }
    .orderNo{
        width: 600rpx;
        display: flex;
        height: 52rpx;
        padding: 5rpx 43rpx;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        margin: 42rpx auto 0;
        border-radius: 66rpx;
        border: 1px solid var(--Border-Border-Primary, #EDEDED);
        background: var(--Backgroung-Background-Primary, #F9F9F9);
        color: var(--Normal-Black-333, #333);
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 24px */
    }

}
.btn{
    display: flex;
    width: 676rpx;
    height: 104rpx;
    padding: 17rpx 50rpx;
    justify-content: center;
    align-items: center;
    gap: 8rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    border: var(--stroke-weight-1, 1px) solid var(--Brand-Green-Primary, #3C9CFF);
    background: var(--Brand-Green-Primary, #3C9CFF);
    color: var(--Normal-White, #FFF);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 24px */
    position: fixed;
    bottom: 80rpx;
    left: 38rpx;
    box-sizing: border-box;
}
</style>
