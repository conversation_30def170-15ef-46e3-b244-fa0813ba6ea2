<script lang="ts" setup>
import {postMemberAddressAPI, putMemberAddressByIdAPI,} from '@/services/address'
import {onLoad} from '@dcloudio/uni-app'
import {ref} from 'vue'
import {useI18n} from 'vue-i18n'
import {addressData} from './city.js'

const localData = ref(addressData)
const {t} = useI18n()
// 表单数据
const form = ref({
    receiver: '', // 收货人
    contact: '', // 联系方式
    fullLocation: '', // 省市区(前端展示)
    provinceCode: '', // 省份编码(后端参数)
    cityCode: '', // 城市编码(后端参数)
    countyCode: '', // 区/县编码(后端参数)
    address: '', // 详细地址
    isDefault: 0, // 默认地址，1为是，0为否
})

// 获取页面参数
const query = defineProps<{
    id?: string
}>()

// 获取收货地址详情数据
const getMemberAddressByIdData = async () => {
    if (query.id) {
        // 发送请求
        const res = JSON.parse(uni.getStorageSync('userAddress'))
        // 把数据合并到表单中
        Object.assign(form.value, res)
        console.log(form.value)
    }
}

// 页面加载
onLoad(() => {
    getMemberAddressByIdData()
})

// 动态设置标题
uni.setNavigationBarTitle({
    title: query.id ? '修改地址' : '新增地址',
})

// 收集所在地区
const onRegionChange: any = (ev) => {
    console.log(ev)
    // 省市区(前端展示)
    // 数组的每个对象值拼接
    let list = ev.detail.value.map((item: any) => item.text)
    form.value.fullLocation = list.join('')

    let codeList = ev.detail.value.map((item: any) => item.value)
    // 省市区(后端参数)
    const [provinceCode, cityCode, countyCode] = [codeList[0], codeList[1], codeList[2] || '']
    Object.assign(form.value, {provinceCode, cityCode, countyCode})
    console.log(form.value)
}

// 收集是否默认收货地址
const onSwitchChange: any = (ev) => {
    console.log(ev.detail.value)
    form.value.isDefault = ev.detail.value
}

// 定义校验规则
const rules: any = {
    receiver: {
        rules: [{required: true, errorMessage: '请输入收货人姓名'}],
    },
    contact: {
        rules: [
            {required: true, errorMessage: '请输入联系方式'},
            {pattern: /^1[3-9]\d{9}$/, errorMessage: '手机号格式不正确'},
        ],
    },
    countyCode: {
        rules: [{required: true, errorMessage: '请选择所在地区'}],
    },
    address: {
        rules: [{required: true, errorMessage: '请选择详细地址'}],
    },
}

// 表单组件实例
const formRef = ref<any>()

// 提交表单
const onSubmit = async () => {
    try {
        // 表单校验
        await formRef.value?.validate?.()
        let mainInfo = JSON.parse(JSON.stringify(form.value))
        mainInfo.isDefault = form.value.isDefault == 1
        console.log(mainInfo)
        let res: any
        // 校验通过后再发送请求
        if (query.id) {
            // 修改地址请求
            res = await putMemberAddressByIdAPI(query.id, mainInfo)
        } else {
            // 新建地址请求
            res = await postMemberAddressAPI(mainInfo)
        }
        if (res.msg == 'success') {
            // 成功提示
            uni.showToast({icon: 'success', title: query.id ? '修改成功' : '添加成功'})
            // 返回上一页
            setTimeout(() => {
                uni.navigateBack()
            }, 400)
        }
    } catch (error) {
        uni.showToast({icon: 'error', title: '请填写完整信息'})
    }
}
</script>

<template>
    <view class="content">
        <uni-forms ref="formRef" :model="form" :rules="rules">
            <!-- 表单内容 -->
            <view class="form-item" name="receiver">
                <text class="label">收货人</text>
                <input v-model="form.receiver" class="input" placeholder="请填写收货人姓名"/>
            </view>
            <view class="form-item" name="contact">
                <text class="label">电话</text>
                <input
                    v-model="form.contact"
                    :maxlength="11"
                    class="input"
                    placeholder="请填写收货人手机号码"
                />
            </view>
            <view class="form-item" name="countyCode">
                <text class="label">所在地区</text>
                <uni-data-picker v-model="form.countyCode" :localdata="localData" popup-title="请选择省市区"
                                 @change="onRegionChange" style="margin: 10rpx 0">
                </uni-data-picker>
            </view>
            <view class="form-item" name="address">
                <text class="label">详细地址</text>
                <input v-model="form.address" class="input" placeholder="街道、楼牌号等信息"/>
            </view>
            <view class="form-item">
                <label class="label">设为默认地址</label>
                <switch
                    :checked="form.isDefault"
                    class="switch"
                    color="#3C9CFF"
                    @change="onSwitchChange"
                />
            </view>
        </uni-forms>
    </view>
    <!-- 提交按钮 -->
    <button class="button" @tap="onSubmit">保存地址</button>
</template>

<style lang="scss" scoped>
// 深度选择器修改 uni-data-picker 组件样式
:deep(.selected-area) {
    flex: 0 1 auto;
    height: auto;
}

:deep(.uniui-clear::before) {
    content: ''
}

page {
    background-color: #f4f4f4;
}

.content {
    margin: 20rpx 20rpx 0;
    padding: 0 20rpx;
    border-radius: 10rpx;
    background-color: #fff;

    .form-item,
    .uni-forms-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        min-height: 96rpx;
        padding: 25rpx 10rpx 10rpx;
        background-color: #fff;
        font-size: 28rpx;
        border-bottom: 1rpx solid #ddd;
        position: relative;
        margin-bottom: 0;

        input {
            font-size: 24rpx;
            margin-top: 10rpx;
        }

        // 调整 uni-forms 样式
        .uni-forms-item__content {
            display: flex;
        }

        .uni-forms-item__error {
            margin-left: 200rpx;
        }

        &:last-child {
            border: none;
        }

        .label {
            width: 200rpx;
            color: #333;
        }

        .input {
            flex: 1;
            display: block;
            height: 46rpx;
        }

        .switch {
            position: absolute;
            right: -20rpx;
            transform: scale(0.8);
        }

        .picker {
            flex: 1;
        }

        .placeholder {
            color: #808080;
        }
    }
}

.button {
    height: 80rpx;
    margin: 30rpx 20rpx;
    color: #fff;
    border-radius: 80rpx;
    font-size: 30rpx;
    background-color: #3C9CFF;
}
</style>
