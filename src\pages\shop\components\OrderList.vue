<script lang="ts" setup>
import {cancelOrderAPI, confirmOrderAPI, getUserOrder, refundOrderAPI, returnNumberAPI,} from '@/services/order'
import {onMounted, ref} from 'vue'
import moment from 'moment'
import GoodItem from './GoodItem.vue'
import TimeCount from './timeCount.vue'
import neilModal from '@/components/neil-modal/neil-modal.vue'
import htzImageUpload from '@/components/htz-image-upload/htz-image-upload.vue'
// 定义 porps
const props = defineProps<{
    orderState: number
}>()
const isLoading = ref(false)
const isFinish = ref(false)
const isTriggered = ref(false)
//
const getMemberOrderData = async () => {
    // 如果数据处于加载中，退出函数
    if (isLoading.value) return;
    // 退出分页判断
    if (isFinish.value) {
        uni.showToast({icon: 'none', title: '没有更多数据~'});
        return;
    }
    try {
        // 发送请求前，标记为加载中
        isLoading.value = true;
        // 发送请求
        const res = await getUserOrder(params.value);
        // 数组追加
        orderList.value = [...orderList.value, ...res.result.rows];
        // 分页逻辑
        updatePaginationState(res.result.count);
    } catch (error) {
        console.error('Error fetching user orders:', error);
        uni.showToast({icon: 'none', title: '请求失败，请稍后再试'});
    } finally {
        // 发送请求后，重置标记
        isLoading.value = false;
    }
};
const updatePaginationState = (totalCount: any) => {
    if (params.value.size > totalCount) {
        isFinish.value = true;
    } else if ((params.value.page - 1) * params.value.size < totalCount) {
        params.value.page++;
    } else {
        isFinish.value = true;
    }
};
onMounted(() => {
    getMemberOrderData()
})
// 自定义下拉刷新被触发
const onRefresherrefresh = async () => {
    // 开始动画
    isTriggered.value = true
    // 重置数据
    params.value.page = 1
    orderList.value = []
    isFinish.value = false
    // 加载数据
    await getMemberOrderData()
    // 关闭动画
    isTriggered.value = false
}

// tab列表
const tabTypes = [
    {name: '-1', label: '全部'},
    {name: '1', label: '待支付'},
    {name: '4', label: '待发货'},
    {name: '5', label: '待收货'},
    {name: '7', label: '已完成'},
    {name: '10', label: '售后'},
    {name: '11', label: '已取消'},
]
// 获取订单列表
const orderList: any = ref([])
const total = ref(0)
const params = ref({
    status: props.orderState,
    page: 1,
    size: 10,
})

// tab切换
const tabChange = (type: any) => {
    params.value.status = type
    orderList.value = []
    getMemberOrderData()
}

// 页数切换
const pageChange = (page: any) => {
    console.log(page)
    params.value.page = page
    getMemberOrderData()
}

const formatPayState = (order: any) => {
    let payState = order.status
    const stateMap: any = {
        1: '待支付',
        2: '待尾款',
        4: '待发货',
        5: '待收货',
        6: '待评价',
        7: '已完成',
        10: '售后',
        11: '已取消',
    }
    if (order.refundSupportId != null) {
        let str = ''
        if (order.refundSupport.status == 0) {
            str = '待审核'
        }
        if (order.refundSupport.status == 2) {
            str = '审核拒绝'
        }
        if (order.refundSupport.status == 3) {
            str = '审核通过'
        }
        if (order.refundSupport.status == 5) {
            str = '退款成功'
        }
        if (order.refundSupport.status == 7) {
            str = '退货退款'
        }
        if (order.refundSupport.status == 8) {
            str = '已退货'
        }
        return '售后 ' + str
    } else {
        return stateMap[payState]
    }
}
const formatTime = (time: any) => {
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
const add = (a: any, b: any) => {
    let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b))
    let sum = Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b
    return sum.toFixed(2)
}
const toPay = (id: any) => {
    uni.navigateTo({url: '/pages/shop/pay?id=' + id})
}
const toPayEnd = (id: any) => {
    uni.navigateTo({url: '/pagesOrder/pay/payEnd?id=' + id})
}
const cancelOrderAction = async (id: any) => {
    uni.showModal({
        title: '提示',
        content: '是否取消订单？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        success: async (action: any) => {
            if (action.confirm) {
                // 取消订单
                let res: any = await cancelOrderAPI(id)
                console.log(res)
                if (res.msg == 'success') {
                    onRefresherrefresh()
                }
            }
        },
    })
}

const confirmOrderAction = async (id: any) => {
    uni.showModal({
        title: '提示',
        content: '是否确认收货？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        success: async (action: any) => {
            if (action.confirm) {
                // 确认收货
                let res: any = await confirmOrderAPI(id)
                if (res.msg == 'success') {
                    onRefresherrefresh()
                }
            }
        },
    })
}
const isRefund = ref(false)
const activeForm: any = ref({})
const showRefund = (item: any) => {
    isRefund.value = true
    imgData.value = []
    videoData.value = []
    reason.value = ''
    activeForm.value = item
}
const closeRefund = () => {
    isRefund.value = false
}
const isRefundShipping = ref(false)
const showRefundShipping = (item: any) => {
    isRefundShipping.value = true
    shippingNum.value = ''
    shippingCompany.value = 'yuantong'
    activeForm.value = item
}
const closeRefundShipping = () => {
    isRefundShipping.value = false
}
//  填写物流单号
const confirmRefundShipping = async () => {
    let mainInfo = {
        refundSupportId: activeForm.value.refundSupportId,
        returnTrackNumber: shippingNum.value,
        returnTrackCom: shippingCompany.value,
    }
    let res: any = await returnNumberAPI(mainInfo)
    console.log(res)
    if (res.msg == 'success') {
        onRefresherrefresh()
        closeRefundShipping()
    }
}
const shippingNum = ref('')
const shippingCompany = ref('yuantong')
const shippingList = [
    {
        label: '圆通速递',
        value: 'yuantong',
    },
    {
        label: '中通快递',
        value: 'zhongtong',
    },
    {
        label: '韵达快递',
        value: 'yunda',
    },
    {
        label: '极兔速递',
        value: 'jtexpress',
    },
    {
        label: '申通快递',
        value: 'shentong',
    },
    {
        label: '邮政快递包裹',
        value: 'youzhengguonei',
    },
    {
        label: '顺丰速运',
        value: 'shunfeng',
    },
    {
        label: '京东物流',
        value: 'jd',
    },
    {
        label: 'EMS',
        value: 'ems',
    },
    {
        label: '邮政电商标快',
        value: 'youzhengdsbk',
    },
    {
        label: '德邦快递',
        value: 'debangkuaidi',
    },
    {
        label: '菜鸟速递',
        value: 'danniao',
    },
    {
        label: '顺丰快运',
        value: 'shunfengkuaiyun',
    },
    {
        label: '德邦物流',
        value: 'debangwuliu',
    },
    {
        label: '中通快运',
        value: 'zhongtongkuaiyun',
    },
    {
        label: '京东快运',
        value: 'jingdongkuaiyun',
    },
    {
        label: '速必达',
        value: 'subida',
    },
    {
        label: '安能快运',
        value: 'annengwuliu',
    },
    {
        label: '跨越速运',
        value: 'kuayue',
    },
]
const toDetail = (order: any) => {
    uni.navigateTo({
        url: '/pages/shop/orderDetail?id=' + order.id,
    })
}
const confirmRefund = async () => {
    refundForm.value.videos = videoData.value
    refundForm.value.imgs = imgData.value
    refundForm.value.reason = reason.value
    refundForm.value.orderId = activeForm.value.id
    console.log(refundForm.value)
    let res: any = await refundOrderAPI(refundForm.value)
    console.log(res)
    if (res.msg == 'success') {
        onRefresherrefresh()
    }
}
const bindPickerChange = (e: any) => {
    index.value = e.detail.value
    shippingCompany.value = shippingList[e.detail.value].value
}
const index: any = ref(0)
const imgData: any = ref([])
const videoData: any = ref([])
const refundForm: any = ref({
    reason: '',
    imgs: [],
    videos: [],
})
const reason = ref('')
const imgSuccess = (res: any) => {
    imgData.value.push(res)
}
const videoSuccess = (res: any) => {
    videoData.value.push(res)
}
const buyAgain = (order: any) => {
    console.log(order)
}
</script>

<template>
    <view>
        <neil-modal
            :show="isRefund"
            cancel-text="取消"
            confirm-text="确定"
            title="退款"
            @close="closeRefund"
            @confirm="confirmRefund"
        >
            <scroll-view class="scroll-Y" scroll-y="true" style="height: 600rpx">
                <view class="formBox" style="margin-bottom: 0">
                    <label for="">退款理由</label>
                    <view class="content">
                        <textarea v-model="reason" placeholder="请输入退款理由" style="height:240rpx"></textarea>
                    </view>
                </view>
                <view class="formBox">
                    <label for="">照片</label>
                    <view class="content">
                        <htz-image-upload
                            v-model="imgData"
                            :max="5"
                            action="https://assets.cutold.com/v1/imgs/upload"
                            name="photos"
                            @uploadSuccess="imgSuccess"
                        ></htz-image-upload>
                    </view>
                </view>
                <view class="formBox" style="margin-bottom: 0">
                    <label for="">视频</label>
                    <view class="content">
                        <htz-image-upload
                            v-model="videoData"
                            :max="5"
                            action="https://assets.cutold.com/v1/videos/upload"
                            mediaType="video"
                            name="videos"
                            @uploadSuccess="videoSuccess"
                        ></htz-image-upload>
                    </view>
                </view>
            </scroll-view>
        </neil-modal>
        <neil-modal
            :show="isRefundShipping"
            cancel-text="取消"
            confirm-text="确定"
            title="退货"
            @close="closeRefund"
            @confirm="confirmRefundShipping"
        >
            <view class="formBox">
                <label for="">退货地址</label>
                <view class="content">
                    <view v-if="activeForm">
                        {{ activeForm.refundSupport?.returnAddress }}
                    </view>
                </view>
            </view>
            <view class="formBox">
                <label for="">退货单号</label>
                <view class="content">
                    <input
                        v-model="shippingNum"
                        class="uni-input"
                        placeholder="请输入退货单号"
                        type="number"
                    />
                </view>
            </view>
            <view class="formBox">
                <label for="">退货物流</label>
                <view class="content">
                    <picker :range="shippingList" :value="index" range-key="label" @change="bindPickerChange">
                        <view class="uni-input">{{ shippingList[index].label }}</view>
                    </picker>
                </view>
            </view>
        </neil-modal>
        <scroll-view
            :refresher-triggered="isTriggered"
            class="orders"
            enable-back-to-top
            refresher-enabled
            scroll-y
            style="height: calc(100vh - 160rpx)"
            @refresherrefresh="onRefresherrefresh"
            @scrolltolower="getMemberOrderData"
        >

            <view v-for="order in orderList" :key="order.id" class="order-item">
                <view class="head">
                    <view class="settlement">
                        <view class="text">订单编号: {{ order.id }}</view>
                        <view class="number">{{ formatPayState(order) }}</view>
                    </view>
                    <!-- 未付款，倒计时时间还有 -->
                    <view v-if="order.status === 1 || order.status === 2" class="settlement">
                        <view class="text">截止时间</view>
                        <view class="number">
                            <TimeCount :order="order"></TimeCount>
                        </view>
                    </view>
                </view>
                <view class="goods">
                    <view v-for="item in order.orderGoods" :key="item.id">
                        <GoodItem :orderGood="item" :skuId="item.skuId"></GoodItem>
                    </view>
                </view>
                <view class="desc">
                    <view class="descText">共{{ order.totalNum || 0 }}件商品，总金额</view>
                    <view class="red">
                        ¥{{ add(order.totalMoney / 100, order.postFee / 100) }}
                    </view>
                </view>
                <view class="actionBox">
                    <view v-if="order.status === 1" class="btn" @click="toPay(order.id)">
                        支付
                        <TimeCount :order="order"></TimeCount>
                    </view>
                    <view class="btnText" @click="toDetail(order)"><a>订单详情</a></view>
                    <view
                        v-if="
            (order.status === 4 || order.status === 5) &&
            order.isPreSale == false &&
            order.refundSupportId == null
          "
                        class="btnText"
                    >
                        <a @click="showRefund(order)">申请退款</a>
                    </view>
                    <view v-if="order.status === 1" class="btnCancel">
                        <a @click="cancelOrderAction(order.id)">取消订单</a>
                    </view>
                    <view v-if="order.status === 5 && order.refundSupportId == null" class="btnText">
                        <a @click="confirmOrderAction(order.id)">确认收货</a>
                    </view>
                    <view v-if="order.isPreSale == false && order?.refundSupport?.status === 7" class="btnText">
                        <a @click="showRefundShipping(order)">退货单号</a>
                    </view>
                    <view v-if="order.status === 7" class="btn">
                        <a @click="buyAgain(order)">再次购买</a>
                    </view>
                </view>
            </view>
            <!-- 底部提示文字 -->
            <view class="loading-text" style="margin-bottom: 64rpx">
                {{ isFinish ? '没有更多数据~' : '正在加载...' }}
            </view>
        </scroll-view>
    </view>

</template>

<style lang="scss">
.formBox {
    background: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    padding: 20rpx;

    label {
        color: var(--Normal-Black-666, #666);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 24px */
    }

    .content {
        textarea {
            height: 100rpx;
            color: var(--Normal-Black-666, #666);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
        }

        input {
            color: var(--Normal-Black-666, #666);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 24px */
        }
    }
}

.loading-text {
    color: var(--Normal-Black-666, #666);
    font-family: 'PingFang SC';
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60rpx;
    margin-bottom: 60rpx;
}

.order-item {
    width: 678rpx;
    flex-shrink: 0;
    padding: 0 32rpx 36rpx;
    background: #ffffff;
    border-radius: 12rpx;
    background: var(--Normal-White, #fff);
    margin: 24rpx auto 24rpx;
    box-sizing: border-box;

    .settlement {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 96rpx;
        border-bottom: 1px solid #ededed;

        .text {
            color: #333;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 28px; /* 116.667% */
        }

        .number {
            color: #333;
            text-align: right;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 28px; /* 116.667% */
        }
    }

    .goods {
        margin-bottom: 26rpx;
        border-bottom: 2rpx solid #ededed;
    }

    .desc {
        display: flex;
        justify-content: flex-end;

        .red {
            color: var(--Brand-Green-Primary, #3C9CFF);
            font-family: 'PingFang HK';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 150%;
        }

        .descText {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 20rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 30px */
            margin-right: 10rpx;
        }
    }

    .actionBox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;

        .btn {
            display: inline-flex;
            min-width: 200rpx;
            height: 72rpx;
            padding: 28rpx 38rpx;
            justify-content: center;
            align-items: center;
            gap: 8rpx;
            flex-shrink: 0;
            color: var(--Normal-White, #FFF);
            text-align: right;
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 36px */
            border-radius: 12rpx;
            background: var(--Brand-Green-Primary, #3C9CFF);
            box-sizing: border-box;
        }

        .btnText {
            color: var(--Normal-Black-333, #333);
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 36px */
        }

        .btnCancel {
            color: var(--Normal-Black-666, #666);
            font-family: "PingFang HK";
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 36px */
        }
    }
}

// 订单列表
.orders {
    background: #f3f3f3;
}
textarea{
    width: 470rpx;
    height: 300rpx;
    font-size: 24rpx;
    border: 1px solid #ededed;
    border-radius: 24rpx;
    padding: 24rpx;
    margin: 10px 0;
}
</style>
