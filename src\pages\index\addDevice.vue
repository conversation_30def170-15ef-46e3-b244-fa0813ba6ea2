<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="scanAdd">扫描设备二维码
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="blueAdd">使用蓝牙搜索设备
                <L-arrow></L-arrow>
            </view>
        </view>
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" @click="snAdd">使用设备SN码添加
                <L-arrow></L-arrow>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {useCarStore} from '@/stores'
const carStore = useCarStore()
carStore.clearCarInfo()
// 页面跳转函数
const scanAdd = () => {
    uni.scanCode({
        success: (res:any) => {
            if(res.errMsg== "scanCode:ok"){
				uni.navigateTo({
					      url:'/pages/addDevice/snResult?type=scan&sn='+res.result
				})
			}
        }
    })
}
// 页面跳转函数
const blueAdd = () => {
    uni.navigateTo({
        url: '/pages/addDevice/blueToothAdd'
    })
}
// 页面跳转函数
const snAdd = () => {
    uni.navigateTo({
        url: '/pages/addDevice/snAdd'
    })
}

</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .nickname {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

    }
}


</style>
