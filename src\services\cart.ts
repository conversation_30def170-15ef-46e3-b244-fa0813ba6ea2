import { http } from '@/utils/http'
const shopURL = 'https://shop.evl.usr.86.ltd/srv'


export const getCartListAPI = () => {
  return http({
    url:shopURL+ "/v1/me/cart",
      data: {
      page:1,
      size:40
    },
  })
}

/**
 * 获取购物车列表
 */
export const getMemberCartAPI = () => {
  return http({
    url: shopURL+"/v1/me/cart",
    data: {
      page:1,
      size:40
    },
  })
}
export const addCartQuantity = (id:any, data:any) => {
  return http({
    url: shopURL+`/v1/me/cart/${id}`,
    method: "PUT",
    data,
  })
};
/**
 * 删除/清空购物车单品
 * @param data 请求体参数 ids SKUID 集合
 */
export const deleteMemberCartAPI = (id:any) => {
  return http({
    method: 'DELETE',
    url: shopURL+`/v1/me/cart/${id}`,
  })
}





export const addCartShopAPI = (data:any) => {
  return http({
    method: 'POST',
    url: shopURL+'/v1/me/cart',
    data,
  })
}


