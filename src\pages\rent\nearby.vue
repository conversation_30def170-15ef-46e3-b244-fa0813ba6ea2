<template>
    <view class="searchBox">
        <image src="./imgs/search.svg" @tap="searchKey"></image>
        <u-input v-model="keyword" placeholder="门店搜索" @blur="searchKey" @keydown.enter="searchKey" ></u-input>
    </view>
    <view  class="tipBox" >
        <view v-for="item in dataList" class="tipItem" @click.stop.prevent="toDetail(item.id)">
            <view class="itemContent">
                <image class="shopImg" :src="item.thumb" mode="aspectFit"></image>
                <view class="mainBox">
                    <view class="name">
                        {{item.name}}
                    </view>
                    <view class="infoBox">
                        <view class="title"> {{item.detail.address}}</view>
                        <image class="distance" src="./imgs/tel.png" mode="aspectFit" @click.stop="callTel(item.detail.tel)"></image>
                    </view>
                    <view class="time">
                        营业时间：{{ item.timeStr }}
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {getNearStoreAPI, getNearStoreDetailAPI} from "@/services/rent";
const keyword = ref('')
const searchKey = () => {
    console.log(keyword.value)
    getData()
}
const toDetail = (id: any) => {
    uni.navigateTo({
        url: '/pages/rent/shopDetail?id=' + id
    })
}
// 请求数据
const filter = ref({
    lng: 0,
    lat: 0,
    distance:5000,
    q:''
})
const callTel = (tel:any) => {
    console.log(tel)
    uni.makePhoneCall({
        phoneNumber: tel,
        success: () => {
            console.log('makePhoneCall success')
        },
        fail: () => {
            console.log('makePhoneCall fail')
        }
    })
}
// 设备列表
const dataList = ref([])
const getData = async () => {
    filter.value.q=keyword.value
    console.log('downCallback',filter.value)
    let res = await getNearStoreAPI(filter.value)
    console.log(res)
    if (res.msg == 'success') {
        let list  = res.result
        for(let i in list){
            let resDetail = await getNearStoreDetailAPI(list[i].id)
            list[i].detail = resDetail.result
            console.log(list[i].detail,9999)
            if(list[i].detail.imgs&&list[i].detail.imgs.length>0){
                list[i].thumb = list[i].detail.imgs[0]
            }else{
                list[i].thumb =''
            }
            if(list[i].is24H){
                list[i].timeStr = '24小时'
            }else{
                list[i].timeStr = secondsToTime(list[i].startTime)+' - '+secondsToTime(list[i].endTime)
            }
        }
        console.log(list)
        dataList.value = list
    }
}

// 秒转时分秒
const secondsToTime = (seconds:any) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
// 页面加载
onMounted(() => {
    uni.getLocation({
        type: 'gcj02',
        success: (res) => {
            console.log(res)
            filter.value.lat = res.latitude;
            filter.value.lng = res.longitude;
            getData()
        }
    });
})
</script>

<style lang="scss" scoped>
.searchBox{
    display: flex;
    width: 686rpx;
    height: 80rpx;
    padding: 0px 12px;
    align-items: center;
    border-radius: 16rpx;
    border: 1px solid var(--Border-4, #E6E6E8);
    background: var(--Background-white, #FFF);
    margin: 32rpx 32rpx 0;
    image{
        width: 30rpx;
        height: 30rpx;
        margin-right: 20rpx;
    }
}
.tipBox {
    padding: 32rpx;
    .tipItem {
        margin-bottom: 32rpx;

        .itemContent {
            width: 686rpx;
            height: 280rpx;
            border-radius: 24rpx;
            background: var(--Background-white, #FFF);
            display: flex;
            padding: 32rpx;
            align-items: center;

            .shopImg {
                width: 200rpx;
                height: 200rpx;
                border-radius: 12rpx;
                margin-right: 24rpx;
            }

            .mainBox {
                flex: 1;
                .name {
                    overflow: hidden;
                    color: var(--Text-main, #282A2E);
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-family: "PingFang SC";
                    font-size: 30rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 40rpx; /* 133.333% */
                }

                .infoBox {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 24rpx;
                    margin-top: 24rpx;

                    .title {
                        color: var(--Text-content, #53565C);

                        /* Regular/Caption1 */
                        font-family: var(--Font-Family, "PingFang SC");
                        font-size: var(--Font-Size-Caption1, 24rpx);
                        font-style: normal;
                        font-weight: 400;
                        line-height: var(--Line-Height-Caption1, 32rpx); /* 133.333% */
                        letter-spacing: var(--Letter-Spacing-Caption1, 0px);
                    }

                    .distance {
                        width: 68rpx;
                        height: 68rpx;
                    }
                }

                .time {
                    color: #969696;
                    /* Regular/Caption2 */
                    font-family: var(--Font-Family, "PingFang SC");
                    font-size: var(--Font-Size-Caption2, 22rpx);
                    font-style: normal;
                    font-weight: 400;
                    line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
                    margin-top: 32rpx;
                }
            }
        }
    }
}

</style>
