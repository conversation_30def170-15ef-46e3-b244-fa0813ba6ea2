<script lang="ts" setup>
import {getCategoryAllAPI} from '@/services/category'
import {getHotGoodsAPI} from '@/services/hot'
import {onLoad} from '@dcloudio/uni-app'
import {onMounted, ref} from 'vue'
import CategoryPanel from './components/CategoryPanel.vue'
import GoodPanel from './components/GoodPanel.vue'
import {getShopBanner} from "@/services/banner";

onMounted(() => {
})
// 获取轮播图数据
const bannerList = ref<any>([])
const getHomeBannerData = async () => {
    try {
        const res: any = await getShopBanner({
            type: 0,
            page: 1,
            size: 10
        })
        if (res.msg === 'success') {
            bannerList.value = res.result.rows
        }
    } catch (error) {
        console.log(error)
    }
}
// 获取轮播图数据
const bannerListA = ref<any>([])
const getHomeBannerDataA = async () => {
    try {
        const res: any = await getShopBanner({
            type: 1,
            page: 1,
            size: 10
        })
        if (res.msg === 'success') {
            bannerListA.value = res.result.rows
        }
    } catch (error) {
        console.log(error)
    }
}
// 获取所有分类数据
const categoryList: any = ref([])
const getHomeCategoryData = async () => {
    const res = await getCategoryAllAPI()
    let list = res.result
    let arr = []
    for (let i in list) {
        arr.push([list[i]])
        if (list[i].childs) {
            arr.push(list[i].childs)
        }
    }
    list = arr.reduce((acc, cur) => {
        return acc.concat(cur)
    }, [])
    list.sort((a: any, b: any) => {
        return b.order - a.order
    })
    list = list.slice(0, 5)
    categoryList.value = list
    console.log(categoryList.value)
}
// 获取热门推荐数据
const hotList: any = ref([])
const getHotList = async () => {
    try {
        const res = await getHotGoodsAPI({
            page: 1,
            size: 8,
            collectId: '676ccb0851a48f9cfcdbf401',
        })
        console.log(res)
        if (res.msg === 'success') {
            let list = res.result.rows
            for (let i in list) {
                list[i].tags = await getGoodDetail(list[i])
            }
            console.log(list, 'tags')
            hotList.value = list
        }
    } catch (error) {
        console.log(error)
    }
}
// 新品数据
const newList: any = ref([])
const getNewList = async () => {
    try {
        const res = await getHotGoodsAPI({
            page: 1,
            size: 8,
            collectId: '676ccaf351a48f9cfcdbf400',
        })
        console.log(res)
        if (res.msg === 'success') {
            let list = res.result.rows
            for (let i in list) {
                list[i].tags = await getGoodDetail(list[i])
            }
            console.log(list, 'tags')
            newList.value = list
        }
    } catch (error) {
        console.log(error)
    }
}
const getGoodDetail = async (item: any) => {
    return new Promise((resolve, reject) => {
        let arr = []
        if (item.goods.isNew) {
            arr.push('New')
        }
        if (item.goods.isHot) {
            arr.push('Hot')
        }
        resolve(arr)
    })
};
// 是否加载中标记
const isLoading = ref(false)
// 页面加载
onLoad(async () => {
    isLoading.value = true
    await Promise.all([
        getHomeBannerData(),
        getHomeBannerDataA(),
        getHomeCategoryData(),
        getHotList(),
        getNewList(),
    ])
    isLoading.value = false
})
// 当前下拉刷新状态
const isTriggered = ref(false)
// 自定义下拉刷新被触发
const onRefresherrefresh = async () => {
    // 开始动画
    isTriggered.value = true
    // 加载数据
    await Promise.all([
        getHomeBannerData(),
        getHomeBannerDataA(),
        getHomeCategoryData(),
        getHotList(),
        getNewList(),
    ])
    // 关闭动画
    isTriggered.value = false
}
const searchKeyWorld = (e: any) => {
    uni.navigateTo({
        url: '/pages/shop/sort?keyword=' + e,
    })
}
</script>

<template>
    <view class="viewport">
        <!-- 自定义导航栏 -->
        <XtxTopBar @search="searchKeyWorld"/>
        <!-- 滚动容器 -->
        <scroll-view
            :refresher-triggered="isTriggered"
            class="scroll-view"
            enable-back-to-top
            refresher-enabled
            scroll-y
            @refresherrefresh="onRefresherrefresh"
            @scrolltolower="onScrolltolower"
        >
            <!-- 自定义轮播图 -->
            <XtxSwiper v-if="bannerList.length>0" :list="bannerList"/>
            <!-- 分类面板 -->
            <CategoryPanel v-if="categoryList.length>0" :list="categoryList"/>
            <!-- 自定义轮播图 -->
            <XtxSwiperList v-if="bannerListA.length>0" :list="bannerListA"/>
            <!-- 正在热销 -->
            <GoodPanel v-if="hotList.length>0" :list="hotList" :type="0" collectId="676ccb0851a48f9cfcdbf401"/>
            <!-- 为你推荐 -->
            <GoodPanel v-if="newList.length>0" :list="newList" :type="1" collectId="676ccaf351a48f9cfcdbf400"/>
        </scroll-view>
    </view>
</template>

<style lang="scss">
page {
    background-color: #f7f7f7;
    height: 100%;
    overflow: hidden;
}

.viewport {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.scroll-view {
    flex: 1;
    overflow: hidden;
}
</style>
