<script lang="ts" setup>
import {getOrderAPI, getPayLinkAPI, getWalletAPI,} from '@/services/pay'
import {onMounted, ref} from 'vue'
import {useCountDown} from '@/composables/useCountDown'
import {onLoad} from '@dcloudio/uni-app'

const {formatTime, start} = useCountDown()
// 获取订单数据
const payInfo: any = ref({})
const payMoney = ref(0)
const add = (a: any, b: any) => {
    let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b))
    const sum = Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b
    return sum.toFixed(2)
}
onMounted(async () => {
    getPayInfo()
})
const query: any = ref({})
onLoad((options) => {
    query.value = options
})
// 获取订单信息
const getPayInfo = async () => {
    const res: any = await getOrderAPI(query.value.id)
    payInfo.value = res.result
    if (res.result.status != 1) {
        uni.navigateTo({
            url: '/pagesOrder/list/list',
        })
    } else {
        payInfo.value.countdown = calculateTimeDifferenceInSeconds(res.result.payLatestTime)
        console.log(payInfo.value.countdown)
        // 初始化倒计时秒数
        start(payInfo.value.countdown)
        payMoney.value = add(res.result.totalMoney / 100, res.result.postFee / 100)
    }
}
// 获取支付链接
const getPayLink = async () => {
    if(activePay.value=='wx'||activePay.value=='alipay'){
        uni.showToast(
            {
                title: '暂未接入',
                icon: 'none',
                duration: 2000,
            }
        )
        return
    }
    let res = await getWalletAPI(query.value.id)
    console.log(res)
    if(res.msg == 'success'){
        uni.showToast({
            title: '支付成功',
            icon: 'none',
            duration: 2000,
        })
        uni.redirectTo({
            url:
                '/pages/shop/payResult?method=alipay.trade.page.pay.return&out_trade_no=' +
                query.value.id,
        })
    }
}
const calculateTimeDifferenceInSeconds = (targetDate: any) => {
    const now: any = new Date()
    const target: any = new Date(targetDate)
    const differenceInMilliseconds = target - now
    return Math.floor(differenceInMilliseconds / 1000)
}


const onCopySuccess = () => {
    alert('复制成功！')
}

const onCopyError = () => {
    alert('复制失败，请重试！')
}
const copyTextA = '<EMAIL>'
const copyTextB = '111111'
const copyTextC = '111111'
const iframeUrl = ref('')
const activePay = ref('wallet')
const changePay = (text: any) => {
    activePay.value = text
    iframeUrl.value = ''
}
const back = () => {
    uni.navigateBack({delta: 1})
}
</script>

<template>
    <L-top-height></L-top-height>
    <view class="xtx-pay-page">
        <view class="container">
            <!-- 付款信息 -->
            <view class="pay-info">
                <view class="topInfo">
                    <image src="./imgs/back.png" @click="back"></image>
                    <view>收银台</view>
                </view>
                <view class="amount"
                ><text class="unit">¥</text><text class="price">{{ payMoney }}</text></view
                >
                <view class="orderNo">订单编号：{{ query.id }}</view>
            </view>
            <!-- 付款方式 -->
            <view class="pay-type">
                <view class="item">
                    <image src="./imgs/wallet.svg" class="btn" mode="aspectFit"></image>
                    <view class="payText">余额支付</view>
                    <svg
                        v-if="activePay == 'wallet'"
                        fill="none"
                        height="40"
                        viewBox="0 0 40 40"
                        width="40"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                        <circle cx="20" cy="20" fill="#3C9CFF" r="11"/>
                    </svg>
                    <svg
                        v-else
                        fill="none"
                        height="40"
                        viewBox="0 0 40 40"
                        width="40"
                        xmlns="http://www.w3.org/2000/svg"
                        @click="changePay('wallet')"
                    >
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                    </svg>
                </view>
                <view class="item">
                    <image src="./imgs/alipay.svg" class="btn" mode="aspectFit"></image>
                    <view class="payText">支付宝支付</view>
                    <svg
                        v-if="activePay == 'alipay'"
                        fill="none"
                        height="40"
                        viewBox="0 0 40 40"
                        width="40"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                        <circle cx="20" cy="20" fill="#3C9CFF" r="11"/>
                    </svg>
                    <svg
                        v-else
                        fill="none"
                        height="40"
                        viewBox="0 0 40 40"
                        width="40"
                        xmlns="http://www.w3.org/2000/svg"
                        @click="changePay('alipay')"
                    >
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                    </svg>
                </view>
                <view class="item">
                    <image src="./imgs/wxpay.svg" class="btn wx" mode="aspectFit"></image>
                    <view class="payText">微信支付</view>
                    <svg v-if="activePay == 'wx'" fill="none" height="40" viewBox="0 0 40 40"
                         width="40" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                        <circle cx="20" cy="20" fill="#3C9CFF" r="11"/>
                    </svg>
                    <svg v-else fill="none" height="40" viewBox="0 0 40 40" width="40"
                         xmlns="http://www.w3.org/2000/svg" @click="changePay('wx')">
                        <circle cx="20" cy="20" r="19" stroke="#EDEDED" stroke-width="2"/>
                    </svg>
                </view>
                <view class="btnBox" size="large" type="primary" @click="getPayLink">
                    <view class="btnText">确定支付</view>
                    <view class="time"> {{ formatTime }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped>
$xtxColor: #19d454;
$priceColor: #fd0000;

.btnBox {
    position: fixed;
    bottom: 80rpx;
    display: flex;
    width: 676rpx;
    height: 104rpx;
    left: 42rpx;
    border-radius: 16rpx;
    border: var(--stroke-weight-1, 1px) solid var(--Brand-Green-Primary, #3C9CFF);
    background: var(--Brand-Green-Primary, #3C9CFF);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .btnText {
        color: var(--Normal-White, #FFF);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        height: 30rpx;
    }

    .time {
        color: var(--Normal-White, #FFF);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20rpx;
        font-style: normal;
        font-weight: 400;
        height: 30rpx;
    }
}

.pay-info {
    width: 750rpx;
    height: 382rpx;
    flex-shrink: 0;
    background: var(--Brand-Green-Primary, #3C9CFF);
    box-shadow: 0px 8px 8px 0px rgba(0, 0, 0, 0.03);

    .topInfo {
        padding: 0 26rpx;
        padding-top: 96rpx;
        display: flex;
        width: 100%;

        image {
            width: 56rpx;
            height: 56rpx;
            flex-shrink: 0;
        }

        view {
            flex: 1;
            color: var(--Normal-White, #fff);
            font-family: 'PingFang SC';
            font-size: 32rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 32px */
            text-indent: 235rpx;
        }
    }

    .amount {
        display: flex;
        align-items: flex-start;
        margin-top: 54rpx;
        justify-content: center;

        .unit {
            color: var(--Normal-White, #fff);
            font-family: 'PingFang HK';
            font-size: 48rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 48px */
            margin-right: 12rpx;
        }

        .price {
            color: var(--Normal-White, #fff);
            font-family: 'PingFang HK';
            font-size: 60rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 60px */
        }
    }

    .orderNo {
        width: auto;
        display: flex;
        height: 52rpx;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        border-radius: 66rpx;
        background: var(--normal-black-30, rgba(0, 0, 0, 0.3));
        color: var(--Normal-White, #fff);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 24px */
        margin: 24rpx 100rpx 0;
    }
}

.pay-type {
    background-color: #fff;
    padding: 15rpx 15rpx;
    .item {
        padding: 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .btn {
            width: 50rpx;
            height: 50rpx;
            flex-shrink: 0;
            margin-right: 32rpx;
        }
        .payText{
            flex:1;
            color: #000;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }
        svg {
            width: 40rpx;
            height: 40rpx;
            flex-shrink: 0;
        }
    }


}
</style>
