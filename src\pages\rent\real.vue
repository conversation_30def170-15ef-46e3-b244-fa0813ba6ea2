<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem border borderTopRadius ">
                <view class="nickname">正面照片:</view>
                <view class="avatar" @click="uploadFront">
                    <image v-if="userInfo.imgFront" :src="userInfo.imgFront" class="avatarItem"
                           ></image>
                    <text v-else> 上传</text>
                </view>
            </view>
            <view class="listItem border borderTopRadius ">
                <view class="nickname">反面照片:</view>
                <view class="avatar" @click="uploadBack">
                    <image v-if="userInfo.imgBack" :src="userInfo.imgBack" class="avatarItem"
                           ></image>
                    <text v-else> 上传</text>
                </view>
            </view>
            <view class="listItem ">
                <view class="nickname">身份证姓名:</view>
                <u-input v-model="userInfo.idName" placeholder="请输入身份证姓名" ></u-input>
            </view>
            <view class="listItem borderBottomRadius">
                <view class="nickname">身份证号:</view>
                <u-input v-model="userInfo.idCard" placeholder="请输入身份证号"></u-input>
            </view>
            <view class="listItem borderBottomRadius">
                <view class="nickname">手机号:</view>
                <u-input v-model="userInfo.tel" placeholder="请输入手机号"></u-input>
            </view>
        </view>
        <view style="margin: 32rpx;" v-if="userInfo">
            <u-button type="primary" @click="saveRealInfo" v-if="userInfo.status==0" >认证</u-button>
            <u-button type="primary" v-if="userInfo.status==1" >已认证</u-button>
            <u-button type="primary" @click="saveRealInfo" v-if="userInfo.status==2" >重新认证</u-button>
        </view>
        <view style="margin: 32rpx;" v-else>
            <u-button type="primary" @click="saveRealInfo" >认证</u-button>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getRealInfoAPI, savePutRealInfoAPI, saveRealInfoAPI} from '@/services/rent'
import {onShow} from "@dcloudio/uni-app";
import AliOss from "@/utils/upload.ts";
import {v4 as uuidv4} from 'uuid';
import {getOssParamsAPI} from "@/services/upload";

const ossParams = ref({
    accessid: '',
    policy: '',
    signature: '',
    fileName: '',
    savaPath: ''
})

const show = ref(false)
const userInfo: any = ref({
    imgFront: '',
    imgBack: '',
    idName: '',
    idCard: '',
    tel: ''
})
const dataInfo:any = ref(null)
const saveRealInfo = async () => {
    if(dataInfo.value.status == 2){
        const res = await savePutRealInfoAPI(userInfo.value)
        console.log(res,userInfo.value)
        if (res.msg == 'success') {
            uni.navigateBack()
        }
    }else{
        const res = await saveRealInfoAPI(userInfo.value)
        console.log(res,userInfo.value)
        if (res.msg == 'success') {
            uni.navigateBack()
        }
    }

}
const resetData = async () => {
    const res = await getRealInfoAPI()
    if (res.msg == 'success'&&res.result!=null) {
        userInfo.value = res.result
    }
    dataInfo.value = res.result
}
//  获取OSS参数

const getOssParams = async () => {
    const res: any = await getOssParamsAPI()
    if (res.msg == 'success') {
        ossParams.value = res.result
    }
}
onShow(() => {
    getOssParams()
    resetData()
})
const avatarUrl = ref('')
// 上传头像函数
const uploadBack = () => {
    uni.chooseImage({
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
            const tempFilePaths = res.tempFilePaths;
            avatarUrl.value = tempFilePaths[0];
            // 这里可以添加上传图片到服务器的逻辑
            uploadToServerBack(tempFilePaths[0]);
        }
    });
}
// 上传图片到服务器的函数
const uploadToServerBack = async (filePath: string) => {
    let url = await uploadFile(filePath)
    console.log(url)
    userInfo.value.imgBack = url
}


// 上传头像函数
const uploadFront = () => {
    uni.chooseImage({
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
            const tempFilePaths = res.tempFilePaths;
            avatarUrl.value = tempFilePaths[0];
            // 这里可以添加上传图片到服务器的逻辑
            uploadToServerFront(tempFilePaths[0]);
        }
    });
}
// 上传图片到服务器的函数
const uploadToServerFront = async (filePath: string) => {
    let url = await uploadFile(filePath)
    console.log(url)
    userInfo.value.imgFront = url
}
// 上传文件到oss
const uploadFile = (filePath: string) => {
    ossParams.value.fileName = uuidv4() + '.png'
    ossParams.value.savaPath = "avatar/"
    return new Promise((resolve, reject) => {
        AliOss.upload(filePath, ossParams.value, (path: any) => {
            if (path) {
                resolve(path);
            } else {
                reject('');
            }
        })
    })

}
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;

    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .nickname {
            width: 200rpx;
            text-align: left;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 30rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 40rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

        .avatar {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            width: 150rpx;
            height: 150rpx;
            .avatarItem{
                width: 150rpx;
                height: 150rpx;
            }
        }
    }

    .border {
        height: 200rpx;
    }
}

.btnBox {
    position: fixed;
    bottom: 32rpx;
    left: 32rpx;
    right: 32rpx;
    padding: 32rpx;
    background: #FFFFFF;

    .btn {
        color: var(--error-u-error-dark, #E45656);
        font-feature-settings: 'case' on;
        /* Regular/Body */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);
    }
}
</style>
