<template>
    <view>
        <!--地图信息    -->
        <map
            :key="latitude"
            id="map"
            :latitude="latitude"
            :longitude="longitude"
            :markers="covers"
            :scale="15.5"
            :show-compass="true"
            :style="{ width: '750rpx', height: windowHeight + 'px' }"
            @markertap="marketTapAction"
        ></map>
        <view class="mainBox">
            <view class="topBox">
                <view class="location" @click="toLocation">
                    <image class="icon" src="./imgs/location.png"></image>
                </view>
                <view class="refresh" @clixk="refreshData">
                    <image class="icon" src="./imgs/refresh.png"></image>
                </view>
                <view style="flex: 1"></view>
                <view class="shop" @click="toList">
                    <image class="icon" src="./imgs/shop.png"></image>
                </view>
            </view>
            <view class="bottomBox" v-if="showMarketInfo">
                <image class="leftBox"  :src="marketInfo.imgs[0]" mode="aspectFit"></image>
                <view class="rightBox">
                    <view class="leftMain">
                        <text class="title">{{ marketInfo.name }}</text>
                        <text class="address">{{ marketInfo.address }}</text>
                        <text class="time">营业时间：{{ timeStr }}</text>
                    </view>
                    <view class="rightMain">
                        <view class="nav" @click="navLine">
                            <image class="iconImg"  src="./imgs/nav.png"  mode="aspectFit"></image>
                        </view>
                        <text class="desc">距你{{ distanceStr }}</text>
                        <view class="tel" @click="callTel">
                            <image class="iconImg" src="./imgs/tel.png"  mode="aspectFit"></image>
                        </view>
                    </view>
                </view>

            </view>
        </view>

    </view>
</template>

<script>
import moment from "moment/moment";
import mine from './imgs/mine.png'
import store from './imgs/store.png'
import {ref} from "vue";
const app = getApp();

export default {
    computed: {
        moment() {
            return moment
        }
    },
    components: {},
    data() {
        return {
            windowHeight: 0,
            latitude: '36.679045',
            longitude: '117.06492',
            covers: [],
            list:[],
            map: null,
            showMarketInfo:false,
            marketInfo: null,
            timeStr:'',
            distanceStr:'',
            realInfo: {
                status:0
            },
            orderId:null,
        };
    },
    onLoad(options) {
        var systemInfo = uni.getSystemInfoSync();
        this.map = uni.createMapContext('map');
        this.windowHeight = systemInfo.windowHeight;
        this.orderId = options.id
    },
    onShow() {
        let that = this;
        uni.getLocation({
            type: 'gcj02',
            success: (res) => {
                that.latitude = res.latitude;
                that.longitude = res.longitude;
                that.getData()
            }
        });
    },
    methods: {
        async getData() {
            const member = JSON.parse(uni.getStorageSync('member')) ;
            uni.request({
                url: 'https://shop.evl.usr.86.ltd/srv/rent/me/order/'+this.orderId,
                method: 'GET',
                header:{
                    Authorization:member.profile.token
                },
                success: (res) => {
                    this.marketInfo = res.data.result
                    this.drawRide()
                },
                fail: (err) => {
                    console.log(err)
                }
            })
        },
        drawRide() {
            let that = this;
            const coverData = []; //线路起始点
            coverData.push({
                id: "mine",
                distance: 0,
                width: 30,
                height: 36,
                latitude: that.latitude,
                longitude: that.longitude,
                iconPath: mine
            })
            let item = this.marketInfo.store
            console.log(this.marketInfo,item)
            coverData.push({
                id:0,
                width: 30,
                height: 36,
                distance: item.distance,
                storeId: item.id,
                latitude: item.location.coordinates[1],
                longitude: item.location.coordinates[0],
                iconPath: store
            })
            this.covers = JSON.parse(JSON.stringify(coverData))
            let distance = this.getDistance(that.latitude, that.longitude, item.location.coordinates[1], item.location.coordinates[0])
            console.log(distance)
            this.getMarkerData(item.id, distance)
        },
        getMarkerData(storeId, distance) {
            console.log(storeId)
            const member = JSON.parse(uni.getStorageSync('member'));
            uni.request({
                url: 'https://shop.evl.usr.86.ltd/srv/rent/me/store/' + storeId,
                method: 'GET',
                header: {
                    Authorization: member.profile.token
                },
                success: (res) => {
                    this.showMarketInfo = true
                    if (res.data.result) {
                        this.marketInfo = res.data.result
                        if (this.marketInfo.is24H) {
                            this.timeStr = '24小时'
                        } else {
                            this.timeStr = this.secondsToTime(this.marketInfo.startTime) + ' - ' + this.secondsToTime(this.marketInfo.endTime)
                        }
                        // 米转千米，不足1000米显示米，大于1000米显示千米
                        this.distanceStr = distance
                    }
                },
                fail: (err) => {
                    console.log(err)
                }
            })
        },

 getDistance(lat1, lon1, lat2, lon2){
    console.log(lat1, lon1, lat2, lon2)
    const R = 6371; // 地球半径，单位为公里
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    // 距离，单位为公里
    const distance = R * c * 1000;
    let str = ''
    if (distance < 1000) {
        str= distance.toFixed(0) + 'm'
    } else {
        str = (distance / 1000).toFixed(2) + 'km'
    }
    return str
},
        deg2rad(deg) {
            return deg * (Math.PI / 180);
        },
        // 秒转时分秒
        secondsToTime(seconds){
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        },
        toLocation(){
            //     回到定位点
        },
        refreshData(){
            //     刷新数据
        },
        toList(){
            //      附近门店
            uni.navigateTo({
                url: '/pages/rent/nearby'
            })
        },
        callTel(){
            uni.makePhoneCall({
                phoneNumber: this.marketInfo.tel
            })
        },
        navLine() {
// 打开的点击事件，传经纬度和地点名
            let latitude = this.marketInfo.location.coordinates[1]
            let longitude = this.marketInfo.location.coordinates[0]
            let name = this.marketInfo.address+this.marketInfo.name
            let url = "";
            if (plus.os.name == "Android") {//判断是安卓端
                plus.nativeUI.actionSheet({//选择菜单
                    title: "选择地图应用",
                    cancel: "取消",
                    buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
                }, function (e) {
                    switch (e.index) {
                        //下面是拼接url,不同系统以及不同地图都有不同的拼接字段
                        case 1:
                            //注意referer=xxx的xxx替换成你在腾讯地图开发平台申请的key
                            url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                            break;
                        case 2:
                            url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&coord_type=gcj02&src=andr.baidu.openAPIdemo`;
                            break;
                        case 3:
                            url = `androidamap://viewMap?sourceApplication=appname&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                            break;
                        default:
                            break;
                    }
                    if (url != "") {
                        url = encodeURI(url);
                        //plus.runtime.openURL(url,function(e){})调起手机APP应用
                        plus.runtime.openURL(url, function (e) {
                            plus.nativeUI.alert("本机未安装指定的地图应用");
                        });
                    }
                })
            } else {
                // iOS上获取本机是否安装了百度高德地图，需要在manifest里配置
                // 在manifest.json文件app-plus->distribute->apple->urlschemewhitelist节点下添加
                //（如urlschemewhitelist:["iosamap","baidumap"]）
                plus.nativeUI.actionSheet({
                    title: "选择地图应用",
                    cancel: "取消",
                    buttons: [{title: "腾讯地图"}, {title: "百度地图"}, {title: "高德地图"}]
                }, function (e) {
                    switch (e.index) {
                        case 1:
                            url = `qqmap://map/geocoder?coord=${latitude},${longitude}&referer=xxx`;
                            break;
                        case 2:
                            url = `baidumap://map/marker?location=${latitude},${longitude}&title=${name}&content=${name}&src=ios.baidu.openAPIdemo&coord_type=gcj02`;
                            break;
                        case 3:
                            url = `iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
                            break;
                        default:
                            break;
                    }
                    if (url != "") {
                        url = encodeURI(url);
                        plus.runtime.openURL(url, function (e) {
                            plus.nativeUI.alert("本机未安装指定的地图应用");
                        });
                    }
                })
            }

        },
    }
};
</script>

<style scoped>
.realBox{
    position: absolute;
    top: 32rpx;
    left: 32rpx;
    display: flex;
    width: 686rpx;
    height: 80rpx;
    padding: 12rpx 16rpx;
    align-items: center;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 4rpx 12rpx 0px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.real{
    width: 40rpx;
    height: 40rpx;
    flex-shrink: 0;
}
.arrow{
    width: 40rpx;
    height: 40rpx;
    flex-shrink: 0;
}
.text{
    color: #282A2E;
    text-align: left;
    margin-left: 16rpx;
    flex:1;
    /* Regular/Caption1 */
    font-family:  "PingFang SC";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx; /* 133.333% */
}
.auth{
    color:  #3C9CFF;
    text-align: center;
    width: 100rpx;
    /* Medium/Caption1 */
    font-family: "PingFang SC";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx; /* 133.333% */
}
.mainBox{
    position: absolute;
    bottom: 30rpx;
    width: 750rpx;
}
.topBox{
    padding: 0 32rpx 32rpx;
    width: 750rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.location {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-right: 24rpx;
}

.refresh {
    width: 80rpx;
    height:80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.shop {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.icon {
    width:80rpx;
    height: 80rpx;
    z-index: 99;
}
.bottomBox{
    width: 686rpx;
    height:232rpx;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #FFF;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 32rpx;
}
.leftBox{
    width: 200rpx;
    height: 200rpx;
    flex-shrink: 0;
    border-radius: 12rpx;
    margin-left: 18rpx;
    margin-right: 24rpx;
}
.rightBox{
    flex:1;
    display: flex;
    flex-direction: row;
}
.leftMain{
    flex:1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.rightMain{
    width: 120rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    margin-left: 8rpx;
}
.title{
    overflow: hidden;
    color: #282A2E;
    font-feature-settings: 'case' on;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 40rpx; /* 133.333% */
    letter-spacing: -0.24px;
    overflow: hidden;
}
.address{
    color:  #53565C;
    font-family:  "PingFang SC";
    font-size:  24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx;
    height: 64rpx;
    overflow: hidden;
}
.time{
    color: #969696;
    font-feature-settings: 'case' on;
    font-family: "PingFang SC";
    font-size: 22rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 26rpx; /* 118.182% */
}
.desc{
    color:  #909399;
    text-align: center;
    font-feature-settings: 'case' on;
    font-family: "PingFang SC";
    font-size: 20rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 26rpx; /* 130% */
    margin-top: 8rpx;
    margin-right: 8rpx;
}
.nav{
    width: 68rpx;
    height: 68rpx;
    flex-shrink: 0;
}
.tel{
    width: 68rpx;
    height: 68rpx;
    flex-shrink: 0;
}
.iconImg{
    width: 68rpx;
    height: 68rpx;
}

</style>
