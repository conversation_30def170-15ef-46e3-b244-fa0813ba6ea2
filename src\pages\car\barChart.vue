<template>
    <u-navbar :is-back="true" :isFixed="true" title="充电">
        <template v-slot:right>
            <image class="rightIcon" src="../../static/imgs/car/date.svg" @tap="showDate"></image>
        </template>
    </u-navbar>
    <view class="listBox" style="margin-top: 32rpx">
        <view class="energy">
            <view class="energyItem borderTopRadius borderBottomRadius">
                <view class="energyLeft">
                    充电状态
                </view>
                <view class="energyWhite">
                    暂未充电
                </view>
            </view>
        </view>
    </view>
    <view class="batteryBox">
        <view class="batteryMain">
            <view class="batteryTitle">
                当前电量
            </view>
            <right-battery v-if="carInfo?.extInfo" :battery="Number(carInfo.extInfo.battery)"></right-battery>
        </view>
    </view>
    <view class="listBox">
        <mescroll-empty v-if="dataList.length == 0"></mescroll-empty>
        <view v-for="item in dataList" class="energy">
            <view class="energyTitle">
                {{ item.date }}
            </view>
            <view class="energyItem borderTopRadius">
                <view class="energyLeft">
                    充电量
                </view>
                <view class="energyRight">
                    {{ item.totalNum }}Wh
                </view>
            </view>
            <view class="energyItem borderBottomRadius">
                <view class="energyLeft">
                    电池电量
                </view>
                <view class="energyRight">
                    {{ item.totalTime}}%
                </view>
            </view>
        </view>
    </view>
    <u-popup v-model="show" border-radius="48" mode="bottom">
        <view class="maskBox">
            <view class="maskTitle">选择时间跨度</view>
            <view class="maskMain">
                <view class="maskDesc">
                    快捷选择
                </view>
                <view class="listBox">
                    <view :class="confirmType==0?'listItem active':'listItem'" @click="clickQuick(0)">全部</view>
                    <view :class="confirmType==1?'listItem active':'listItem'" @click="clickQuick(1)">当日</view>
                    <view :class="confirmType==2?'listItem active':'listItem'" @click="clickQuick(2)">3天</view>
                    <view :class="confirmType==3?'listItem active':'listItem'" @click="clickQuick(3)">7天</view>
                    <view :class="confirmType==4?'listItem active':'listItem'" @click="clickQuick(4)">30天</view>
                </view>
                <view class="maskDesc">
                    选择时间
                </view>
                <view class="timeBox">
                    <view :class="{'checkedBox':checkedBegin}" class="uncheckBox" @click="checkedDate(true)">
                        {{ defaultDate[0] }}
                    </view>
                    <view class="center"></view>
                    <view :class="{'checkedBox':!checkedBegin}" class="uncheckBox" @click="checkedDate(false)">
                        {{ defaultDate[1] }}
                    </view>
                </view>
            </view>
            <date-picker v-if="checkedBegin"
                         :default-date=defaultDate[0]
                         @onChange="onChange">
            </date-picker>
            <date-picker v-else
                         :default-date=defaultDate[1]
                         @onChange="onChange">
            </date-picker>
            <view class="btnBox">
                <u-button plain type="primary" @click="show=false">取消</u-button>
                <u-button type="primary" @click="searchLine">点击查询</u-button>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import {ref, watch} from 'vue'
import moment from "moment";
import {onShow} from "@dcloudio/uni-app";
import {getCarInfo, getDeviceCharge} from "@/services/device";
import {calcAdd} from "@/utils/math";
import RightBattery from "@/components/battery/right-battery.vue";
// 右上角按钮点击事件
const show = ref(false)
const checkedBegin = ref(true)
const defaultDate = ref(['2000-01-01', moment().format('YYYY-MM-DD')])
const getAll = ref(true)
const showDate = () => {
    show.value = true
}

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})
const confirmType: any = ref(0)
const clickQuick = (type: number) => {
    switch (type) {
        case 0://全部
            defaultDate.value = ['2000-01-01', moment().format('YYYY-MM-DD')]
            checkedBegin.value = true
            break
        case 1://当日
            defaultDate.value = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 2://3天
            defaultDate.value = [moment().subtract(3, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 3://7天
            defaultDate.value = [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
        case 4://30天
            defaultDate.value = [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            break
    }
}
watch(() => defaultDate.value, () => {
    let list = [
        ['2000-01-01', moment().format('YYYY-MM-DD')],
        [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(3, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    ]
    let num = 0
    for (let i = 0; i < list.length; i++) {
        if (list[i][0] === defaultDate.value[0] && list[i][1] === defaultDate.value[1]) {
            confirmType.value = i
            num++
        }
    }
    if (num == 0) {
        confirmType.value = 5
    }
}, {
    deep: true
})
// 打开日期选择框
const checkedDate = (isBegin: boolean) => {
    console.log('checkedDate', isBegin)
    checkedBegin.value = isBegin
}
// 点击确定按钮，返回当前选择的值
const onChange = (date: any) => {
    defaultDate.value[checkedBegin.value ? 0 : 1] = date
}


const searchLine = () => {
    let startTime = new Date(defaultDate.value[0]).getTime()
    let endTime = new Date(defaultDate.value[1]).getTime()
    if (startTime > endTime) {//如果开始日期大于结束日期,则将结束日期设置为开始日期
        filter.begin = defaultDate.value[1]
        filter.end = defaultDate.value[0]
    }
    filter.begin = defaultDate.value[0]
    filter.end = defaultDate.value[1]
    filter.deviceId = props.id
    show.value = false
    //获取数据
    getResetData()
}
const carInfo: any = ref({})
onShow(async () => {
    show.value = false
    filter.deviceId = props.id
    getResetData()
    let res = await getCarInfo(props.id)
    if (res.msg == 'success') {
        carInfo.value = res.result
    }
})
const filter = {
    deviceId: props.id,
    begin: defaultDate.value[0],
    end: defaultDate.value[1],
    page: 1,
    size: 10000 //todo 服务器没有汇总数据所以一次拉取10000条，后续接口修改在优化这里
}
const dataList = ref([])

/**
 * 获取筛选参数
 * todo 当前全部逻辑为2000-01-01 - now 所以不需要该方法，后面修在处理
 */
function getFilterValue() {
    if (getAll.value) {
        return {
            deviceId: filter.deviceId,
            page: filter.page,
            size: filter.size,
        }
    } else {
        return filter
    }
}

const getResetData = async () => {
    filterStatus.value = filter.begin == "2000-01-01" && filter.end == moment().format('YYYY-MM-DD')
    let res = await getDeviceCharge(filter)
    if (res.msg == "success") {
        let list: any = res.result.rows
        let dateList: any = []
        let totalNum = 0
        let totalMeter = 0
        let totalTime = 0
        console.log(list)
        for (let item of list) {
            let date = moment(item.startTime).format('YYYY-MM-DD')
            let index = dateList.findIndex((i: any) => i.date === date)
            // 根据distance和time  计算时速
            if (index === -1) {
                dateList.push({
                    date: date,
                    list: [item]
                })
            } else {
                dateList[index].list.push(item)
            }
        }
        for (let i in dateList) {
            let arr = dateList[i].list
            totalNum = 0
            totalMeter = 0
            totalTime = 0
            for (let n in arr) {
                const item = arr[n]
                totalNum = calcAdd(totalNum, item.chargeAmount/100)
                totalTime = calcAdd(totalTime, item.energy)
            }
            dateList[i].totalNum = totalNum
            dateList[i].totalMeter = totalMeter
            dateList[i].totalTime = totalTime
        }
        console.log(dateList)
        dataList.value = dateList
    }
}
const formatSecondsToHHMMSS = (seconds: any) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(secs).padStart(2, '0');
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
const filterStatus = ref(false)
</script>

<style lang="scss" scoped>
.rightIcon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 32rpx;
}

.maskBox {
    background: #FFFFFF;

    .maskTitle {
        margin-top: 48rpx;
        color: #4F4F4F;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 48rpx; /* 150% */
    }

    .maskMain {
        margin: 32rpx;
        padding: 24rpx;
        border-radius: 6px;
        background: #F7F7F7;

        .maskDesc {
            color: #969696;
            font-family: "PingFang SC";
            font-size: 28rpx;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 24rpx;
        }

        .listBox {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            padding: 0;

            .listItem {
                flex: 1;
                display: flex;
                height: 64rpx;
                justify-content: center;
                align-items: center;
                border-radius: 8rpx;
                border: 1px solid #F2F2F2;
                background: #FFF;
                color: #4F4F4F;
                text-align: center;
                font-family: "PingFang SC";
                font-size: 28rpx;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-bottom: 24rpx;
                margin-right: 10rpx;
            }
            .active {
                border: 1px solid #4B85FD;
            }
        }

        .timeBox {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .uncheckBox {
                flex: 1;
                height: 64rpx;
                flex-shrink: 0;
                border-radius: 8rpx;
                border: 2rpx solid #F2F2F2;
                background: #FFF;
                color: #969696;
                font-family: "PingFang SC";
                font-size: 28rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 36px; /* 128.571% */
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .center {
                width: 24rpx;
                height: 4rpx;
                flex-shrink: 0;
                background: #C3C3C3;
                margin: 0 12rpx;
            }

            .checkedBox {
                border: 2rpx solid #4B85FD;
                color: #282828;
                font-weight: 500;
            }
        }
    }

    .btnBox {
        display: flex;
        margin: 36rpx;

        .u-btn {
            flex: 1;
            margin: 0 32rpx;
        }
    }
}
.batteryBox{
    padding: 0 32rpx 32rpx;
    .batteryMain{
        padding: 24rpx;
        background: #ffffff;
        border-radius: 16rpx;
        .batteryTitle{
            color: var(--Text-tips, #909399);
            font-feature-settings: 'case' on;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption2, 22rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
            letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
            margin-bottom: 10rpx;
        }
    }

}

.listBox {
    padding: 0 32rpx 32rpx;

    .energy {
        .energyTitle {
            color: var(--Text-main, #282A2E);
            font-family: "PingFang SC";
            font-size: 32rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 42rpx; /* 131.25% */
            letter-spacing: -0.32px;
            margin-top: 48rpx;
            margin-bottom: 24rpx;
            text-indent: 10rpx;
        }

        .energyItem {
            display: flex;
            height: 112rpx;
            padding: 24rpx 32rpx;
            justify-content: space-between;
            align-items: center;
            align-self: stretch;
            background: #ffffff;

            .energyLeft {
                color: var(--Text-main, #282A2E);
                font-feature-settings: 'case' on;
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Body, 34rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
                letter-spacing: var(--Letter-Spacing-Body, -0.41px);
                text-align: left;
            }

            .energyRight {
                color: var(--Primary-text, #3C9CFF);
                font-feature-settings: 'case' on;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 40rpx; /* 133.333% */
                letter-spacing: -0.24px;
                text-align: right;
            }
            .energyWhite{
                color: var(--Text-tips, #909399);
                font-feature-settings: 'case' on;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 40rpx; /* 133.333% */
                letter-spacing: -0.24px;
                text-align: right;
            }
        }
    }
}
</style>
