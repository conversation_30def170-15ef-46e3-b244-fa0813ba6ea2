<script setup lang="ts">
import {useMemberStore} from "@/stores";
const memberStore = useMemberStore()
if(memberStore?.profile?.token){
    uni.switchTab({
        url: '/pages/car/car'
    })
}
const toLogin = () => {
    uni.navigateTo({
      url: '/pages/role/login'
    })
  }
</script>

<template>
<view class="main">
    <L-top-height></L-top-height>
    <view class="mainBox">
        <img class="textImg"  src="../../static/imgs/role/text.png" alt="">
        <view class="textBox">
            <view class="textA">欢迎使用 </view>
            <view class="textB">智能骑行App</view>
        </view>
        <view class="desc">
            智能出行，安全护航！
        </view>
        <view class="btnBox">
            <u-button type="primary"  @click="toLogin">登录</u-button>
        </view>
        <view class="mit">
            登录即代表同意
            <text> 《用户协议》</text>
            和
            <text>《服务与隐私协议》</text>
            ，并使用本机号码登录
        </view>
    </view>

</view>
</template>

<style scoped lang="scss">
.main{
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: url('../../static/imgs/role/bg.png')  no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    //.mainBox{
    //    flex:1;
    //    display: flex;
    //    justify-content: center;
    //    flex-direction: column;
    //}
    .textImg{
        margin-top: 120rpx;
        margin-left: 50rpx;
        height: 250rpx;
    }
    .textBox{
        margin-top: 32rpx;
        margin-left: 48rpx;
        color: var(--Text-white, #FFF);
        font-family: "PingFang SC";
        font-size: 64rpx;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }
    .desc{
        color: var(--Text-white, #FFF);
        /* Regular/Callout */
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Callout, 32rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Callout, 42rpx); /* 131.25% */
        letter-spacing: var(--Letter-Spacing-Callout, -0.32px);
        margin-top: 6rpx;
        margin-bottom: 195rpx;
        margin-left: 48rpx;
    }
    .btnBox{
        margin: 0 48rpx 96rpx;
        .mb-32{
            margin-bottom: 32rpx;
        }
    }
    .mit{
        margin: 0 48rpx;
        color: var(--Text-white, #FFF);
        text-align: center;
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Caption2, 22rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Caption2, 26rpx); /* 118.182% */
        letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
        text{
            color: var(--Primary-text, #3C9CFF);
            font-feature-settings: 'case' on;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Caption2, 22rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Caption2, 26rpx);
            letter-spacing: var(--Letter-Spacing-Caption2, 0.07px);
        }
    }
}
</style>