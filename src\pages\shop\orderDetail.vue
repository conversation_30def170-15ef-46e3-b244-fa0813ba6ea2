<script lang="ts" setup>
// tab列表
import {useCountDown} from '@/composables/useCountDown'
import {ref} from 'vue'
import moment from 'moment'
import {confirmOrderAPI, getOrderDetailAPI, refundOrderAPI, returnNumberAPI,} from '@/services/order'
import {onLoad, onShow} from '@dcloudio/uni-app'

const {formatTime, start} = useCountDown()
const query: any = ref({})
onLoad((options) => {
    query.value = options
})
const orderDetail: any = ref({})
onShow(() => getOrderDetailAction(query.value.id))
const calculateTimeDifferenceInSeconds = (targetDate: any) => {
    const now: any = new Date()
    const target: any = new Date(targetDate)
    const differenceInMilliseconds = target - now
    return Math.floor(differenceInMilliseconds / 1000)
}
const getOrderDetailAction = async (id: any) => {
    let res: any = await getOrderDetailAPI(id)
    console.log(res)
    if (res.msg == 'success') {
        orderDetail.value = res.result
        orderDetail.value.countdown = calculateTimeDifferenceInSeconds(res.result.payLatestTime)
        // 初始化倒计时秒数
        start(orderDetail.value.countdown)
    }
}
const shippingList: any = [
    {
        title: '圆通速递',
        dataIndex: 'yuantong',
    },
    {
        title: '中通快递',
        dataIndex: 'zhongtong',
    },
    {
        title: '韵达快递',
        dataIndex: 'yunda',
    },
    {
        title: '极兔速递',
        dataIndex: 'jtexpress',
    },
    {
        title: '申通快递',
        dataIndex: 'shentong',
    },
    {
        title: '邮政快递包裹',
        dataIndex: 'youzhengguonei',
    },
    {
        title: '顺丰速运',
        dataIndex: 'shunfeng',
    },
    {
        title: '京东物流',
        dataIndex: 'jd',
    },
    {
        title: 'EMS',
        dataIndex: 'ems',
    },
    {
        title: '邮政电商标快',
        dataIndex: 'youzhengdsbk',
    },
    {
        title: '德邦快递',
        dataIndex: 'debangkuaidi',
    },
    {
        title: '菜鸟速递',
        dataIndex: 'danniao',
    },
    {
        title: '顺丰快运',
        dataIndex: 'shunfengkuaiyun',
    },
    {
        title: '德邦物流',
        dataIndex: 'debangwuliu',
    },
    {
        title: '中通快运',
        dataIndex: 'zhongtongkuaiyun',
    },
    {
        title: '京东快运',
        dataIndex: 'jingdongkuaiyun',
    },
    {
        title: '速必达',
        dataIndex: 'subida',
    },
    {
        title: '安能快运',
        dataIndex: 'annengwuliu',
    },
    {
        title: '跨越速运',
        dataIndex: 'kuayue',
    },
]
const getShippingName = (name: any) => {
    //     获取name等于dataIndex的数组项
    if (name) {
        return shippingList.find((item: any) => item.dataIndex === name).title
    } else {
        return ''
    }
}

const preciseDivide = (num: number, divisor: number): string => {
    const result = BigInt(num * 100) / BigInt(divisor)
    return Number(result) / 100
}
const fixPrice = (price: any, num = 100) => {
    if (typeof price == 'number') {
        return preciseDivide(price, num)
    } else {
        return 0
    }
}
const add = (a: any, b: any) => {
    if (a > -1 && b > -1) {
        let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b))
        const sum = Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b
        return sum
    } else {
        return 0
    }
}
const toPay = (id: any) => {
    uni.navigateTo({url: '/pagesOrder/pay/pay?id=' + id})
}
const toPayEnd = (id: any) => {
    uni.navigateTo({url: '/pagesOrder/pay/payEnd?id=' + id})
}
const getStatusText = (order: any) => {
    let payState = order.status
    const stateMap: any = {
        1: '待支付',
        2: '待尾款',
        4: '待发货',
        5: '待收货',
        6: '待评价',
        7: '已完成',
        10: '售后',
        11: '已取消',
    }
    if (order.refundSupportId != null) {
        let str = ''
        if (order.refundSupport.status == 0) {
            str = '待审核'
        }
        if (order.refundSupport.status == 2) {
            str = '审核拒绝'
        }
        if (order.refundSupport.status == 3) {
            str = '审核通过'
        }
        if (order.refundSupport.status == 5) {
            str = '退款成功'
        }
        if (order.refundSupport.status == 7) {
            str = '退货退款'
        }
        if (order.refundSupport.status == 8) {
            str = '已退货'
        }
        return '售后 ' + str
    } else {
        return stateMap[payState]
    }
}
const reviewGood = (item: any) => {
    uni.setStorageSync('reviewGood', JSON.stringify(item))
    uni.navigateTo({url: '/pages/shop/review'})
}
const viewReply = (item: any) => {
    uni.setStorageSync('reviewGood', JSON.stringify(item))
    uni.navigateTo({url: '/pages/shop/review'})
}

const confirmOrderAction = async (id: any) => {
    uni.showModal({
        title: '提示',
        content: '是否确认收货？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        success: async (action: any) => {
            if (action.confirm) {
                // 确认收货
                let res: any = await confirmOrderAPI(id)
                if (res.msg == 'success') {
                    getOrderDetailAction(query.value.id)
                }
            }
        },
    })
}
const isRefund = ref(false)
const activeForm: any = ref({})
const showRefund = (item: any) => {
    isRefund.value = true
    activeForm.value = item
}
const closeRefund = () => {
    isRefund.value = false
}
const isRefundShipping = ref(false)
const showRefundShipping = (item: any) => {
    isRefundShipping.value = true
    activeForm.value = item
}
//  填写物流单号
const confirmRefundShipping = async () => {
    let mainInfo = {
        refundSupportId: activeForm.value.refundSupportId,
        returnTrackNumber: shippingNum.value,
        returnTrackCom: shippingCompany.value,
    }
    let res: any = await returnNumberAPI(mainInfo)
    console.log(res)
    if (res.msg == 'success') {
        getOrderDetailAction(query.value.id)
    }
}
const shippingNum = ref('')
const shippingCompany = ref('yuantong')
const confirmRefund = async () => {
    refundForm.value.videos = videoData.value
    refundForm.value.imgs = imgData.value
    refundForm.value.reason = reason.value
    refundForm.value.orderId = activeForm.value.id
    console.log(refundForm.value)
    let res: any = await refundOrderAPI(refundForm.value)
    if (res.msg == 'success') {
        getOrderDetailAction(query.value.id)
    }
}
const bindPickerChange = (e: any) => {
    index.value = e.detail.value
    shippingCompany.value = shippingList[e.detail.value].value
}
const index: any = ref(0)
const imgData: any = ref([])
const videoData: any = ref([])
const refundForm: any = ref({
    reason: '',
    imgs: [],
    videos: [],
})
const reason = ref('')
const imgSuccess = (res: any) => {
    imgData.value.push(res)
}
const videoSuccess = (res: any) => {
    videoData.value.push(res)
}

const toShipping = (id:any) => {
    if(id){
        uni.navigateTo({url: '/pages/shop/shipping?id=' + id})
    }
}
</script>

<template>
    <view>
        <neil-modal
            :show="isRefund"
            cancel-text="取消"
            confirm-text="确定"
            title="退款"
            @close="closeRefund"
            @confirm="confirmRefund"
        >
            <scroll-view class="scroll-Y" scroll-y="true" style="height: 600rpx;margin: 32rpx">
                <view class="formBox">
                    <label for="">退款理由</label>
                    <view class="content">
                        <textarea v-model="reason" placeholder="请输入退款理由"></textarea>
                    </view>
                </view>
                <view class="formBox">
                    <label for="">照片</label>
                    <view class="content">
                        <htz-image-upload
                            v-model="imgData"
                            :max="5"
                            action="https://assets.cutold.com/v1/imgs/upload"
                            name="photos"
                            @uploadSuccess="imgSuccess"
                        ></htz-image-upload>
                    </view>
                </view>
                <view class="formBox" style="margin-bottom: 0">
                    <label for="">视频</label>
                    <view class="content">
                        <htz-image-upload
                            v-model="videoData"
                            :max="5"
                            action="https://assets.cutold.com/v1/videos/upload"
                            mediaType="video"
                            name="videos"
                            @uploadSuccess="videoSuccess"
                        ></htz-image-upload>
                    </view>
                </view>
            </scroll-view>
        </neil-modal>
        <neil-modal
            :show="isRefundShipping"
            cancel-text="取消"
            confirm-text="确定"
            title="退货"
            @close="closeRefund"
            @confirm="confirmRefundShipping"
        >
            <view class="formBox">
                <label for="">退货地址</label>
                <view class="content">
                    <view v-if="activeForm">
                        {{ activeForm.refundSupport?.returnAddress }}
                    </view>
                </view>
            </view>
            <view class="formBox">
                <label for="">退货单号</label>
                <view class="content">
                    <input
                        v-model="shippingNum"
                        class="uni-input"
                        placeholder="请输入退货单号"
                        type="number"
                    />
                </view>
            </view>
            <view class="formBox">
                <label for="">退货物流</label>
                <view class="content">
                    <picker :range="shippingList" :value="index" range-key="label" @change="bindPickerChange">
                        <view class="uni-input">{{ shippingList[index].label }}</view>
                    </picker>
                </view>
            </view>
        </neil-modal>
        <view class="myRefund">
            <view class="refundBox">
                <view class="orderTitle">订单信息</view>
                <view class="orderFlex">
                    <view class="orderItemInfo">
                        <view class="orderLabel">订单编号</view>
                        <view class="orderValue">{{ orderDetail.id }}</view>
                    </view>
                    <view class="orderItemInfo">
                        <view class="orderLabel">下单时间</view>
                        <view class="orderValue">
                            {{ moment(orderDetail.created_at).format('YYYY-MM-DD HH:mm:ss') }}
                        </view>
                    </view>
                </view>
                <view class="orderFlex">
                    <view class="orderItemInfo">
                        <view class="orderLabel">支付方式</view>
                        <view class="orderValue">
                            <text v-if="orderDetail.status == 1">待支付</text>
                            <text v-else-if="orderDetail.status == 2">待支付尾款</text>
                            <text v-else>在线支付</text>
                        </view>
                    </view>
                    <view class="orderItemInfo">
                    </view>
                </view>
            </view>
            <view class="refundBoxShip">
                <view class="orderFlex">
                    <view class="orderFlexDirection">
                        <view class="orderTitle">收货人信息</view>
                        <view class="orderFlexDirection">
                            <view class="orderItem">
                                <view class="orderLabel">收货人</view>
                                <view class="orderValue">{{ orderDetail.receiverContact }}</view>
                            </view>
                            <view class="orderItem">
                                <view class="orderLabel">联系方式</view>
                                <view class="orderValue">{{ orderDetail.receiverMobile }}</view>
                            </view>
                            <view class="orderItem">
                                <view class="orderLabel">地址</view>
                                <view class="orderValue">{{ orderDetail.receiverAddress }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="orderFlexDirection">
                        <view class="orderTitle">物流信息</view>
                        <view class="orderFlexDirection">
                            <view class="orderItem">
                                <view class="orderLabel">配送方式</view>
                                <view class="orderValue">
                                    {{ getShippingName(orderDetail.trackCom) || '未发货' }}
                                </view>
                            </view>
                            <view class="orderItem" @click="toShipping(orderDetail.trackNumber)">
                                <view class="orderLabel">物流单号</view>
                                <view class="orderValue">{{ orderDetail.trackNumber || '未发货' }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="refundBoxPay">
                <view class="payTitle">付款信息</view>
                <view class="payBox">
                    <view class="payItem">
                        <view class="payLabel">商品总价</view>
                        <view class="payValue">¥{{ fixPrice(orderDetail.totalMoney) }}</view>
                    </view>
                    <view class="payItem">
                        <view class="payLabel">邮费</view>
                        <view class="payValue">¥{{ fixPrice(orderDetail.postFee) }}</view>
                    </view>
                    <view class="payItem">
                        <view class="payLabel rightText">订单合计</view>
                        <view class="payValue rightText">
                            ¥{{ add(fixPrice(orderDetail.totalMoney), fixPrice(orderDetail.postFee)) }}
                        </view>
                    </view>
                </view>
                <view v-if="orderDetail.status == 1 || orderDetail.status == 2" class="btnBox">
                    <view class="desc">应付订单金额</view>
                    <view class="price">
                        ¥{{ add(fixPrice(orderDetail.totalMoney), fixPrice(orderDetail.postFee)) }}
                    </view>
                    <view class="btn" @click="toPay(orderDetail.id)">支付剩余{{ formatTime }}</view>
                </view>

            </view>
            <view class="refundBoxGood">
                <view class="goodFlex">
                    <view class="shipping">
                        发货时间:下单后1-5天
                    </view>
                    <view class="statusText">{{ getStatusText(orderDetail) }}</view>
                </view>
                <view class="goodList">
                    <view v-for="item in orderDetail.orderGoods" class="goodItem">
                        <view style="display: flex">
                            <view class="imgBox">
                                <image :src="item.picture" alt=""/>
                            </view>
                            <view class="infoBox">
                                <view class="name">{{ item.name }}</view>
                                <view class="spec">
                                    <view v-for="info in item.specs">{{ info.name }}: {{ info.value }}</view>
                                </view>
                            </view>
                        </view>
                        <view style="display: flex;width: 100%;justify-content: space-between">
                            <view class="desc"> 单价</view>
                            <view class="count">
                                <view>¥{{ item.totalMoney / 100 }}</view>
                            </view>
                        </view>
                        <view style="display: flex;width: 100%;justify-content: space-between">
                            <view class="desc"> 数量</view>
                            <view class="count">
                                <view>{{ item.quantity }}</view>
                            </view>
                        </view>
                        <view v-if="orderDetail.status == 7"
                             style="display: flex;width: 100%;justify-content: space-between">
                            <view class="desc"> 评价</view>
                            <view class="orderID">
                                <view v-if="item.goodsReviewId == null" class="reply" @click="reviewGood(item)">
                                    评价商品
                                </view>
                                <view v-else class="view" @click="viewReply(item)">查看评价</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="orderDetail.status == 5" class="btnBox main">
                <view
                    v-if="orderDetail.refundSupportId == null"
                    class="btn"
                    @click="confirmOrderAction(orderDetail.id)"
                >
                    确认收货
                </view>
                <view
                    v-if="orderDetail.isPreSale == false && orderDetail.refundSupportId == null"
                    class="btn"
                    @click="showRefund(orderDetail)"
                >
                    售后服务
                </view>
                <view
                    v-if="orderDetail.isPreSale == false && orderDetail?.refundSupport?.status === 7"
                    class="btn"
                    @click="showRefundShipping(orderDetail)"
                >
                    退货单号
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped>
.reply {
    display: flex;
    padding: 8px 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid var(--Border-Border-Primary, #ededed);
    background: var(--Backgroung-Background-Primary, #f9f9f9);
    color: var(--Highlight, #ff4500);
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 21px */
    cursor: pointer;
}

.view {
    display: flex;
    padding: 8px 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid var(--Border-Border-Primary, #ededed);
    background: var(--Backgroung-Background-Primary, #f9f9f9);
    color: var(--Brand-Gray-Blue, #6a7d8c);
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 21px */
    cursor: pointer;
}

.goodFlex {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .shipping {
        color: var(--Normal-Black-333, #333);
        font-family: 'PingFang HK';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 21px */
    }

    .statusText {
        color: var(--Highlight, #ff4500);
        text-align: right;
        font-family: 'PingFang HK';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 21px */
    }
}

.goodList {
    width: 100%;

    .goodItem {
        width: 100%;
        display: flex;
        flex-direction: column;

        .imgBox {
            width: 100px;

            image {
                width: 100px;
                height: 100px;
            }
        }

        .infoBox {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 13px;

            .name {
                color: var(--Normal-Black-333, #333);
                font-family: 'PingFang HK';
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 150%; /* 21px */
            }

            .spec {
                color: var(--Normal-Black-666, #666);
                font-family: 'PingFang HK';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 100%; /* 14px */
                p {
                    margin-bottom: 5px;
                }
            }
        }

        .desc {
            color: var(--Normal-Gray-999, #999);
            text-align: left;
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 21px */
            margin-bottom: 12px;
        }

        .count {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 21px */
        }

        .orderID {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 21px */
        }

        .tabTitle {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 21px */
        }
    }
}

.payTitle {
    color: var(--Normal-Black, #000);
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%; /* 21px */
    margin-bottom: 16px;
}

.btnBox {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 38px;
    margin-bottom: 26px;

    .desc {
        color: var(--Normal-Gray-999, #999);
        text-align: right;
        font-family: 'PingFang HK';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 21px */
    }

    .price {
        color: var(--Brand-Green-Primary, #3C9CFF);
        text-align: right;
        font-family: 'PingFang HK';
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 30px */
        margin: 0 12px;
    }

    .btn {
        display: flex;
        width: 240px;
        padding: 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 8px;
        background: var(--Brand-Green-Primary, #3C9CFF);
        color: var(--Normal-White, #fff);
        text-align: center;
        font-family: 'PingFang HK';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 14px */
        margin: 0 10px;
    }
}

.main {
    margin-bottom: 26rpx;
    padding-bottom: 30rpx;
}

.mainCenter {
    display: flex;
    justify-content: center;
    align-items: center;
}

.payBox {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;

    .payItem {
        display: flex;
        justify-content: space-between;

        .payLabel {
            color: var(--Normal-Gray-999, #999);
            text-align: left;
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 21px */
            margin-bottom: 12px;
            margin-right: 24rpx;

            .redText {
                color: var(--Highlight, #ff4500);
                text-align: right;
                font-family: 'PingFang HK';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 150%; /* 21px */
            }

            .infoText {
                color: var(--Normal-Gray-999, #999);
                font-family: 'PingFang HK';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 150%; /* 21px */
            }
        }

        .payValue {
            color: var(--Normal-Black-333, #333);
            text-align: left;
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 21px */
        }

        .rightText {
            text-align: right;
        }
    }
}

.title {
    width: 100%;
    height: 88px;
    background: #f2f2f2;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-text {
        color: #333;
        font-family: 'PingFang HK';
        font-size: 32px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 48px */
    }
}

.orderTitle {
    color: var(--Normal-Black, #000);
    font-family: 'PingFang HK';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%; /* 21px */
    margin-bottom: 16px;
}

.orderFlex {
    width: 100%;
    display: flex;
    flex-direction: column;

    .orderItemInfo {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;

        .orderLabel {
            width: 64px;
            margin-right: 12px;
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 21px */
        }

        .orderValue {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 21px */
        }
    }
}

.orderFlexDirection {
    flex: 1;
    display: flex;
    flex-direction: column;

    .orderItem {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;

        .orderLabel {
            width: 64px;
            margin-right: 12px;
            color: var(--Normal-Gray-999, #999);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 21px */
        }

        .orderValue {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang HK';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 21px */
        }
    }
}

.myRefund {
    min-height: 747px;

    .refundBox {
        display: flex;
        padding: 24px 24px 0;
        margin: 32rpx;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        background: #fff;
    }

    .refundBoxShip {
        margin: 0 32rpx 32rpx;
        display: flex;
        padding: 24px 24px 8px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        background: #fff;
    }

    .refundBoxPay {
        margin: 0 32rpx 32rpx;
        display: flex;
        padding: 24px 24px 8px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        background: #fff;
    }

    .refundBoxGood {
        display: flex;
        padding: 24px 24px 8px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        background: #fff;
        margin: 0 32rpx 32rpx;
    }
}
textarea{
    width: 470rpx;
font-size: 24rpx;
    border: 1px solid #ededed;
    border-radius: 24rpx;
    padding: 24rpx;
    margin: 10px 0;
 }
</style>
