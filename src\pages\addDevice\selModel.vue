<template>
    <view class="listBox">
        <view class="listItem" @tap="toSelModel">
            <view class="brandImg">
                <image class="brand" src="../../static/imgs/addDevice/plus.svg" mode="aspectFit"></image>
            </view>
            <view class="brandText">绿源</view>
        </view>
    </view>
    <view class="modelBox" v-for="item in modelList">
        <view class="modelTitle">{{ item.category }} 系列</view>
        <view class="carBox">
            <view class="carItem"  v-for="car in item.list" @click="toCode(car)">
                <image class="carImg" :src="car.img" mode="aspectFit"></image>
                <view class="carText">{{ car.model }}</view>
            </view>
        </view>
    </view>
    <view style="height:1rpx"></view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {useCarStore} from '@/stores'
import {onMounted} from "vue";
import {getModelAPI} from "@/services/device";
const carStore = useCarStore()
const props = defineProps({
    id:{
        type:String,
        default:''
    }
})
const modelList:any = ref([])
const getModelList = async () => {
    let res =  await getModelAPI({brandId:props.id,page:1,size:1000})
console.log(res)
    if(res.msg=='success'){
        let arr = res.result.rows
        let list = []
        for (let i = 0; i < arr.length; i++) {
            let item = arr[i]
            let index = list.findIndex((i:any) => i.category === item.category)
            if(index==-1){
                list.push({category:item.category,list:[item]})
            }else{
                list[index].list.push(item)
            }
        }
        modelList.value = list
    }
}
onMounted(()=>{
    getModelList()
})
const toCode = (car:any) => {
    let obj =  Object.assign(carStore.carInfo, {model:car.model,img:car.img,modelID:car.id})
    // 合并对象
    carStore.setCarInfo(obj)
    console.log(obj,car)
    uni.navigateBack({
       delta:1
    })
}
</script>

<style lang="scss" scoped>
.modelTitle{
    padding: 30rpx;
    color: var(--Text-main, #282A2E);
    /* Medium/Callout */
    font-family: "PingFang SC";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 42rpx; /* 131.25% */
    letter-spacing: -0.32px;
}
.carBox{
    padding: 0 32rpx;
    .carItem{
        padding: 26rpx 24rpx;
        margin-bottom: 32rpx;
        background: #FFFFFF;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 16rpx;
        .carImg{
            height:116rpx ;
            width: 186rpx;
        }
        .carText{
            margin-left: 24rpx;
            flex: 1;
            color: #000;
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Headline, 34rpx);
            font-style: normal;
            font-weight: 600;
            line-height: var(--Line-Height-Headline, 44rpx); /* 129.412% */
            letter-spacing: var(--Letter-Spacing-Headline, -0.41px);
        }
    }

}
.listBox{
    padding: 16rpx 32rpx;
    background: #FFFFFF;
    .listItem{
        padding: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .brandImg{
            width: 112rpx;
            height: 112rpx;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12rpx;
            background: var(--Background-layout, #F5F5F5);
            .brand{
                width: 40rpx;
                height: 40rpx;
            }
        }
        .brandText{
            flex: 1;
            margin: 0 28rpx;
            color: #000;

            /* Medium/Headline */
            font-family: "PingFang SC";
            font-size: 34rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 44rpx; /* 129.412% */
            letter-spacing: -0.408px;
        }
    }
}
</style>
