<script lang="ts" setup>
import {useGuessList} from '@/composables'
import {addCartQuantity, deleteMemberCartAPI, getMemberCartAPI} from '@/services/cart'
import {useMemberStore} from '@/stores'
import {onShow} from '@dcloudio/uni-app'
import {computed, ref} from 'vue'
import lxcCount from '@/components/lxc-count/lxc-count.vue'
import {calcAdd, calcDiv, calcMult} from '@/composables/math'
// 是否适配底部安全区域
defineProps<{
    safeAreaInsetBottom?: boolean
}>()

// 获取屏幕边界到安全区域距离
const {safeAreaInsets} = uni.getSystemInfoSync()

// 获取会员Store
const memberStore = useMemberStore()

// 获取购物车数据
const cartList: any = ref<any>([])
// 优化购物车空列表状态，默认展示列表
const showCartList = ref(true)
const getMemberCartData = async () => {
    const res: any = await getMemberCartAPI()
    //  res.result.rows  添加属性selected
    let list = res.result.rows
    list.forEach((item: any) => {
        item.selected = true
    })
    let listA: any = [
        {
            id: '',
            name: '',
            list: [],
        },
    ]
    // 根据数组list的goods的designer的name为主键拆分为二维数组
    for (let i in list) {
        let info: any = list[i]
        listA[0].list.push(info)
    }
    console.log(listA, 999)
    // 检查listA的list为空就删除
    listA = listA.filter((v: any) => v.list.length > 0)
    cartList.value = listA
    showCartList.value = res.result.count > 0
}

// 初始化调用: 页面显示触发
onShow(() => {
    uni.setStorageSync('selCart',null)
    if (memberStore.profile) {
        getMemberCartData()
    }
})
// 去首页
const navigateToIndex = () => {
    uni.switchTab({
        url: '/pages/shop/shop',
    })
}
onShow(() => {
    uni.setStorageSync('selCart',JSON.stringify([]))
})
// 结算按钮
const gotoPayment = () => {
    if (allCount.value === 0) {
        return uni.showToast({
            icon: 'none',
            title: '请选择商品',
        })
    }
    let list = []
    let listBasic = cartList.value
    for (let i in listBasic) {
        let arr = listBasic[i].list
        for (let n in arr) {
            if (arr[n].selected) {
                list.push(arr[n])
            }
        }
    }
    console.log(list)
    uni.setStorageSync('selCart',JSON.stringify(list))
    // 跳转到结算页
    uni.navigateTo({
        url: '/pages/shop/createOrder?type=cart',
    })
}
const info = computed(() => {
    let obj = {
        allPrice: 0,
    }
    let list = []
    let listBasic = cartList.value
    for (let i in listBasic) {
        let arr = listBasic[i].list
        for (let n in arr) {
            if (arr[n].selected) {
                list.push(arr[n])
            }
        }
    }
    console.log(list)
    let sum = 0
    for (const item of list) {
        let multNum = calcMult(item.quantity, item.sku.price)
        sum = calcAdd(sum, multNum)
    }
    obj.allPrice = calcDiv(sum, 100)
    return obj
})
const add = (a, b) => {
    let epsilon = Number.EPSILON * Math.max(Math.abs(a), Math.abs(b))
    const sum = Math.abs(a + b - Math.round(a + b)) < epsilon ? Math.round(a + b) : a + b
    return sum
}
// 猜你喜欢
const {guessRef, onScrolltolower} = useGuessList()
const handleCountClick = async (val, index, num) => {
    let list = JSON.parse(JSON.stringify(cartList.value))
    console.log(list, list[num].list[index], index, num)
    cartList.value[num].list[index]['quantity'] = val
    await addCartQuantity(cartList.value[num].list[index].id, {quantity: val})
}
const onChangeSelected = (item: any) => {
    // 前端数据更新-是否选中取反
    item.selected = !item.selected
}
// 修改选中状态-全选修改
const onChangeSelectedAll = () => {
    // 全选状态取反
    const _isSelectedAll = !isSelectedAll.value
    // 前端数据更新
    cartList.value.forEach((item: any) => {
        item.list.forEach((info: any) => {
            info.selected = _isSelectedAll
        })
    })
}
// 计算全选状态
const isSelectedAll = computed(() => {
    let arr = []
    let list = cartList.value
    for (let i in list) {
        arr.push({flag: list[i].list.every((v: any) => v.selected)})
    }
    return arr.every((v: any) => v.flag)
})
const maxFee = computed(() => {
    let selectedCartList = []
    let listBasic = cartList.value
    for (let i in listBasic) {
        let arr = listBasic[i].list
        for (let n in arr) {
            if (arr[n].selected) {
                selectedCartList.push(arr[n])
            }
        }
    }
    let arr = selectedCartList.map((item: any) => item.sku.shipping.amount / 100)
    console.log(arr)
    if (arr.length == 0) {
        return 0
    } else {
        return Math.max(...arr)
    }

})
const allCount = computed(() => {
    let count = 0
    let list = cartList.value
    for (let i in list) {
        let arr = list[i].list
        for (let n in arr) {
            if (arr[n].selected) {
                count += arr[n].quantity
            }
        }
    }
    return count
})
const toPage = (url: any) => {
    uni.navigateTo({
        url: url,
    })
}
const manageStatus = ref(false)
const changeStatus = () => {
    manageStatus.value = !manageStatus.value
}
const delCart = () => {
    let selectedCartList: any = []
    let list = cartList.value
    for (let i in list) {
        let arr = list[i].list
        for (let n in arr) {
            if (arr[n].selected) {
                selectedCartList.push(arr[n])
            }
        }
    }
    uni.showModal({
        content: '是否删除',
        confirmColor: '#3C9CFF',
        success: async (res) => {
            if (res.confirm) {
                // 后端删除单品
                selectedCartList.forEach(async (info: any) => {
                    await deleteMemberCartAPI(info.id)
                })
                // 重新获取列表
                getMemberCartData()
            }
        },
    })
}
</script>

<template>
    <u-navbar :is-back="true" title="购物车">
        <template v-slot:right>
            <view :class="manageStatus ? 'label active' : 'label'" @click="changeStatus">
                {{ manageStatus ? '完成' : '管理' }}
            </view>
        </template>
    </u-navbar>
    <scroll-view class="scroll-view" enable-back-to-top scroll-y @scrolltolower="onScrolltolower">
            <!-- 购物车列表 -->
            <view v-if="showCartList" class="goodMain">
                <template v-for="(info, num) in cartList">
                    <view style="margin-bottom: 20rpx">
                        <!-- 滑动操作项 -->
                        <view v-for="(item, index) in info.list" :key="item.skuId" class="cart-swipe">
                            <!-- 商品信息 -->
                            <view class="goods">
                                <view class="navigator" hover-class="none">
                                    <text
                                        :class="{ checked: item.selected }"
                                        class="checkbox"
                                        @tap.stop="onChangeSelected(item)"
                                    ></text>
                                    <image :src="item.sku.picture" class="picture" mode="aspectFill"></image>
                                    <view class="meta" @click="toPage(`/pages/shop/detail?id=${item.goodsId}`)">
                                        <view class="name ellipsis">{{ item.goods.name }}</view>
                                        <view class="attrsText ellipsis">
                                            <text v-for="spec in item.sku.specs">
                                                {{ spec.value }}
                                            </text>
                                        </view>
                                        <view class="price">
                                            {{ (item.sku.price / 100).toFixed(2) }}
                                            <view class="count">
                                                <lxc-count
                                                    :delayed="1"
                                                    :index="item.skuId"
                                                    :max="item.sku.inventory"
                                                    :min="1"
                                                    :value="item.quantity"
                                                    @handleCount="handleCountClick($event, index, num)"
                                                >
                                                </lxc-count>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
            </view>
            <!-- 购物车空状态 -->
            <view v-else class="cart-blank">
                <mescroll-empty ></mescroll-empty>
                <button class="button" @click="navigateToIndex">去商城首页</button>
            </view>
            <!-- 吸底工具栏 -->
            <view
                v-if="showCartList"
                :style="{ paddingBottom: safeAreaInsetBottom ? safeAreaInsets?.bottom + 'px' : 0 }"
                class="toolbar"
            >
                <text :class="{ checked: isSelectedAll }" class="all" @tap="onChangeSelectedAll">全选</text>
                <view class="priceBox">
                    <view class="priceAll">
                        <text v-if="manageStatus == false" class="text">合计:</text>
                        <text v-if="manageStatus == false" class="amount">{{ add(info.allPrice, maxFee) }}</text>
                    </view>
                    <view class="priceDetail">运费:¥{{ maxFee }} 总价:¥{{ info.allPrice }}</view>
                </view>

                <view class="button-grounp">
                    <view
                        v-if="manageStatus == false"
                        :class="{ disabled: allCount === 0 }"
                        class="button payment-button"
                        @tap="gotoPayment"
                    >
                        结算 ({{ allCount }})
                    </view>
                    <view
                        v-if="manageStatus == true"
                        :class="{ disabled: allCount === 0 }"
                        class="button payment-button"
                        @tap="delCart"
                    >
                        删除
                    </view>
                </view>
            </view>

        <!-- 猜你喜欢 -->
        <!--    <XtxGuess ref="guessRef" />-->
        <!-- 底部占位空盒子 -->
        <!-- 底部占位空盒子 -->
        <view class="toolbar-height"></view>
    </scroll-view>
</template>

<style lang="scss">
// 根元素
:host {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f7f7f8;
}

.checkbox {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 100%;
    color: var(--Normal-Black-333, #333);
    font-family: 'PingFang HK';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 24px */

    &::before {
        content: '\e6cd';
        font-family: 'erabbit' !important;
        font-size: 40rpx;
        color: #444;
    }

    &.checked::before {
        content: '\e6cc';
        color: #3C9CFF;
    }
}

// 滚动容器
.scroll-view {
    flex: 1;
    background-color: #f7f7f8;
}

// 优惠提示


.label {
    color: #000;
    font-size: 24rpx;
    margin-right: 40rpx;
}

.active {
    color: #3C9CFF;
    font-size: 24rpx;
    margin-right: 40rpx;
}

// 购物车列表
.goodMain {
    margin: 36rpx;

    .cart-title {
        height: 88rpx;
        padding-left: 24rpx;
        flex-shrink: 0;
        border-radius: 12rpx 12rpx 0rpx 0rpx;
        border-top: 1px solid var(--Border-Border-Primary, #ededed);
        border-right: 1px solid var(--Border-Border-Primary, #ededed);
        border-left: 1px solid var(--Border-Border-Primary, #ededed);
        background: var(--Normal-White, #fff);
        color: var(--Brand-Green-Primary, #3C9CFF);
        font-family: 'PingFang HK';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 88rpx; /* 24px */
    }

    // 购物车商品
    .goods {
        display: flex;
        padding: 20rpx 20rpx 20rpx 80rpx;
        border-radius: 10rpx;
        background-color: #fff;
        position: relative;

        .navigator {
            width: 100%;
            display: flex;
        }

        .checkbox {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80rpx;
            height: 100%;

            &::before {
                content: '\e6cd';
                font-family: 'erabbit' !important;
                font-size: 32rpx;
                color: #ededed;
            }

            &.checked::before {
                content: '\e6cc';
                color: #3C9CFF;
            }
        }

        .picture {
            width: 128rpx;
            height: 128rpx;
        }

        .meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 20rpx;
        }

        .name {
            color: var(--Normal-Black-333, #333);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 24px */
        }

        .attrsText {
            color: var(--Normal-Black-666, #666);
            font-family: 'PingFang SC';
            font-size: 22rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 22px */
        }

        .price {
            width: 100%;
            color: var(--Brand-Green-Primary, #3C9CFF);
            font-family: 'PingFang HK';
            font-size: 32rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 32px */
            display: flex;
            align-items: center;

            &::before {
                content: '￥';
                color: var(--Brand-Green-Primary, #3C9CFF);
                font-family: 'PingFang HK';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 120%; /* 28.8px */
            }

            .count {
                flex: 1;
                display: flex;
                justify-content: flex-end;
            }
        }
    }

    .cart-swipe {
    }

    .cart-swipe-right {
        display: flex;
        height: 100%;

        .button {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            padding: 6px;
            line-height: 1.5;
            color: #fff;
            font-size: 26rpx;
            border-radius: 0;
        }

        .delete-button {
            background-color: #cf4444;
        }
    }
}

// 空状态
.cart-blank,
.login-blank {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 60vh;

    .image {
        width: 400rpx;
        height: 281rpx;
    }

    .text {
        color: #444;
        font-size: 26rpx;
        margin: 20rpx 0;
    }

    .button {
        width: 240rpx !important;
        height: 60rpx;
        line-height: 60rpx;
        margin-top: 20rpx;
        font-size: 26rpx;
        border-radius: 12rpx;
        color: #fff;
        background-color: #3C9CFF;
    }
}

// 吸底工具栏
.toolbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: calc(var(--window-bottom));
    z-index: 1;

    height: 100rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    border-top: 1rpx solid #ededed;
    border-bottom: 1rpx solid #ededed;
    background-color: #fff;
    box-sizing: content-box;

    .all {
        margin-left: 25rpx;
        color: var(--Normal-Black-333, #333);
        font-family: 'PingFang HK';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 100%; /* 24px */
        position: relative;
        padding-left: 20rpx;
    }

    .all::before {
        font-family: 'erabbit' !important;
        content: '\e6cd';
        font-size: 32rpx;
        position: absolute;
        left: -20rpx;
        color: #ededed;
    }

    .checked::before {
        content: '\e6cc';
        color: #3C9CFF;
    }

    .priceBox {
        flex: 1;

        .priceAll {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .text {
                margin-right: 8rpx;
                margin-left: 32rpx;

                color: #000;
                font-family: 'PingFang HK';
                font-size: 24rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 100%; /* 24px */
            }

            .amount {
                .decimal {
                    color: #000;
                    font-family: 'PingFang HK';
                    font-size: 32rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 100%;
                }

                &::before {
                    content: '￥';
                    color: #000;
                    font-family: 'PingFang HK';
                    font-size: 32rpx;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 100%;
                }
            }
        }
    }

    .priceDetail {
        color: var(--Normal-Black-666, #666);
        font-family: 'PingFang HK';
        font-size: 22rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 22px */
        text-align: right;
    }

    .button-grounp {
        margin-left: auto;
        display: flex;
        justify-content: space-between;
        text-align: center;
        line-height: 72rpx;
        font-size: 13px;
        color: #fff;

        .button {
            width: 240rpx;
            margin: 0 10rpx;
            border-radius: 72rpx;
        }

        .payment-button {
            display: flex;
            width: 164rpx;
            padding: 17rpx 0;
            justify-content: center;
            align-items: center;
            gap: 8rpx;
            flex-shrink: 0;
            align-self: stretch;
            border-radius: 16rpx;
            border: var(--stroke-weight-1, 1px) solid var(--Brand-Green-Primary, #3C9CFF);
            background: var(--Brand-Green-Primary, #3C9CFF);
            color: var(--Normal-White, #fff);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 100%; /* 24px */
            &.disabled {
                opacity: 0.6;
            }
        }
    }
}

// 底部占位空盒子
.toolbar-height {
    height: 100rpx;
}
</style>
