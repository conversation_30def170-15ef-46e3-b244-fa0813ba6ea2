<script lang="ts" setup>
import {computed, ref} from 'vue'
import {codeLoginAPI, getCodeAPI, loginAPI} from '@/services/login'
import {useMemberStore} from "@/stores";
import CryptoJS from 'crypto-js';
const memberStore = useMemberStore()
const form = ref({
    phone: '',
    code: '',
    password: '',
})
const checkPhone = ref(true)
const isSending = ref(false)
const countdown = ref(60)
const timer: any = ref(null)
// 发送验证码
const sendVerificationCode = async () => {
    if (isSending.value) return
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(form.value.phone)) {
        uni.showToast({icon: 'none', title: '请输入有效的手机号'})
        return
    }

    isSending.value = true
    let res = await getCodeAPI({tel: form.value.phone})
    if (res.msg == 'success') {
        uni.showToast({icon: 'none', title: '验证码已发送到:' + form.value.phone})
        timer.value = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
                clearInterval(timer.value)
                isSending.value = false
                countdown.value = 60
            }
        }, 1000)
    }


}
//  状态动态计算是否都填写
const status = computed(() => {
    return form.value.phone && ((checkPhone.value && form.value.code) || (!checkPhone.value && form.value.password))
})
//  提交登录
const submit = async () => {
    let res: any
    if (checkPhone.value) {
        res = await codeLoginAPI({tel: form.value.phone, code: form.value.code})
    } else {
        res = await loginAPI({tel: form.value.phone, password: CryptoJS.MD5(form.value.password).toString()})
    }
    console.log('login:', res)
    if (res.msg == 'success') {
        memberStore.setProfile(res.result)
        uni.removeStorageSync('carId')
        uni.switchTab({
            url: '/pages/car/car'
        })
    }

}
//  切换登录方式
const switchLoginType = (phoneLogin: boolean) => {
    checkPhone.value = phoneLogin
    //  切换重装页面表单
    form.value = {
        phone: '',
        code: '',
        password: '',
    }
}
// 忘记密码
const toForget = () => {
    uni.navigateTo({
        url: '/pages/role/forget'
    })
}
</script>

<template>
    <view class="content">
        <L-top-height></L-top-height>
        <L-top-close></L-top-close>
        <view class="contentBox">
            <text :class="checkPhone?'textCheck':'textUnCheck'" @click="switchLoginType(true)">手机号登录</text>
            <text :class="checkPhone?'textUnCheck':'textCheck'" style="margin-left: 48rpx"
                  @click="switchLoginType(false)">
                密码登录
            </text>
            <view class="phoneBox">
                <u-input v-model="form.phone" :border="false" :clearable="false" :maxlength="11"
                         placeholder="请输入手机号"
                         type="number"/>
            </view>
            <view v-show="checkPhone" class="codeBox">
                <view class="codeInputBox">
                    <u-input v-model="form.code" maxlength="6" :border="false" :clearable="false" placeholder="请输入验证码"
                             type="number"/>
                </view>
                <view class="codeBtnBox">
                    <u-button :disabled="isSending" plain="isSending" type="primary" @click="sendVerificationCode">
                        {{ isSending ? `${countdown}秒后重发` : '获取验证码' }}
                    </u-button>
                </view>
            </view>
            <view v-show="!checkPhone" class="passBox">
                <u-input v-model="form.password" :border="false" :clearable="false" placeholder="请输入密码"
                         type="password"/>
            </view>
            <view class="loginBox">
                <u-button :disabled="!status" type="primary" @click="submit">登录</u-button>
            </view>
            <view v-show="!checkPhone" class="forgetPass" @click="toForget">忘记密码</view>
        </view>
        <view>
            <!-- <view class="otherLogin">其他登录方式</view> -->
<!--            <view class="iconBox">-->
<!--                <image class="otherIcon" src="../../static/imgs/role/weixin_circle.svg"></image>-->
<!--                <image class="otherIcon" src="../../static/imgs/role/qq_circle.svg"></image>-->
<!--            </view>-->
            <view class="agreementBox">
                <text class="plainText">登录即代表同意</text>
                <text class="agreementText">《用户协议》</text>
                <text class="plainText">和</text>
                <text class="agreementText">《服务与隐私协议》</text>
                <text class="plainText">，并使用本机号码登录</text>
            </view>
        </view>
    </view>

</template>

<style lang="scss" scoped>
.content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .contentBox {
        flex: 1;
        padding: 32rpx;

        .textCheck {
            color: #282A2E;
            /* Medium/Title3 */
            font-family: "PingFang SC";
            font-size: 56rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 68rpx; /* 121.429% */
            letter-spacing: 0.364px;
        }

        .textUnCheck {
            color: #909399;
            /* Regular/Title1 */
            font-family: "PingFang SC";
            font-size: 56rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 68rpx; /* 121.429% */
            letter-spacing: 0.36px;
        }

        .phoneBox {
            display: flex;
            width: 686rpx;
            height: 96rpx;
            padding: 24rpx;
            align-items: center;
            gap: 8rpx;
            flex-shrink: 0;
            border-radius: 8rpx;
            border: 2rpx solid #E6E6E8;
            margin-top: 112rpx;

            :deep(input) {
                flex: 1 0 0;
                font-feature-settings: 'case' on;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 40rpx; /* 133.333% */
                letter-spacing: -0.24px;
            }
        }

        .codeBox {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30rpx;

            .codeInputBox {
                display: flex;
                width: 400rpx;
                height: 96rpx;
                padding: 0px 24rpx;
                align-items: center;
                gap: 8rpx;
                flex-shrink: 0;
                border-radius: 8rpx;
                border: 2rpx solid #E6E6E8;

                :deep(input) {
                    flex: 1 0 0;
                    font-feature-settings: 'case' on;
                    font-family: "PingFang SC";
                    font-size: 30rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 40rpx; /* 133.333% */
                    letter-spacing: -0.24px;
                }
            }

            .codeBtnBox {
                display: flex;
                width: 256rpx;
                height: 96rpx;
                justify-content: center;
                align-items: center;
                gap: 16rpx;
                flex-shrink: 0;
                border-radius: 6rpx;

                :deep(button) {
                    width: 100%;
                    height: 100%;
                    font-feature-settings: 'case' on;
                    /* Regular/Body */
                    font-family: "PingFang SC";
                    font-size: 34rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 44rpx; /* 129.412% */
                    letter-spacing: -0.41px;
                }
            }
        }

        .passBox {
            display: flex;
            width: 686rpx;
            height: 96rpx;
            padding: 0px 24rpx;
            align-items: center;
            gap: 8rpx;
            flex-shrink: 0;
            margin-top: 30rpx;
            border-radius: 8rpx;
            border: 2rpx solid #E6E6E8;

            :deep(input) {
                flex: 1 0 0;
                font-feature-settings: 'case' on;
                font-family: "PingFang SC";
                font-size: 30rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 40rpx; /* 133.333% */
                letter-spacing: -0.24px;
            }
        }

        .loginBox {
            display: flex;
            width: 686rpx;
            height: 96rpx;
            justify-content: center;
            align-items: center;
            gap: 16rpx;
            flex-shrink: 0;
            margin-top: 30rpx;

            :deep(button) {
                width: 100%;
                height: 100%;
                font-feature-settings: 'case' on;
                /* Regular/Body */
                font-family: "PingFang SC";
                font-size: 34rpx;
                font-style: normal;
                font-weight: 400;
                line-height: 44rpx; /* 129.412% */
                letter-spacing: -0.41px;
            }
        }

        .forgetPass {
            text-align: end;
            color: #53565C;
            font-feature-settings: 'case' on;

            /* Regular/Caption2 */
            font-family: "PingFang SC";
            font-size: 22rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 26rpx; /* 118.182% */
            letter-spacing: 0.07px;
            margin-top: 30rpx;
        }
    }

    .otherLogin {
        align-items: center;
        text-align: center;
        color: #909399;
        font-feature-settings: 'case' on;
        //字体居中
        /* Regular/Caption2 */
        font-family: "PingFang SC";
        font-size: 22rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 26rpx; /* 118.182% */
        letter-spacing: 0.07px;
    }

    .iconBox {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 48rpx;

        .otherIcon {
            width: 64rpx;
            height: 64rpx;
            margin-left: 35rpx;
            margin-right: 35rpx;
            flex-shrink: 0;
        }
    }

    .agreementBox {
        margin: 48rpx 52rpx 40rpx;
        text-align: center;

        .plainText {
            color: #909399;
            font-feature-settings: 'case' on;
            /* Regular/Caption2 */
            font-family: "PingFang SC";
            font-size: 22rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 26rpx; /* 118.182% */
            letter-spacing: 0.07px;
        }

        .agreementText {
            color: #3C9CFF;
            font-feature-settings: 'case' on;
            /* Regular/Caption2 */
            font-family: "PingFang SC";
            font-size: 22rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 26rpx;
            letter-spacing: 0.07px;
        }
    }

}

</style>