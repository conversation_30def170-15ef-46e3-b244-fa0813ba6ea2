<script lang="ts" setup>
import UButton from "@/uni_modules/vk-uview-ui/components/u-button/u-button.vue";

const props = defineProps({
    status: {
        type: [String, Number],
        default: 0
    }
})

const resultCode = Number(props.status)

console.log(resultCode)
//0绑定成功 1 绑定失败 2 设备已绑定 3 二维码绑定失败
const resultType = () => {
    switch (resultCode) {
        case 0:
            return '绑定成功'
        case 1:
            return '绑定失败'
        case 2:
            return '设备已绑定'
        case 3:
            return '二维码绑定失败'
        default:
            return '未知状态'
    }
}

const msg = () => {
    switch (resultCode) {
        case 0:
            return "该设备已经绑定至其他账户或设备。请确认是否需要解绑当前账户后重新绑定。\n如果仍无法绑定，请检查设备状态或联系客服获得进一步支持。"
        case 1:
            return "请检查SN码是否正确，再次尝试。\nSN码对应的设备已被绑定。\n如果仍无法绑定，请检查设备状态或联系客服获得进一步支持。"
        case 2:
            return "·该设备已经绑定至其他账户或设备。请确认是否需要解绑当前账户后重新绑定。\n如果仍无法绑定，请检查设备状态或联系客服获得进一步支持。"
        case 3:
            return "· 二维码是否清晰可见。\n· 手机摄像头是否正常。"
        default:
            return '未知状态'
    }
}
const deal = () => {
    uni.navigateTo({
        url:'/pages/rent/order'
    })
}
</script>

<template>
    <view class="content">
        <L-top-height></L-top-height>
        <image src="../../static/imgs/addDevice/bindSuccess.svg" class="imgResult"></image>
        <view class="msgContent">
            <text class="msgTitle">信息已确认</text>
        </view>
        <view class="btnBox">
            <u-button class="btn" type="primary" @click="deal()">查看订单</u-button>
        </view>
    </view>
</template>


<style lang="scss" scoped>
.content {
    //内容居中
    display: flex; /* 使用 Flexbox 布局 */
    align-items: center;
    flex-direction: column;
    height: 100vh;

    .textTitle {
        margin-top: 48rpx;
        color: var(--Text-main, #282A2E);
        font-family: "PingFang SC";
        font-size: 56rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 68rpx; /* 121.429% */
        letter-spacing: 0.364px;
    }

    .imgResult {
        margin-top: 128rpx;
        width: 256rpx;
        height: 256rpx;
    }

    .msgContent {
        padding: 32rpx;
        width: 686rpx;
        margin-top: 96rpx;
        flex-shrink: 0;
        border-radius: 16rpx;
        display: flex;
        justify-content: center;

        .msgTitle {
            color: #000;
            text-align: center;
            /* Regular/Headline */
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Headline, 34rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Headline, 44rpx); /* 129.412% */
        }

        .msgBox {
            margin-top: 24rpx;

            .msgText {
                color: #000;
                font-family: var(--Font-Family, "PingFang SC");
                font-size: var(--Font-Size-Caption1, 24rpx);
                font-style: normal;
                font-weight: 400;
                line-height: var(--Line-Height-Caption1, 48rpx); /* 133.333% */
                letter-spacing: var(--Letter-Spacing-Caption1, 0px);
            }
        }
    }

    .btnBox {
        position: absolute;
        bottom: 0;

        .btn {
            width: 686rpx;
            height: 96rpx;
            border-radius: 16rpx;
            margin-bottom: 32rpx;
        }
    }
}

</style>