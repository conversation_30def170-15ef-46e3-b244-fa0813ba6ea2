import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useCarStatusStore = defineStore(
    'carStatus',
    () => {
        // 车型信息
        const carStatus = ref<any>(
            [
                {
                    id: '1',
                    flag:true,
                    flagText:'',
                    info:{
                        app:true,
                        msg:true,
                        tel:true
                    }
                },
                {
                    id: '2',
                    flag:true,
                    flagText:'',
                    info:{
                        app:true,
                        msg:true,
                        tel:true,
                        meter:'500米'
                    }
                },
                {
                    id: '3',
                    flag:true,
                    flagText:'',
                    info:{
                        app:true,
                        msg:true,
                        tel:true,
                        battery:'20%'
                    }
                },
                {
                    id: '4',
                    flag:true,
                    flagText:'',
                    info:{
                        app:true,
                        msg:true,
                        tel:true
                    }
                },
                {
                    id: '5',
                    information:false,
                    flag:false,
                    flagText:'',
                    info:{
                        begin:'23:00',
                        end:'07:00',
                        carList:[]
                    }
                }
            ]
        )
        // 修改车型信息
        const setCarStatus = (val: any) => {
            carStatus.value = val
        }

        // 清理车型信息
        const clearCarStatus = () => {
            carStatus.value = {}
        }

        // 记得 return
        return { carStatus, setCarStatus, clearCarStatus }
    },
    {
        // 配置持久化
        persist: {
            // 调整为兼容多端的API
            storage: {
                setItem(key, value) {
                    uni.setStorageSync(key, value)
                },
                getItem(key) {
                    return uni.getStorageSync(key)
                },
            },
        },
    },
)