<template>
    <view class="mainBox">
        <view class="listBox">
            <view class="listItem borderTopRadius borderBottomRadius" >免打扰
                <u-switch v-model="mainInfo.flag"></u-switch>
            </view>
        </view>
        <view v-if="mainInfo.flag">
            <view class="desc">生效时间与车辆</view>
            <view class="listBox">
                <view class="listItem borderTopRadius borderBottomRadius" @click="toPage('/pages/index/effectTime')">时间段(每天)
                    <view class="nickname">{{ mainInfo.info.begin}} - {{ mainInfo.info.end }}</view>
                    <L-arrow></L-arrow>
                </view>
            </view>
            <view class="listBox">
                <view class="listItem borderTopRadius borderBottomRadius" @click="toPage('/pages/index/effectCar')">生效车辆
                    <L-arrow></L-arrow>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import {ref, watch} from 'vue'
const mainInfo = ref({})
import {useCarStatusStore} from '@/stores'
const carStatusStore = useCarStatusStore()
let list = carStatusStore.carStatus
const mainList = ref(list)
for(let i in list ){
    if(list[i].id==5){
        mainInfo.value = list[i]
    }
}
// 页面跳转函数
const toPage = (path: any) => {
    uni.navigateTo({
        url: path
    })
}
watch(()=>mainInfo.value,()=>{
    mainList.value[4] = mainInfo.value
    carStatusStore.setCarStatus(mainList.value)
},{
    deep:true
})
</script>

<style lang="scss" scoped>
.mainBox {
    width: 750rpx;
}
.desc{
    padding: 32rpx 32rpx 0;
    color: #000;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 64rpx;
}

.listBox {
    padding: 32rpx 32rpx 0;
    border-radius: 16rpx;
    .listItem {
        padding: 0 32rpx;
        background: #FFFFFF;
        height: 112rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--Text-main, #282A2E);
        font-feature-settings: 'case' on;
        font-family: var(--Font-Family, "PingFang SC");
        font-size: var(--Font-Size-Body, 30rpx);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Line-Height-Body, 44rpx); /* 129.412% */
        letter-spacing: var(--Letter-Spacing-Body, -0.41px);

        .nickname {
            flex: 1;
            text-align: right;
            margin-right: 10rpx;
            color: var(--Text-content, #53565C);
            font-family: var(--Font-Family, "PingFang SC");
            font-size: var(--Font-Size-Footnote, 26rpx);
            font-style: normal;
            font-weight: 400;
            line-height: var(--Line-Height-Footnote, 36rpx); /* 138.462% */
            letter-spacing: var(--Letter-Spacing-Footnote, -0.08px);
        }

    }
}


</style>
